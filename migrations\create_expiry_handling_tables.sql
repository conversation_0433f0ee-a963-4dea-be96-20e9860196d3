-- 创建过期食材处理相关表
-- 执行时间：2025-06-23

-- 1. 创建过期食材处理记录表
CREATE TABLE expiry_handlings (
    id INT IDENTITY(1,1) PRIMARY KEY,
    inventory_id INT NOT NULL,
    handling_type NVARCHAR(20) NOT NULL, -- 报废/退货/转移/其他
    handling_status NVARCHAR(20) NOT NULL DEFAULT '待处理', -- 待处理/处理中/已完成/已取消
    handling_date DATE NULL, -- 实际处理日期
    planned_date DATE NOT NULL, -- 计划处理日期
    operator_id INT NOT NULL,
    approver_id INT NULL,
    quantity FLOAT NOT NULL, -- 处理数量
    unit NVARCHAR(20) NOT NULL,
    unit_cost DECIMAL(10,2) NULL, -- 单位成本
    total_loss DECIMAL(15,2) NULL, -- 总损失金额
    reason NTEXT NULL, -- 处理原因
    notes NTEXT NULL, -- 备注
    -- 退货相关字段
    return_to_supplier BIT NOT NULL DEFAULT 0, -- 是否退回供应商
    supplier_response NTEXT NULL, -- 供应商回复
    compensation_amount DECIMAL(15,2) NULL, -- 赔偿金额
    -- 转移相关字段
    transfer_to NVARCHAR(100) NULL, -- 转移目标
    transfer_purpose NVARCHAR(100) NULL, -- 转移用途
    -- 审批相关
    approval_notes NTEXT NULL, -- 审批意见
    approved_at DATETIME2(1) NULL, -- 审批时间
    -- 财务相关
    financial_voucher_id INT NULL, -- 财务凭证ID
    is_financial_processed BIT NOT NULL DEFAULT 0, -- 是否财务处理
    area_id INT NULL, -- 学校级隔离
    created_at DATETIME2(1) NOT NULL DEFAULT GETDATE(),
    updated_at DATETIME2(1) NOT NULL DEFAULT GETDATE(),
    
    -- 外键约束
    CONSTRAINT FK_expiry_handlings_inventory FOREIGN KEY (inventory_id) REFERENCES inventories(id),
    CONSTRAINT FK_expiry_handlings_operator FOREIGN KEY (operator_id) REFERENCES users(id),
    CONSTRAINT FK_expiry_handlings_approver FOREIGN KEY (approver_id) REFERENCES users(id),
    CONSTRAINT FK_expiry_handlings_area FOREIGN KEY (area_id) REFERENCES administrative_areas(id)
);

-- 2. 创建过期通知记录表
CREATE TABLE expiry_notifications (
    id INT IDENTITY(1,1) PRIMARY KEY,
    inventory_id INT NOT NULL,
    notification_type NVARCHAR(20) NOT NULL, -- 临期预警/过期通知/处理提醒
    notification_level NVARCHAR(20) NOT NULL, -- 低/中/高/紧急
    days_to_expiry INT NULL, -- 距离过期天数
    message NTEXT NOT NULL, -- 通知消息
    recipient_id INT NOT NULL, -- 接收人
    is_read BIT NOT NULL DEFAULT 0, -- 是否已读
    is_handled BIT NOT NULL DEFAULT 0, -- 是否已处理
    read_at DATETIME2(1) NULL, -- 阅读时间
    handled_at DATETIME2(1) NULL, -- 处理时间
    handling_id INT NULL, -- 关联处理记录
    area_id INT NULL, -- 学校级隔离
    created_at DATETIME2(1) NOT NULL DEFAULT GETDATE(),
    updated_at DATETIME2(1) NOT NULL DEFAULT GETDATE(),
    
    -- 外键约束
    CONSTRAINT FK_expiry_notifications_inventory FOREIGN KEY (inventory_id) REFERENCES inventories(id),
    CONSTRAINT FK_expiry_notifications_recipient FOREIGN KEY (recipient_id) REFERENCES users(id),
    CONSTRAINT FK_expiry_notifications_handling FOREIGN KEY (handling_id) REFERENCES expiry_handlings(id),
    CONSTRAINT FK_expiry_notifications_area FOREIGN KEY (area_id) REFERENCES administrative_areas(id)
);

-- 3. 创建索引
CREATE INDEX IX_expiry_handlings_inventory_id ON expiry_handlings(inventory_id);
CREATE INDEX IX_expiry_handlings_operator_id ON expiry_handlings(operator_id);
CREATE INDEX IX_expiry_handlings_handling_status ON expiry_handlings(handling_status);
CREATE INDEX IX_expiry_handlings_planned_date ON expiry_handlings(planned_date);
CREATE INDEX IX_expiry_handlings_area_id ON expiry_handlings(area_id);

CREATE INDEX IX_expiry_notifications_inventory_id ON expiry_notifications(inventory_id);
CREATE INDEX IX_expiry_notifications_recipient_id ON expiry_notifications(recipient_id);
CREATE INDEX IX_expiry_notifications_is_read ON expiry_notifications(is_read);
CREATE INDEX IX_expiry_notifications_is_handled ON expiry_notifications(is_handled);
CREATE INDEX IX_expiry_notifications_area_id ON expiry_notifications(area_id);

-- 4. 创建触发器自动更新updated_at字段
CREATE TRIGGER TR_expiry_handlings_updated_at
ON expiry_handlings
AFTER UPDATE
AS
BEGIN
    SET NOCOUNT ON;
    UPDATE expiry_handlings 
    SET updated_at = GETDATE()
    FROM expiry_handlings eh
    INNER JOIN inserted i ON eh.id = i.id;
END;

CREATE TRIGGER TR_expiry_notifications_updated_at
ON expiry_notifications
AFTER UPDATE
AS
BEGIN
    SET NOCOUNT ON;
    UPDATE expiry_notifications 
    SET updated_at = GETDATE()
    FROM expiry_notifications en
    INNER JOIN inserted i ON en.id = i.id;
END;

-- 5. 插入初始数据（处理类型和状态的约束）
-- 可以考虑添加CHECK约束来限制字段值
ALTER TABLE expiry_handlings 
ADD CONSTRAINT CK_expiry_handlings_type 
CHECK (handling_type IN ('报废', '退货', '转移', '其他'));

ALTER TABLE expiry_handlings 
ADD CONSTRAINT CK_expiry_handlings_status 
CHECK (handling_status IN ('待处理', '处理中', '已完成', '已取消'));

ALTER TABLE expiry_notifications 
ADD CONSTRAINT CK_expiry_notifications_type 
CHECK (notification_type IN ('临期预警', '过期通知', '处理提醒'));

ALTER TABLE expiry_notifications 
ADD CONSTRAINT CK_expiry_notifications_level 
CHECK (notification_level IN ('低', '中', '高', '紧急'));

PRINT '过期食材处理相关表创建完成！';
