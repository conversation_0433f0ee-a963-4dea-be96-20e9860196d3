/**
 * 简化版主题切换器 - 去除过度效果
 * 只保留基本的主题切换功能
 */

class SimpleThemeSwitcher {
    constructor() {
        // 优先使用primary主题，确保导航栏显示为蓝色
        const savedTheme = localStorage.getItem('user-theme');
        this.currentTheme = savedTheme === 'success' ? 'primary' : (savedTheme || 'primary');
        this.init();
    }

    init() {
        console.log('简化版主题切换器加载完成');
        
        // 应用保存的主题
        this.applyTheme(this.currentTheme);
        
        // 更新活动状态
        this.updateActiveTheme();
        
        // 绑定事件
        this.bindEvents();
        
        console.log('当前主题:', this.currentTheme);
    }

    bindEvents() {
        // 主题选项点击事件
        document.addEventListener('click', (e) => {
            const themeOption = e.target.closest('.theme-option');
            
            if (themeOption) {
                e.preventDefault();
                const theme = themeOption.getAttribute('data-theme');
                this.switchTheme(theme);
            }
        });
    }

    applyTheme(theme) {
        // 简单应用主题，无动画
        document.documentElement.setAttribute('data-theme', theme);
        document.body.setAttribute('data-theme', theme);

        // 更新CSS变量
        this.updateThemeVariables(theme);

        // 触发自定义事件
        window.dispatchEvent(new CustomEvent('themeChanged', {
            detail: { theme }
        }));

        this.currentTheme = theme;
        localStorage.setItem('user-theme', theme);
        this.updateActiveTheme();

        console.log('主题切换完成:', theme);
    }

    updateThemeVariables(theme) {
        // 定义主题颜色映射
        const themeColors = {
            'primary': '#007bff',
            'secondary': '#6c757d', 
            'success': '#28a745',
            'warning': '#ffc107',
            'info': '#17a2b8',
            'danger': '#dc3545'
        };

        const color = themeColors[theme] || themeColors.primary;
        
        // 更新CSS变量
        document.documentElement.style.setProperty('--theme-primary', color);
        
        // 计算深色版本（简单的颜色变暗）
        const darkColor = this.darkenColor(color, 20);
        document.documentElement.style.setProperty('--theme-primary-dark', darkColor);
    }

    darkenColor(color, percent) {
        // 简单的颜色变暗函数
        const num = parseInt(color.replace("#", ""), 16);
        const amt = Math.round(2.55 * percent);
        const R = (num >> 16) - amt;
        const G = (num >> 8 & 0x00FF) - amt;
        const B = (num & 0x0000FF) - amt;
        return "#" + (0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 +
            (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 +
            (B < 255 ? B < 1 ? 0 : B : 255)).toString(16).slice(1);
    }

    switchTheme(theme) {
        this.applyTheme(theme);

        // 关闭下拉菜单
        const dropdown = document.querySelector('.theme-switcher-panel');
        if (dropdown && dropdown.classList.contains('show')) {
            dropdown.classList.remove('show');
        }

        // 简单的成功提示
        this.showNotification(`已切换到 ${this.getThemeName(theme)} 主题`);
    }

    updateActiveTheme() {
        document.querySelectorAll('.theme-option').forEach(option => {
            const theme = option.getAttribute('data-theme');
            if (theme === this.currentTheme) {
                option.classList.add('active');
            } else {
                option.classList.remove('active');
            }
        });
    }

    getThemeName(theme) {
        const themeNames = {
            'primary': '海洋蓝',
            'secondary': '现代灰',
            'success': '自然绿',
            'warning': '活力橙',
            'info': '优雅紫',
            'danger': '深邃红'
        };
        return themeNames[theme] || theme;
    }

    showNotification(message) {
        // 简单的通知，无复杂动画
        if (typeof toastr !== 'undefined') {
            toastr.success(message);
        } else {
            // 原生简单通知
            const notification = document.createElement('div');
            notification.textContent = message;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: var(--theme-primary, #007bff);
                color: white;
                padding: 10px 15px;
                border-radius: 4px;
                z-index: 9999;
                font-size: 14px;
                box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            `;

            document.body.appendChild(notification);
            setTimeout(() => {
                notification.remove();
            }, 2000);
        }
    }
}

// 初始化简化版主题切换器
let simpleThemeSwitcher;

document.addEventListener('DOMContentLoaded', function() {
    simpleThemeSwitcher = new SimpleThemeSwitcher();
    
    // 导出到全局作用域
    window.themeSwitcher = simpleThemeSwitcher;
    window.switchTheme = (theme) => simpleThemeSwitcher.switchTheme(theme);
});

// 全局测试函数
window.testTheme = function(theme) {
    if (window.themeSwitcher) {
        window.themeSwitcher.switchTheme(theme);
        console.log('主题已切换到:', theme);
    }
};
