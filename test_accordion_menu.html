<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>导航栏手风琴效果测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .sidebar {
            width: 200px;
            min-height: 100vh;
            background: #2c5aa0;
            color: white;
        }
        
        .sidebar-nav .nav-link {
            color: white;
            padding: 0.75rem 1rem;
            border: none;
            background: none;
            text-decoration: none;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .sidebar-nav .nav-link:hover {
            background: rgba(255, 255, 255, 0.1);
            color: white;
        }
        
        .sidebar-nav .nav-link.active {
            background: rgba(255, 255, 255, 0.2);
        }
        
        .sidebar-nav .collapse .nav-link {
            padding-left: 2rem;
            font-size: 0.9rem;
        }
        
        .fa-chevron-down {
            transition: transform 0.3s ease;
        }
        
        .main-content {
            flex: 1;
            padding: 2rem;
        }
    </style>
</head>
<body>
    <div class="d-flex">
        <!-- 左侧导航栏 -->
        <div class="sidebar">
            <div class="sidebar-nav">
                <!-- 首页菜单 -->
                <div class="nav-item">
                    <a class="nav-link" href="#" data-toggle="collapse" data-target="#homeSubmenu" aria-expanded="false">
                        <span><i class="fas fa-home"></i> 首页</span>
                        <i class="fas fa-chevron-down"></i>
                    </a>
                    <div class="collapse" id="homeSubmenu">
                        <a class="nav-link" href="#">仪表盘</a>
                        <a class="nav-link" href="#">食堂日常管理</a>
                    </div>
                </div>

                <!-- 周菜单管理 -->
                <div class="nav-item">
                    <a class="nav-link" href="#" data-toggle="collapse" data-target="#menuSubmenu" aria-expanded="false">
                        <span><i class="fas fa-calendar-alt"></i> 周菜单管理</span>
                        <i class="fas fa-chevron-down"></i>
                    </a>
                    <div class="collapse" id="menuSubmenu">
                        <a class="nav-link" href="#">周菜单计划</a>
                        <a class="nav-link" href="#">周菜单列表</a>
                        <a class="nav-link" href="#">食谱管理</a>
                        <a class="nav-link" href="#">菜单同步工具</a>
                    </div>
                </div>

                <!-- 采购管理 -->
                <div class="nav-item">
                    <a class="nav-link" href="#" data-toggle="collapse" data-target="#purchaseSubmenu" aria-expanded="false">
                        <span><i class="fas fa-shopping-cart"></i> 采购管理</span>
                        <i class="fas fa-chevron-down"></i>
                    </a>
                    <div class="collapse" id="purchaseSubmenu">
                        <a class="nav-link" href="#">采购订单列表</a>
                        <a class="nav-link" href="#">从周菜单创建采购订单</a>
                    </div>
                </div>

                <!-- 库存管理 -->
                <div class="nav-item">
                    <a class="nav-link" href="#" data-toggle="collapse" data-target="#inventorySubmenu" aria-expanded="false">
                        <span><i class="fas fa-warehouse"></i> 库存管理</span>
                        <i class="fas fa-chevron-down"></i>
                    </a>
                    <div class="collapse" id="inventorySubmenu">
                        <a class="nav-link" href="#">库存列表</a>
                        <a class="nav-link" href="#">入库管理</a>
                        <a class="nav-link" href="#">消耗计划管理</a>
                        <a class="nav-link" href="#">出库管理</a>
                        <a class="nav-link" href="#">仓库管理</a>
                    </div>
                </div>

                <!-- 系统管理 -->
                <div class="nav-item">
                    <a class="nav-link" href="#" data-toggle="collapse" data-target="#systemSubmenu" aria-expanded="false">
                        <span><i class="fas fa-cogs"></i> 系统管理</span>
                        <i class="fas fa-chevron-down"></i>
                    </a>
                    <div class="collapse" id="systemSubmenu">
                        <a class="nav-link" href="#">管理仪表盘</a>
                        <a class="nav-link" href="#">用户管理</a>
                        <a class="nav-link" href="#">角色管理</a>
                        <a class="nav-link" href="#">系统设置</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- 右侧内容区域 -->
        <div class="main-content">
            <h1>导航栏手风琴效果测试</h1>
            <div class="alert alert-info">
                <h4>测试说明：</h4>
                <ul>
                    <li>点击左侧任意菜单项，观察是否只有当前菜单展开</li>
                    <li>当展开新菜单时，其他菜单应该自动关闭</li>
                    <li>箭头图标应该正确旋转</li>
                    <li>这就是手风琴效果</li>
                </ul>
            </div>
            
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">功能特点</h5>
                    <ul>
                        <li><strong>手风琴效果</strong>：同时只能展开一个菜单</li>
                        <li><strong>平滑动画</strong>：菜单展开/收起有平滑过渡</li>
                        <li><strong>图标旋转</strong>：箭头图标随菜单状态旋转</li>
                        <li><strong>用户友好</strong>：保持界面整洁，避免菜单过长</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        $(document).ready(function() {
            // 侧边栏子菜单切换 - 手风琴效果
            $('.sidebar-nav [data-toggle="collapse"]').on('click', function(e) {
                e.preventDefault();
                const target = $(this).attr('data-target');
                const $target = $(target);
                const $icon = $(this).find('.fa-chevron-down');
                const isCurrentlyShown = $target.hasClass('show');

                // 如果当前菜单是关闭状态，先关闭所有其他菜单
                if (!isCurrentlyShown) {
                    // 关闭所有其他展开的菜单
                    $('.sidebar-nav .collapse.show').each(function() {
                        if (this !== $target[0]) {
                            $(this).collapse('hide');
                            // 重置对应的箭头图标
                            const $otherIcon = $(this).prev().find('.fa-chevron-down');
                            $otherIcon.css('transform', 'rotate(0deg)');
                        }
                    });
                }

                // 切换当前子菜单
                $target.collapse('toggle');

                // 旋转箭头图标
                $target.on('show.bs.collapse', function() {
                    $icon.css('transform', 'rotate(180deg)');
                });

                $target.on('hide.bs.collapse', function() {
                    $icon.css('transform', 'rotate(0deg)');
                });
            });
        });
    </script>
</body>
</html>
