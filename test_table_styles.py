#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
表格样式统一测试脚本
验证统一表格样式是否正确应用到所有模块
"""

import requests
import time
from urllib.parse import urljoin

def test_table_styles():
    """测试各个页面的表格样式"""
    
    base_url = "http://xiaoyuanst.com"
    
    # 测试页面列表
    test_pages = [
        {
            "name": "员工管理页面",
            "url": "/employee/",
            "description": "基准页面，使用标准Bootstrap表格样式"
        },
        {
            "name": "供应商管理页面", 
            "url": "/supplier/",
            "description": "使用enterprise-table类"
        },
        {
            "name": "食材管理页面",
            "url": "/ingredient/",
            "description": "使用enterprise-table类"
        },
        {
            "name": "留样管理页面",
            "url": "/food-sample/",
            "description": "使用table table-bordered table-striped类"
        },
        {
            "name": "财务凭证页面",
            "url": "/financial/vouchers/",
            "description": "使用uf-table类（用友风格）"
        },
        {
            "name": "应付账款页面",
            "url": "/financial/payables/",
            "description": "使用uf-table类"
        },
        {
            "name": "日常管理检查记录",
            "url": "/daily-management/",
            "description": "使用inspection-table类"
        }
    ]
    
    print("🔍 表格样式统一测试报告")
    print("=" * 60)
    print(f"测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"基准URL: {base_url}")
    print()
    
    results = []
    
    for page in test_pages:
        print(f"📄 测试页面: {page['name']}")
        print(f"   URL: {page['url']}")
        print(f"   描述: {page['description']}")
        
        try:
            url = urljoin(base_url, page['url'])
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                content = response.text
                
                # 检查是否包含统一表格样式文件
                has_unified_styles = 'unified-table-styles.css' in content
                
                # 检查表格相关的类名
                table_classes = []
                if 'class="table' in content:
                    table_classes.append('table (Bootstrap)')
                if 'class="enterprise-table' in content:
                    table_classes.append('enterprise-table')
                if 'class="uf-table' in content:
                    table_classes.append('uf-table')
                if 'class="inspection-table' in content:
                    table_classes.append('inspection-table')
                
                # 检查表头样式
                has_thead = '<thead>' in content
                has_th = '<th' in content
                
                result = {
                    'page': page['name'],
                    'url': page['url'],
                    'status': 'SUCCESS',
                    'has_unified_styles': has_unified_styles,
                    'table_classes': table_classes,
                    'has_thead': has_thead,
                    'has_th': has_th,
                    'response_time': response.elapsed.total_seconds()
                }
                
                print(f"   ✅ 状态: 正常访问")
                print(f"   📊 统一样式: {'已加载' if has_unified_styles else '❌ 未加载'}")
                print(f"   🏷️  表格类名: {', '.join(table_classes) if table_classes else '无'}")
                print(f"   📋 表头结构: {'有' if has_thead and has_th else '无'}")
                print(f"   ⏱️  响应时间: {response.elapsed.total_seconds():.2f}s")
                
            else:
                result = {
                    'page': page['name'],
                    'url': page['url'],
                    'status': 'ERROR',
                    'error': f'HTTP {response.status_code}'
                }
                print(f"   ❌ 状态: HTTP {response.status_code}")
                
        except requests.exceptions.RequestException as e:
            result = {
                'page': page['name'],
                'url': page['url'],
                'status': 'ERROR',
                'error': str(e)
            }
            print(f"   ❌ 状态: 连接错误 - {str(e)}")
        
        results.append(result)
        print()
    
    # 生成总结报告
    print("📊 测试总结")
    print("-" * 40)
    
    total_pages = len(results)
    success_pages = len([r for r in results if r['status'] == 'SUCCESS'])
    pages_with_unified_styles = len([r for r in results if r.get('has_unified_styles', False)])
    
    print(f"总测试页面: {total_pages}")
    print(f"成功访问: {success_pages}")
    print(f"已加载统一样式: {pages_with_unified_styles}")
    print(f"样式覆盖率: {(pages_with_unified_styles/success_pages*100):.1f}%" if success_pages > 0 else "0%")
    
    # 表格类名统计
    all_table_classes = []
    for result in results:
        if result['status'] == 'SUCCESS':
            all_table_classes.extend(result.get('table_classes', []))
    
    if all_table_classes:
        print("\n🏷️ 发现的表格类名:")
        class_counts = {}
        for cls in all_table_classes:
            class_counts[cls] = class_counts.get(cls, 0) + 1
        
        for cls, count in class_counts.items():
            print(f"   - {cls}: {count}个页面")
    
    # 建议
    print("\n💡 优化建议:")
    if pages_with_unified_styles < success_pages:
        print("   - 确保所有页面都加载了unified-table-styles.css")
    
    if len(set(all_table_classes)) > 1:
        print("   - 考虑统一表格类名，减少样式冲突")
    
    print("   - 定期检查表格样式的一致性")
    print("   - 确保新页面遵循统一的表格设计规范")
    
    return results

if __name__ == "__main__":
    test_table_styles()
