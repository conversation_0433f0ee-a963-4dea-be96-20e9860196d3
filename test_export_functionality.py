#!/usr/bin/env python3
"""
测试导出功能脚本
验证新添加的导出功能是否正常工作
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.services.export_service import UnifiedExportService, export_suppliers_excel, export_inventory_excel
from datetime import datetime, timedelta
import tempfile

def test_unified_export_service():
    """测试统一导出服务"""
    print("🧪 测试统一导出服务...")
    
    try:
        # 创建导出服务实例
        export_service = UnifiedExportService()
        
        # 检查模板是否正确加载
        assert 'supplier_list' in export_service.templates
        assert 'inventory_list' in export_service.templates
        assert 'daily_inspection' in export_service.templates
        
        print("✅ 统一导出服务初始化成功")
        print(f"📋 已加载模板数量: {len(export_service.templates)}")
        
        # 测试模板结构
        supplier_template = export_service.templates['supplier_list']
        assert 'headers' in supplier_template
        assert 'columns' in supplier_template
        assert 'title' in supplier_template
        
        print("✅ 模板结构验证通过")
        
        return True
        
    except Exception as e:
        print(f"❌ 统一导出服务测试失败: {str(e)}")
        return False

def test_export_templates():
    """测试导出模板"""
    print("\n🧪 测试导出模板...")
    
    try:
        export_service = UnifiedExportService()
        
        # 测试所有模板
        templates_to_test = [
            'supplier_list',
            'inventory_list', 
            'daily_inspection',
            'companion_dining',
            'training_records',
            'employee_list'
        ]
        
        for template_name in templates_to_test:
            template = export_service.templates.get(template_name)
            if template:
                print(f"✅ 模板 '{template_name}' 存在")
                print(f"   - 标题: {template['title']}")
                print(f"   - 字段数: {len(template['headers'])}")
            else:
                print(f"❌ 模板 '{template_name}' 不存在")
                
        return True
        
    except Exception as e:
        print(f"❌ 导出模板测试失败: {str(e)}")
        return False

def test_data_formatting():
    """测试数据格式化功能"""
    print("\n🧪 测试数据格式化...")
    
    try:
        export_service = UnifiedExportService()
        
        # 测试数据
        test_data = [
            {
                'name': '测试供应商',
                'contact_person': '张三',
                'phone': '13800138000',
                'address': '北京市朝阳区',
                'email': '<EMAIL>',
                'status': '启用',
                'created_at': '2024-01-15'
            }
        ]
        
        # 测试 _get_nested_value 方法
        value = export_service._get_nested_value(test_data[0], 'name')
        assert value == '测试供应商'
        
        value = export_service._get_nested_value(test_data[0], 'nonexistent')
        assert value == ""
        
        print("✅ 数据格式化功能正常")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据格式化测试失败: {str(e)}")
        return False

def test_export_routes():
    """测试导出路由是否正确注册"""
    print("\n🧪 测试导出路由...")
    
    try:
        app = create_app()
        
        with app.app_context():
            # 检查路由是否存在
            routes_to_check = [
                '/supplier/export',
                '/supplier/export/statistics',
                '/inventory/export',
                '/inventory/export/expiry_report',
                '/daily-management/export/<export_type>'
            ]
            
            # 获取所有路由
            all_routes = []
            for rule in app.url_map.iter_rules():
                all_routes.append(rule.rule)
            
            missing_routes = []
            for route in routes_to_check:
                if route not in all_routes:
                    missing_routes.append(route)
                else:
                    print(f"✅ 路由存在: {route}")
            
            if missing_routes:
                print(f"❌ 缺失路由: {missing_routes}")
                return False
            else:
                print("✅ 所有导出路由都已正确注册")
                return True
                
    except Exception as e:
        print(f"❌ 导出路由测试失败: {str(e)}")
        return False

def test_file_creation():
    """测试文件创建功能"""
    print("\n🧪 测试文件创建...")
    
    try:
        # 检查导出目录是否可以创建
        app = create_app()
        
        with app.app_context():
            export_service = UnifiedExportService()
            
            # 检查目录是否创建成功
            for dir_name, dir_path in export_service.export_dirs.items():
                if os.path.exists(dir_path):
                    print(f"✅ 目录存在: {dir_name} -> {dir_path}")
                else:
                    print(f"❌ 目录不存在: {dir_name} -> {dir_path}")
                    return False
            
            print("✅ 所有导出目录都已创建")
            return True
            
    except Exception as e:
        print(f"❌ 文件创建测试失败: {str(e)}")
        return False

def run_all_tests():
    """运行所有测试"""
    print("🚀 开始测试StudentsCMSSP导出功能...")
    print("=" * 60)
    
    tests = [
        test_unified_export_service,
        test_export_templates,
        test_data_formatting,
        test_export_routes,
        test_file_creation
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ 测试异常: {str(e)}")
            failed += 1
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: 通过 {passed} 个，失败 {failed} 个")
    
    if failed == 0:
        print("🎉 所有测试通过！导出功能已成功实施")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关功能")
        return False

def print_implementation_summary():
    """打印实施总结"""
    print("\n" + "=" * 60)
    print("📋 StudentsCMSSP导出功能实施总结")
    print("=" * 60)
    
    print("\n✅ 已完成的功能:")
    print("1. 创建统一导出服务 (app/services/export_service.py)")
    print("2. 供应商管理导出功能")
    print("   - 供应商信息Excel导出")
    print("   - 供应商采购统计报表")
    print("3. 库存管理导出功能")
    print("   - 库存清单Excel导出")
    print("   - 过期预警报表导出")
    print("4. 日常管理导出功能")
    print("   - 检查记录导出")
    print("   - 陪餐记录导出")
    print("   - 培训记录导出")
    print("   - 问题记录导出")
    print("5. 前端界面改进")
    print("   - 统一的导出按钮设计")
    print("   - 导出进度提示")
    print("   - 筛选条件设置")
    
    print("\n📁 涉及的文件:")
    print("- app/services/export_service.py (新建)")
    print("- app/routes/supplier.py (修改)")
    print("- app/routes/inventory.py (修改)")
    print("- app/routes/daily_management/routes.py (修改)")
    print("- app/templates/supplier/index.html (修改)")
    print("- app/templates/inventory/index.html (修改)")
    print("- app/templates/daily_management/simplified_dashboard.html (修改)")
    
    print("\n🎯 使用方法:")
    print("1. 访问相应模块页面")
    print("2. 点击'导出'按钮")
    print("3. 选择导出类型")
    print("4. 设置筛选条件（如有）")
    print("5. 确认导出")
    
    print("\n🔧 技术特点:")
    print("- 统一的导出服务架构")
    print("- 标准化的Excel格式")
    print("- 完善的错误处理")
    print("- 用户友好的界面")
    print("- 支持筛选和日期范围")

if __name__ == '__main__':
    # 运行测试
    success = run_all_tests()
    
    # 打印实施总结
    print_implementation_summary()
    
    if success:
        print("\n🎉 导出功能实施完成！")
        print("💡 建议: 在生产环境中测试各个导出功能，确保数据准确性")
    else:
        print("\n⚠️  请修复测试中发现的问题后再部署")
    
    sys.exit(0 if success else 1)
