"""
专业优雅的打印和导出模板生成器
为StudentsCMSSP项目提供统一、专业、美观的打印和导出模板
专为黑白打印优化，注重排版美观和专业性
"""

from flask import current_app
from reportlab.lib.pagesizes import A4, landscape
from reportlab.lib import colors
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, PageBreak, KeepTogether
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import cm, mm, inch
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.graphics.shapes import Drawing, Line, Rect
from reportlab.graphics import renderPDF
from openpyxl import Workbook
from openpyxl.styles import Font, Alignment, Border, Side, PatternFill, NamedStyle
from openpyxl.utils import get_column_letter
from openpyxl.worksheet.page import PageMargins
import os
from datetime import datetime, date
from decimal import Decimal
import tempfile
from typing import List, Dict, Any, Optional

class ProfessionalTemplateGenerator:
    """专业模板生成器"""
    
    def __init__(self):
        self.register_fonts()
        self.setup_pdf_styles()
        self.setup_excel_styles()
    
    def register_fonts(self):
        """注册专业字体"""
        try:
            # 注册宋体字体
            font_path = os.path.join(current_app.root_path, 'static', 'fonts', 'simsun.ttf')
            if os.path.exists(font_path):
                pdfmetrics.registerFont(TTFont('SimSun', font_path))
                pdfmetrics.registerFont(TTFont('SimSun-Bold', font_path))
                current_app.logger.info("专业字体注册成功")
            else:
                current_app.logger.warning("字体文件未找到，使用默认字体")
        except Exception as e:
            current_app.logger.error(f"字体注册失败: {str(e)}")
    
    def setup_pdf_styles(self):
        """设置PDF专业样式"""
        self.pdf_styles = {
            # 主标题样式 - 大标题，居中，加粗
            'main_title': ParagraphStyle(
                'MainTitle',
                fontName='SimSun-Bold',
                fontSize=20,
                leading=24,
                alignment=1,  # 居中
                spaceAfter=15,
                spaceBefore=10
            ),
            
            # 副标题样式 - 中等大小，居中
            'subtitle': ParagraphStyle(
                'Subtitle',
                fontName='SimSun',
                fontSize=16,
                leading=20,
                alignment=1,
                spaceAfter=12,
                spaceBefore=8
            ),
            
            # 章节标题样式 - 左对齐，加粗
            'section_title': ParagraphStyle(
                'SectionTitle',
                fontName='SimSun-Bold',
                fontSize=14,
                leading=18,
                alignment=0,  # 左对齐
                spaceAfter=8,
                spaceBefore=12
            ),
            
            # 正文样式 - 标准正文
            'body_text': ParagraphStyle(
                'BodyText',
                fontName='SimSun',
                fontSize=11,
                leading=14,
                alignment=0,
                spaceAfter=6
            ),
            
            # 表格标题样式
            'table_header': ParagraphStyle(
                'TableHeader',
                fontName='SimSun-Bold',
                fontSize=10,
                leading=12,
                alignment=1
            ),
            
            # 表格内容样式
            'table_content': ParagraphStyle(
                'TableContent',
                fontName='SimSun',
                fontSize=9,
                leading=11,
                alignment=0
            ),
            
            # 数字样式 - 右对齐
            'number_style': ParagraphStyle(
                'NumberStyle',
                fontName='SimSun',
                fontSize=9,
                leading=11,
                alignment=2  # 右对齐
            ),
            
            # 小字样式 - 备注等
            'small_text': ParagraphStyle(
                'SmallText',
                fontName='SimSun',
                fontSize=8,
                leading=10,
                alignment=0
            ),
            
            # 签名样式
            'signature': ParagraphStyle(
                'Signature',
                fontName='SimSun',
                fontSize=10,
                leading=12,
                alignment=0
            )
        }
    
    def setup_excel_styles(self):
        """设置Excel专业样式"""
        # 主标题样式
        self.excel_title_style = NamedStyle(name="title_style")
        self.excel_title_style.font = Font(name='宋体', size=16, bold=True)
        self.excel_title_style.alignment = Alignment(horizontal='center', vertical='center')
        
        # 表头样式
        self.excel_header_style = NamedStyle(name="header_style")
        self.excel_header_style.font = Font(name='宋体', size=11, bold=True)
        self.excel_header_style.alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
        self.excel_header_style.fill = PatternFill(start_color='E6E6E6', end_color='E6E6E6', fill_type='solid')
        self.excel_header_style.border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
        
        # 数据样式
        self.excel_data_style = NamedStyle(name="data_style")
        self.excel_data_style.font = Font(name='宋体', size=10)
        self.excel_data_style.alignment = Alignment(horizontal='left', vertical='center', wrap_text=True)
        self.excel_data_style.border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
        
        # 数字样式
        self.excel_number_style = NamedStyle(name="number_style")
        self.excel_number_style.font = Font(name='宋体', size=10)
        self.excel_number_style.alignment = Alignment(horizontal='right', vertical='center')
        self.excel_number_style.border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
        self.excel_number_style.number_format = '#,##0.00'
        
        # 合计样式
        self.excel_total_style = NamedStyle(name="total_style")
        self.excel_total_style.font = Font(name='宋体', size=11, bold=True)
        self.excel_total_style.alignment = Alignment(horizontal='right', vertical='center')
        self.excel_total_style.fill = PatternFill(start_color='F0F0F0', end_color='F0F0F0', fill_type='solid')
        self.excel_total_style.border = Border(
            left=Side(style='medium'),
            right=Side(style='medium'),
            top=Side(style='medium'),
            bottom=Side(style='medium')
        )
    
    def create_pdf_header(self, content, title, subtitle=None, company_name=None, report_date=None):
        """创建PDF专业页眉"""
        # 主标题
        content.append(Paragraph(title, self.pdf_styles['main_title']))
        
        # 公司名称
        if company_name:
            content.append(Paragraph(company_name, self.pdf_styles['subtitle']))
        
        # 副标题
        if subtitle:
            content.append(Paragraph(subtitle, self.pdf_styles['subtitle']))
        
        # 报表日期
        if report_date:
            date_str = report_date.strftime('%Y年%m月%d日') if isinstance(report_date, (date, datetime)) else str(report_date)
            content.append(Paragraph(f"报表日期：{date_str}", self.pdf_styles['body_text']))
        
        content.append(Spacer(1, 10*mm))
        
        # 添加分隔线
        line_drawing = Drawing(180*mm, 1*mm)
        line_drawing.add(Line(0, 0, 180*mm, 0, strokeWidth=0.5, strokeColor=colors.black))
        content.append(line_drawing)
        content.append(Spacer(1, 5*mm))
    
    def create_pdf_footer(self, content, signatures=None, notes=None):
        """创建PDF专业页脚"""
        content.append(Spacer(1, 10*mm))
        
        # 添加分隔线
        line_drawing = Drawing(180*mm, 1*mm)
        line_drawing.add(Line(0, 180*mm, 0, strokeWidth=0.5, strokeColor=colors.black))
        content.append(line_drawing)
        content.append(Spacer(1, 5*mm))
        
        # 签名栏
        if signatures:
            signature_data = []
            signature_row = []
            col_widths = []
            
            for sig in signatures:
                signature_row.extend([
                    Paragraph(f"{sig['label']}:", self.pdf_styles['signature']),
                    Paragraph("", self.pdf_styles['signature'])  # 空白签名区域
                ])
                col_widths.extend([25*mm, 35*mm])
            
            signature_data.append(signature_row)
            
            signature_table = Table(signature_data, colWidths=col_widths)
            signature_table.setStyle(TableStyle([
                ('FONTNAME', (0, 0), (-1, -1), 'SimSun'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ('LINEBELOW', (1, 0), (-1, 0), 0.5, colors.black, None, None, 2, 2),  # 签名线
            ]))
            
            content.append(signature_table)
        
        # 备注
        if notes:
            content.append(Spacer(1, 8*mm))
            content.append(Paragraph("备注：", self.pdf_styles['section_title']))
            content.append(Paragraph(notes, self.pdf_styles['small_text']))
        
        # 打印时间
        print_time = datetime.now().strftime('%Y年%m月%d日 %H:%M')
        content.append(Spacer(1, 5*mm))
        content.append(Paragraph(f"打印时间：{print_time}", self.pdf_styles['small_text']))
    
    def create_professional_table(self, data, headers, col_widths=None, has_total_row=False):
        """创建专业表格"""
        # 准备表格数据
        table_data = [headers] + data
        
        # 设置列宽
        if not col_widths:
            col_count = len(headers)
            available_width = 180*mm  # A4页面可用宽度
            col_widths = [available_width / col_count] * col_count
        
        # 创建表格
        table = Table(table_data, colWidths=col_widths, repeatRows=1)
        
        # 设置表格样式
        table_style = [
            # 字体设置
            ('FONTNAME', (0, 0), (-1, -1), 'SimSun'),
            ('FONTSIZE', (0, 0), (-1, -1), 9),
            
            # 表头样式
            ('FONTNAME', (0, 0), (-1, 0), 'SimSun-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 10),
            ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
            ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
            
            # 边框设置
            ('GRID', (0, 0), (-1, -1), 0.5, colors.black),
            ('LINEBELOW', (0, 0), (-1, 0), 1, colors.black),
            
            # 对齐设置
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('ALIGN', (0, 1), (-1, -1), 'LEFT'),
            
            # 内边距
            ('LEFTPADDING', (0, 0), (-1, -1), 3),
            ('RIGHTPADDING', (0, 0), (-1, -1), 3),
            ('TOPPADDING', (0, 0), (-1, -1), 3),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 3),
        ]
        
        # 如果有合计行，特殊处理
        if has_total_row:
            table_style.extend([
                ('FONTNAME', (0, -1), (-1, -1), 'SimSun-Bold'),
                ('FONTSIZE', (0, -1), (-1, -1), 10),
                ('BACKGROUND', (0, -1), (-1, -1), colors.lightgrey),
                ('LINEABOVE', (0, -1), (-1, -1), 1, colors.black),
            ])
        
        table.setStyle(TableStyle(table_style))
        return table
    
    def create_info_table(self, info_data, col_widths=None):
        """创建信息表格（用于显示基本信息）"""
        if not col_widths:
            col_widths = [30*mm, 45*mm, 30*mm, 45*mm]
        
        info_table = Table(info_data, colWidths=col_widths)
        info_table.setStyle(TableStyle([
            ('FONTNAME', (0, 0), (-1, -1), 'SimSun'),
            ('FONTSIZE', (0, 0), (-1, -1), 11),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('GRID', (0, 0), (-1, -1), 0.5, colors.grey),
            ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),  # 第一列背景
            ('BACKGROUND', (2, 0), (2, -1), colors.lightgrey),  # 第三列背景
            ('LEFTPADDING', (0, 0), (-1, -1), 5),
            ('RIGHTPADDING', (0, 0), (-1, -1), 5),
            ('TOPPADDING', (0, 0), (-1, -1), 4),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 4),
        ]))
        
        return info_table

    def create_professional_excel(self, data, headers, title, subtitle=None, company_name=None,
                                 report_date=None, filename=None):
        """创建专业Excel文档"""
        # 创建工作簿
        wb = Workbook()
        ws = wb.active
        ws.title = title

        # 设置页面属性
        ws.page_setup.paperSize = ws.PAPERSIZE_A4
        ws.page_setup.orientation = ws.ORIENTATION_PORTRAIT
        ws.page_margins = PageMargins(left=0.7, right=0.7, top=0.75, bottom=0.75)

        # 注册样式
        if 'title_style' not in wb.named_styles:
            wb.add_named_style(self.excel_title_style)
        if 'header_style' not in wb.named_styles:
            wb.add_named_style(self.excel_header_style)
        if 'data_style' not in wb.named_styles:
            wb.add_named_style(self.excel_data_style)
        if 'number_style' not in wb.named_styles:
            wb.add_named_style(self.excel_number_style)
        if 'total_style' not in wb.named_styles:
            wb.add_named_style(self.excel_total_style)

        current_row = 1

        # 主标题
        if title:
            ws.merge_cells(f'A{current_row}:{get_column_letter(len(headers))}{current_row}')
            title_cell = ws[f'A{current_row}']
            title_cell.value = title
            title_cell.style = 'title_style'
            current_row += 2

        # 公司名称
        if company_name:
            ws.merge_cells(f'A{current_row}:{get_column_letter(len(headers))}{current_row}')
            company_cell = ws[f'A{current_row}']
            company_cell.value = company_name
            company_cell.font = Font(name='宋体', size=14)
            company_cell.alignment = Alignment(horizontal='center')
            current_row += 1

        # 副标题
        if subtitle:
            ws.merge_cells(f'A{current_row}:{get_column_letter(len(headers))}{current_row}')
            subtitle_cell = ws[f'A{current_row}']
            subtitle_cell.value = subtitle
            subtitle_cell.font = Font(name='宋体', size=12)
            subtitle_cell.alignment = Alignment(horizontal='center')
            current_row += 1

        # 报表日期
        if report_date:
            date_str = report_date.strftime('%Y年%m月%d日') if isinstance(report_date, (date, datetime)) else str(report_date)
            ws.merge_cells(f'A{current_row}:{get_column_letter(len(headers))}{current_row}')
            date_cell = ws[f'A{current_row}']
            date_cell.value = f"报表日期：{date_str}"
            date_cell.font = Font(name='宋体', size=10)
            date_cell.alignment = Alignment(horizontal='center')
            current_row += 2

        # 表头
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=current_row, column=col, value=header)
            cell.style = 'header_style'

        current_row += 1

        # 数据行
        for row_data in data:
            for col, value in enumerate(row_data, 1):
                cell = ws.cell(row=current_row, column=col, value=value)
                # 根据数据类型选择样式
                if isinstance(value, (int, float, Decimal)) and col > 1:
                    cell.style = 'number_style'
                else:
                    cell.style = 'data_style'
            current_row += 1

        # 自动调整列宽
        for column in ws.columns:
            max_length = 0
            column_letter = get_column_letter(column[0].column)

            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass

            # 设置合适的列宽
            adjusted_width = min(max_length + 2, 50)
            if adjusted_width < 8:
                adjusted_width = 8
            ws.column_dimensions[column_letter].width = adjusted_width

        # 设置打印选项
        ws.print_options.horizontalCentered = True
        ws.print_options.verticalCentered = False

        # 生成文件名
        if not filename:
            timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
            filename = f"{title}_{timestamp}.xlsx"

        # 创建临时文件
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.xlsx')
        temp_file.close()

        # 保存文件
        wb.save(temp_file.name)

        return temp_file.name, filename

    def format_currency(self, amount):
        """格式化货币"""
        if amount is None:
            return "0.00"
        return f"{float(amount):,.2f}"

    def format_date(self, date_obj):
        """格式化日期"""
        if date_obj is None:
            return ""
        if isinstance(date_obj, str):
            return date_obj
        return date_obj.strftime('%Y-%m-%d')

    def format_datetime(self, datetime_obj):
        """格式化日期时间"""
        if datetime_obj is None:
            return ""
        if isinstance(datetime_obj, str):
            return datetime_obj
        return datetime_obj.strftime('%Y-%m-%d %H:%M')

    def create_pdf_dir(self, subdir='reports'):
        """创建PDF保存目录"""
        try:
            pdf_dir = os.path.join(current_app.root_path, 'static', 'pdf', subdir)
            if not os.path.exists(pdf_dir):
                os.makedirs(pdf_dir)
            return pdf_dir
        except Exception as e:
            current_app.logger.error(f"创建PDF目录失败: {str(e)}")
            raise
