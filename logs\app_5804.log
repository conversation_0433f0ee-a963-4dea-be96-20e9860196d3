2025-06-22 22:51:38,739 INFO: 应用启动 - PID: 5804 [in C:\StudentsCMSSP\app\__init__.py:868]
2025-06-22 22:53:06,365 ERROR: [安全监控] 2025-06-22 22:53:06 - 频率限制触发 | IP: 127.0.0.1 | IP 127.0.0.1 在1分钟内请求 60 次 [in C:\StudentsCMSSP\app\security_monitor.py:126]
2025-06-22 22:53:06,368 WARNING: SECURITY EVENT - RATE_LIMIT: Rate limit exceeded from 127.0.0.1 [in C:\StudentsCMSSP\app\security_config.py:155]
2025-06-22 22:53:06,373 WARNING: [安全监控] 2025-06-22 22:53:06 - 阻止访问 | IP: 127.0.0.1 | IP 127.0.0.1 尝试访问 /static/js/theme-switcher-simple.js [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-22 22:53:06,374 WARNING: [安全监控] 2025-06-22 22:53:06 - 阻止访问 | IP: 127.0.0.1 | IP 127.0.0.1 尝试访问 /static/js/auth-helper.js [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-22 22:53:06,374 WARNING: SECURITY EVENT - RATE_LIMIT: Rate limit exceeded from 127.0.0.1 [in C:\StudentsCMSSP\app\security_config.py:155]
2025-06-22 22:53:06,375 WARNING: SECURITY EVENT - RATE_LIMIT: Rate limit exceeded from 127.0.0.1 [in C:\StudentsCMSSP\app\security_config.py:155]
2025-06-22 22:53:06,492 WARNING: [安全监控] 2025-06-22 22:53:06 - 阻止访问 | IP: 127.0.0.1 | IP 127.0.0.1 尝试访问 /static/js/enhanced-image-uploader.js [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-22 22:53:06,492 WARNING: SECURITY EVENT - RATE_LIMIT: Rate limit exceeded from 127.0.0.1 [in C:\StudentsCMSSP\app\security_config.py:155]
2025-06-22 22:53:06,532 WARNING: [安全监控] 2025-06-22 22:53:06 - 阻止访问 | IP: 127.0.0.1 | IP 127.0.0.1 尝试访问 /static/js/file-upload-fix.js [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-22 22:53:06,532 WARNING: SECURITY EVENT - RATE_LIMIT: Rate limit exceeded from 127.0.0.1 [in C:\StudentsCMSSP\app\security_config.py:155]
2025-06-22 22:53:06,654 WARNING: [安全监控] 2025-06-22 22:53:06 - 阻止访问 | IP: 127.0.0.1 | IP 127.0.0.1 尝试访问 /static/js/mobile-table-cards.js [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-22 22:53:06,655 WARNING: SECURITY EVENT - RATE_LIMIT: Rate limit exceeded from 127.0.0.1 [in C:\StudentsCMSSP\app\security_config.py:155]
2025-06-22 22:53:06,680 WARNING: [安全监控] 2025-06-22 22:53:06 - 阻止访问 | IP: 127.0.0.1 | IP 127.0.0.1 尝试访问 /static/js/mobile-enhancements.js [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-22 22:53:06,681 WARNING: SECURITY EVENT - RATE_LIMIT: Rate limit exceeded from 127.0.0.1 [in C:\StudentsCMSSP\app\security_config.py:155]
2025-06-22 22:53:06,690 WARNING: [安全监控] 2025-06-22 22:53:06 - 阻止访问 | IP: 127.0.0.1 | IP 127.0.0.1 尝试访问 /static/js/process_navigation.js [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-22 22:53:06,690 WARNING: [安全监控] 2025-06-22 22:53:06 - 阻止访问 | IP: 127.0.0.1 | IP 127.0.0.1 尝试访问 /static/js/user_guide.js [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-22 22:53:06,691 WARNING: SECURITY EVENT - RATE_LIMIT: Rate limit exceeded from 127.0.0.1 [in C:\StudentsCMSSP\app\security_config.py:155]
2025-06-22 22:53:06,691 WARNING: SECURITY EVENT - RATE_LIMIT: Rate limit exceeded from 127.0.0.1 [in C:\StudentsCMSSP\app\security_config.py:155]
2025-06-22 22:53:07,018 WARNING: [安全监控] 2025-06-22 22:53:07 - 阻止访问 | IP: 127.0.0.1 | IP 127.0.0.1 尝试访问 /api/v2/dashboard/today-menu [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-22 22:53:07,020 WARNING: SECURITY EVENT - RATE_LIMIT: Rate limit exceeded from 127.0.0.1 [in C:\StudentsCMSSP\app\security_config.py:155]
2025-06-22 22:53:07,020 WARNING: [安全监控] 2025-06-22 22:53:07 - 阻止访问 | IP: 127.0.0.1 | IP 127.0.0.1 尝试访问 /api/v2/dashboard/todo-items [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-22 22:53:07,020 WARNING: [安全监控] 2025-06-22 22:53:07 - 阻止访问 | IP: 127.0.0.1 | IP 127.0.0.1 尝试访问 /api/v2/dashboard/notifications [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-22 22:53:07,021 WARNING: [安全监控] 2025-06-22 22:53:07 - 阻止访问 | IP: 127.0.0.1 | IP 127.0.0.1 尝试访问 /api/v2/dashboard/supply-chain-status [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-22 22:53:07,022 WARNING: SECURITY EVENT - RATE_LIMIT: Rate limit exceeded from 127.0.0.1 [in C:\StudentsCMSSP\app\security_config.py:155]
2025-06-22 22:53:07,022 WARNING: SECURITY EVENT - RATE_LIMIT: Rate limit exceeded from 127.0.0.1 [in C:\StudentsCMSSP\app\security_config.py:155]
2025-06-22 22:53:07,023 WARNING: SECURITY EVENT - RATE_LIMIT: Rate limit exceeded from 127.0.0.1 [in C:\StudentsCMSSP\app\security_config.py:155]
2025-06-22 22:53:41,678 ERROR: Exception on /daily-management/ [GET] [in C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py:838]
Traceback (most recent call last):
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 1473, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 882, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_cors\extension.py", line 194, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 880, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 865, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
  File "C:\StudentsCMSSP\app\routes\daily_management\routes.py", line 102, in index
    return render_template('daily_management/simplified_dashboard.html',
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 1301, in render
    self.environment.handle_exception()
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 936, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "C:\StudentsCMSSP\app\templates\daily_management\simplified_dashboard.html", line 5, in top-level template code
    {% from 'daily_management/components/qrcode_card_widget.html' import qrcode_card_widget %}
  File "C:\StudentsCMSSP\app\templates\base.html", line 481, in top-level template code
    {% block content %}{% endblock %}
  File "C:\StudentsCMSSP\app\templates\daily_management\simplified_dashboard.html", line 312, in block 'content'
    value="{{ (today - timedelta(days=30)).strftime('%Y-%m-%d') }}">
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\utils.py", line 83, in from_obj
    if hasattr(obj, "jinja_pass_arg"):
jinja2.exceptions.UndefinedError: 'timedelta' is undefined
2025-06-22 22:54:41,846 INFO: Successfully registered SimSun font [in C:\StudentsCMSSP\app\utils\financial_pdf_generator.py:37]
2025-06-22 23:06:51,287 WARNING: [安全监控] 2025-06-22 23:06:51 - 可疑请求 | IP: *************:43094 | 路径: /.git/refs/tags/, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-22 23:08:43,349 ERROR: Exception on /financial/ledgers/detail [GET] [in C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py:838]
Traceback (most recent call last):
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 1473, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 882, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_cors\extension.py", line 194, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 880, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 865, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
  File "C:\StudentsCMSSP\app\utils\school_required.py", line 37, in decorated_function
    return f(*args, **kwargs)
  File "C:\StudentsCMSSP\app\utils\permissions.py", line 582, in decorated_function
    return f(*args, **kwargs)
  File "C:\StudentsCMSSP\app\routes\financial\ledgers.py", line 70, in detail_ledger
    return render_template('financial/ledgers/detail.html',
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 1301, in render
    self.environment.handle_exception()
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 936, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "C:\StudentsCMSSP\app\templates\financial\ledgers\detail.html", line 1, in top-level template code
    {% extends "financial/base.html" %}
  File "C:\StudentsCMSSP\app\templates\financial\base.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "C:\StudentsCMSSP\app\templates\base.html", line 481, in top-level template code
    {% block content %}{% endblock %}
  File "C:\StudentsCMSSP\app\templates\financial\base.html", line 131, in block 'content'
    {{ self.financial_content() }}
  File "C:\StudentsCMSSP\app\templates\financial\ledgers\detail.html", line 364, in block 'financial_content'
    {{ "{:,.2f}"|format(record.balance) }}
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\filters.py", line 1016, in do_format
    return soft_str(value) % (kwargs or args)
TypeError: not all arguments converted during string formatting
2025-06-22 23:08:55,461 ERROR: Exception on /financial/ledgers/detail [GET] [in C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py:838]
Traceback (most recent call last):
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 1473, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 882, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_cors\extension.py", line 194, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 880, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 865, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
  File "C:\StudentsCMSSP\app\utils\school_required.py", line 37, in decorated_function
    return f(*args, **kwargs)
  File "C:\StudentsCMSSP\app\utils\permissions.py", line 582, in decorated_function
    return f(*args, **kwargs)
  File "C:\StudentsCMSSP\app\routes\financial\ledgers.py", line 70, in detail_ledger
    return render_template('financial/ledgers/detail.html',
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 1301, in render
    self.environment.handle_exception()
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 936, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "C:\StudentsCMSSP\app\templates\financial\ledgers\detail.html", line 1, in top-level template code
    {% extends "financial/base.html" %}
  File "C:\StudentsCMSSP\app\templates\financial\base.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "C:\StudentsCMSSP\app\templates\base.html", line 481, in top-level template code
    {% block content %}{% endblock %}
  File "C:\StudentsCMSSP\app\templates\financial\base.html", line 131, in block 'content'
    {{ self.financial_content() }}
  File "C:\StudentsCMSSP\app\templates\financial\ledgers\detail.html", line 364, in block 'financial_content'
    {{ "{:,.2f}"|format(record.balance) }}
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\filters.py", line 1016, in do_format
    return soft_str(value) % (kwargs or args)
TypeError: not all arguments converted during string formatting
2025-06-22 23:09:43,745 ERROR: Exception on /financial/ledgers/detail [GET] [in C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py:838]
Traceback (most recent call last):
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 1473, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 882, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_cors\extension.py", line 194, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 880, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 865, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
  File "C:\StudentsCMSSP\app\utils\school_required.py", line 37, in decorated_function
    return f(*args, **kwargs)
  File "C:\StudentsCMSSP\app\utils\permissions.py", line 582, in decorated_function
    return f(*args, **kwargs)
  File "C:\StudentsCMSSP\app\routes\financial\ledgers.py", line 70, in detail_ledger
    return render_template('financial/ledgers/detail.html',
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 1301, in render
    self.environment.handle_exception()
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 936, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "C:\StudentsCMSSP\app\templates\financial\ledgers\detail.html", line 1, in top-level template code
    {% extends "financial/base.html" %}
  File "C:\StudentsCMSSP\app\templates\financial\base.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "C:\StudentsCMSSP\app\templates\base.html", line 481, in top-level template code
    {% block content %}{% endblock %}
  File "C:\StudentsCMSSP\app\templates\financial\base.html", line 131, in block 'content'
    {{ self.financial_content() }}
  File "C:\StudentsCMSSP\app\templates\financial\ledgers\detail.html", line 364, in block 'financial_content'
    {{ "{:,.2f}"|format(record.balance) }}
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\filters.py", line 1016, in do_format
    return soft_str(value) % (kwargs or args)
TypeError: not all arguments converted during string formatting
