/* 左右式布局 - 使用系统主题 */

/* 左侧导航栏 - 200px固定宽度 */
.sidebar {
    width: 200px !important;
    min-width: 200px !important;
    background: var(--theme-primary);
    transition: background-color 0.3s ease;
}

.sidebar-header {
    background: var(--theme-primary);
    transition: background 0.3s ease;
}

.sidebar-nav .nav-link:hover {
    background: var(--theme-primary-dark);
}

.sidebar-nav .nav-link.active {
    background: var(--theme-primary-dark);
}

/* 右侧顶部工具栏 */
.top-toolbar {
    background: var(--theme-primary) !important;
    transition: background-color 0.3s ease;
}

/* 移动端响应式 */
@media (max-width: 768px) {
    .sidebar.show::before {
        content: '';
        position: fixed;
        top: 0;
        left: 200px;
        right: 0;
        bottom: 0;
        background: rgba(0,0,0,0.5);
        z-index: -1;
    }
}
