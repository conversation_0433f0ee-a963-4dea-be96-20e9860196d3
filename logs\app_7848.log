2025-06-22 23:15:08,948 INFO: 应用启动 - PID: 7848 [in C:\StudentsCMSSP\app\__init__.py:868]
2025-06-22 23:17:33,225 INFO: Successfully registered SimSun font [in C:\StudentsCMSSP\app\utils\financial_pdf_generator.py:37]
2025-06-22 23:18:50,271 INFO: 查询菜谱：日期=2025-06-22, 星期=6(0=周一), day_of_week=7, 餐次=午餐, 区域ID=44 [in C:\StudentsCMSSP\app\routes\food_trace.py:324]
2025-06-22 23:18:50,287 INFO: 找到 1 个周菜单 [in C:\StudentsCMSSP\app\routes\food_trace.py:334]
2025-06-22 23:18:50,297 INFO: 匹配条件的食谱有 4 个 [in C:\StudentsCMSSP\app\routes\food_trace.py:346]
2025-06-22 23:18:50,316 INFO:   - 食谱: 红薯米饭（海淀区中关村第一小学版） [in C:\StudentsCMSSP\app\routes\food_trace.py:361]
2025-06-22 23:18:50,321 INFO:   - 食谱: 韭菜炒豆芽（海淀区中关村第一小学版） [in C:\StudentsCMSSP\app\routes\food_trace.py:361]
2025-06-22 23:18:50,327 INFO:   - 食谱: 蒸蛋羹（海淀区中关村第一小学版） [in C:\StudentsCMSSP\app\routes\food_trace.py:361]
2025-06-22 23:18:50,330 INFO:   - 食谱: 熟鸡蛋 [in C:\StudentsCMSSP\app\routes\food_trace.py:361]
2025-06-22 23:18:50,338 INFO: 食材一致性分析完成: 匹配率=0.0%, 缺失=6, 多余=0 [in C:\StudentsCMSSP\app\routes\food_trace.py:531]
2025-06-22 23:30:12,662 INFO: 生成固定二维码 - 用户: guest_demo, 学校: 海淀区中关村第一小学 (ID: 44) [in C:\StudentsCMSSP\app\routes\daily_management\inspection_qrcode.py:31]
2025-06-22 23:30:12,663 INFO: 员工上传URL: http://xiaoyuanst.com/daily-management/public/inspections/select-date/44/upload [in C:\StudentsCMSSP\app\routes\daily_management\inspection_qrcode.py:38]
2025-06-22 23:30:12,944 INFO: 成功生成二维码base64，数据长度: 1236 [in C:\StudentsCMSSP\app\utils\qrcode_helper.py:78]
2025-06-22 23:30:12,944 INFO: 管理员评分URL: http://xiaoyuanst.com/daily-management/public/inspections/select-date/44/rate [in C:\StudentsCMSSP\app\routes\daily_management\inspection_qrcode.py:46]
2025-06-22 23:30:12,960 INFO: 成功生成二维码base64，数据长度: 1084 [in C:\StudentsCMSSP\app\utils\qrcode_helper.py:78]
2025-06-22 23:30:48,429 INFO: 查询菜谱：日期=2025-06-22, 星期=6(0=周一), day_of_week=7, 餐次=午餐, 区域ID=44 [in C:\StudentsCMSSP\app\routes\food_trace.py:324]
2025-06-22 23:30:48,445 INFO: 找到 1 个周菜单 [in C:\StudentsCMSSP\app\routes\food_trace.py:334]
2025-06-22 23:30:48,451 INFO: 匹配条件的食谱有 4 个 [in C:\StudentsCMSSP\app\routes\food_trace.py:346]
2025-06-22 23:30:48,463 INFO:   - 食谱: 红薯米饭（海淀区中关村第一小学版） [in C:\StudentsCMSSP\app\routes\food_trace.py:361]
2025-06-22 23:30:48,468 INFO:   - 食谱: 韭菜炒豆芽（海淀区中关村第一小学版） [in C:\StudentsCMSSP\app\routes\food_trace.py:361]
2025-06-22 23:30:48,475 INFO:   - 食谱: 蒸蛋羹（海淀区中关村第一小学版） [in C:\StudentsCMSSP\app\routes\food_trace.py:361]
2025-06-22 23:30:48,478 INFO:   - 食谱: 熟鸡蛋 [in C:\StudentsCMSSP\app\routes\food_trace.py:361]
2025-06-22 23:30:48,483 INFO: 食材一致性分析完成: 匹配率=0.0%, 缺失=6, 多余=0 [in C:\StudentsCMSSP\app\routes\food_trace.py:531]
2025-06-22 23:31:39,424 ERROR: Exception on /daily-management/ [GET] [in C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py:838]
Traceback (most recent call last):
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 1473, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 882, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_cors\extension.py", line 194, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 880, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 865, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
  File "C:\StudentsCMSSP\app\routes\daily_management\routes.py", line 102, in index
    return render_template('daily_management/simplified_dashboard.html',
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 1301, in render
    self.environment.handle_exception()
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 936, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "C:\StudentsCMSSP\app\templates\daily_management\simplified_dashboard.html", line 5, in top-level template code
    {% from 'daily_management/components/qrcode_card_widget.html' import qrcode_card_widget %}
  File "C:\StudentsCMSSP\app\templates\base.html", line 481, in top-level template code
    {% block content %}{% endblock %}
  File "C:\StudentsCMSSP\app\templates\daily_management\simplified_dashboard.html", line 312, in block 'content'
    value="{{ (today - timedelta(days=30)).strftime('%Y-%m-%d') }}">
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\utils.py", line 83, in from_obj
    if hasattr(obj, "jinja_pass_arg"):
jinja2.exceptions.UndefinedError: 'timedelta' is undefined
2025-06-22 23:33:00,137 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\StudentsCMSSP\app\routes\inventory.py:151]
2025-06-22 23:33:12,788 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\StudentsCMSSP\app\routes\inventory.py:151]
2025-06-22 23:33:19,437 INFO: 查询菜谱：日期=2025-06-22, 星期=6(0=周一), day_of_week=7, 餐次=午餐, 区域ID=44 [in C:\StudentsCMSSP\app\routes\food_trace.py:324]
2025-06-22 23:33:19,441 INFO: 找到 1 个周菜单 [in C:\StudentsCMSSP\app\routes\food_trace.py:334]
2025-06-22 23:33:19,444 INFO: 匹配条件的食谱有 4 个 [in C:\StudentsCMSSP\app\routes\food_trace.py:346]
2025-06-22 23:33:19,447 INFO:   - 食谱: 红薯米饭（海淀区中关村第一小学版） [in C:\StudentsCMSSP\app\routes\food_trace.py:361]
2025-06-22 23:33:19,451 INFO:   - 食谱: 韭菜炒豆芽（海淀区中关村第一小学版） [in C:\StudentsCMSSP\app\routes\food_trace.py:361]
2025-06-22 23:33:19,458 INFO:   - 食谱: 蒸蛋羹（海淀区中关村第一小学版） [in C:\StudentsCMSSP\app\routes\food_trace.py:361]
2025-06-22 23:33:19,461 INFO:   - 食谱: 熟鸡蛋 [in C:\StudentsCMSSP\app\routes\food_trace.py:361]
2025-06-22 23:33:19,466 INFO: 食材一致性分析完成: 匹配率=0.0%, 缺失=6, 多余=0 [in C:\StudentsCMSSP\app\routes\food_trace.py:531]
2025-06-22 23:50:24,992 INFO: 收到创建周菜单请求: b'{"area_id":"1","week_start":"2025-06-16"}' [in C:\StudentsCMSSP\app\routes\api_weekly_menu.py:76]
2025-06-22 23:50:24,992 INFO: 创建周菜单参数: area_id=1, week_start=2025-06-16 [in C:\StudentsCMSSP\app\routes\api_weekly_menu.py:87]
2025-06-22 23:50:24,992 INFO: 检查用户权限: user_id=1, area_id=1 [in C:\StudentsCMSSP\app\routes\api_weekly_menu.py:98]
2025-06-22 23:50:24,992 INFO: 用户角色: ['管理员', '超级管理员', '系统管理员', '食堂管理员'] [in C:\StudentsCMSSP\app\routes\api_weekly_menu.py:99]
2025-06-22 23:50:24,992 INFO: 权限检查结果: is_admin=1, is_school_admin=0, can_access_area=1, has_edit_permission=1 [in C:\StudentsCMSSP\app\routes\api_weekly_menu.py:106]
2025-06-22 23:50:24,992 INFO: 开始创建周菜单: area_id=1, week_start=2025-06-16, created_by=1 [in C:\StudentsCMSSP\app\routes\api_weekly_menu.py:123]
2025-06-22 23:50:25,007 INFO: 开始创建周菜单: area_id=1, week_start=2025-06-16, created_by=1 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:184]
2025-06-22 23:50:25,007 INFO: 转换后的日期对象: 2025-06-16, 类型=<class 'datetime.date'> [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:190]
2025-06-22 23:50:25,008 INFO: 计算的周结束日期: 2025-06-22 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:202]
2025-06-22 23:50:25,008 INFO: 检查是否已存在该周的菜单: area_id=1, week_start=2025-06-16 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:205]
2025-06-22 23:50:25,008 INFO: 获取周菜单: area_id=1, week_start=2025-06-16, 类型=<class 'datetime.date'> [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:42]
2025-06-22 23:50:25,008 INFO: 使用优化后的查询: area_id=1, week_start=2025-06-16 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:65]
2025-06-22 23:50:25,008 INFO: 执行主SQL查询: area_id=1, week_start_str=2025-06-16 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:77]
2025-06-22 23:50:25,008 INFO: 主查询未找到菜单: area_id=1, week_start=2025-06-16 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:102]
2025-06-22 23:50:25,008 INFO: 使用日期字符串: week_start_str=2025-06-16, week_end_str=2025-06-22 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:214]
2025-06-22 23:50:25,008 INFO: 准备执行SQL创建菜单 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:217]
2025-06-22 23:50:25,008 INFO: SQL参数: {'area_id': '1', 'week_start_str': '2025-06-16', 'week_end_str': '2025-06-22', 'status': '计划中', 'created_by': 1} [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:232]
2025-06-22 23:50:25,014 INFO: 执行SQL: 
            INSERT INTO weekly_menus (area_id, week_start, week_end, status, created_by)
            OUTPUT inserted.id
            VALUES (:area_id, CONVERT(DATETIME2(0), :week_start_str), CONVERT(DATETIME2(0), :week_end_str), :status, :created_by)
             [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:236]
2025-06-22 23:50:25,019 INFO: SQL执行成功，获取到ID: 45 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:243]
2025-06-22 23:50:25,020 INFO: 检查数据库连接状态 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:248]
2025-06-22 23:50:25,021 INFO: 数据库连接正常 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:251]
2025-06-22 23:50:25,022 INFO: 事务提交成功 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:258]
2025-06-22 23:50:25,022 INFO: 菜单缓存已清理 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:20]
2025-06-22 23:50:25,026 INFO: 验证成功: 菜单已创建 ID=45 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:266]
2025-06-22 23:50:25,026 INFO: 周菜单创建成功: id=45 [in C:\StudentsCMSSP\app\routes\api_weekly_menu.py:125]
2025-06-22 23:50:25,027 INFO: 返回创建周菜单响应: {'success': True, 'message': '周菜单创建成功', 'weekly_menu_id': 45, 'status': '计划中'} [in C:\StudentsCMSSP\app\routes\api_weekly_menu.py:142]
2025-06-23 00:07:56,917 WARNING: [安全监控] 2025-06-23 00:07:56 - 可疑请求 | IP: ************:43492 | 路径: /.env, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 00:09:44,152 ERROR: 批量导出失败: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')
[SQL: SELECT financial_vouchers.id AS financial_vouchers_id, financial_vouchers.voucher_number AS financial_vouchers_voucher_number, financial_vouchers.voucher_date AS financial_vouchers_voucher_date, financial_vouchers.area_id AS financial_vouchers_area_id, financial_vouchers.voucher_type AS financial_vouchers_voucher_type, financial_vouchers.summary AS financial_vouchers_summary, financial_vouchers.total_amount AS financial_vouchers_total_amount, financial_vouchers.status AS financial_vouchers_status, financial_vouchers.source_type AS financial_vouchers_source_type, financial_vouchers.source_id AS financial_vouchers_source_id, financial_vouchers.attachment_count AS financial_vouchers_attachment_count, financial_vouchers.created_by AS financial_vouchers_created_by, financial_vouchers.reviewed_by AS financial_vouchers_reviewed_by, financial_vouchers.reviewed_at AS financial_vouchers_reviewed_at, financial_vouchers.posted_by AS financial_vouchers_posted_by, financial_vouchers.posted_at AS financial_vouchers_posted_at, financial_vouchers.notes AS financial_vouchers_notes, financial_vouchers.created_at AS financial_vouchers_created_at, financial_vouchers.updated_at AS financial_vouchers_updated_at, users_1.id AS users_1_id, users_1.username AS users_1_username, users_1.password_hash AS users_1_password_hash, users_1.email AS users_1_email, users_1.real_name AS users_1_real_name, users_1.phone AS users_1_phone, users_1.avatar AS users_1_avatar, users_1.last_login AS users_1_last_login, users_1.status AS users_1_status, users_1.area_id AS users_1_area_id, users_1.area_level AS users_1_area_level, users_1.created_at AS users_1_created_at, users_2.id AS users_2_id, users_2.username AS users_2_username, users_2.password_hash AS users_2_password_hash, users_2.email AS users_2_email, users_2.real_name AS users_2_real_name, users_2.phone AS users_2_phone, users_2.avatar AS users_2_avatar, users_2.last_login AS users_2_last_login, users_2.status AS users_2_status, users_2.area_id AS users_2_area_id, users_2.area_level AS users_2_area_level, users_2.created_at AS users_2_created_at, users_3.id AS users_3_id, users_3.username AS users_3_username, users_3.password_hash AS users_3_password_hash, users_3.email AS users_3_email, users_3.real_name AS users_3_real_name, users_3.phone AS users_3_phone, users_3.avatar AS users_3_avatar, users_3.last_login AS users_3_last_login, users_3.status AS users_3_status, users_3.area_id AS users_3_area_id, users_3.area_level AS users_3_area_level, users_3.created_at AS users_3_created_at 
FROM financial_vouchers LEFT OUTER JOIN users AS users_1 ON users_1.id = financial_vouchers.created_by LEFT OUTER JOIN users AS users_2 ON users_2.id = financial_vouchers.reviewed_by LEFT OUTER JOIN users AS users_3 ON users_3.id = financial_vouchers.posted_by 
WHERE financial_vouchers.id IN (?) AND financial_vouchers.area_id = ?]
[parameters: ('54', 44)]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\StudentsCMSSP\app\routes\financial\vouchers.py:3250]
2025-06-23 00:37:45,026 WARNING: [安全监控] 2025-06-23 00:37:45 - 可疑请求 | IP: **************:38440 | 路径: /, 指标: 无User-Agent [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 00:40:27,254 WARNING: [安全监控] 2025-06-23 00:40:27 - 可疑请求 | IP: *************:59848 | 路径: /, 指标: 无User-Agent [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 00:57:56,813 WARNING: [安全监控] 2025-06-23 00:57:56 - 可疑请求 | IP: ************** | 路径: /, 指标: 无User-Agent [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 01:22:02,176 WARNING: [安全监控] 2025-06-23 01:22:02 - 可疑请求 | IP: ************** | 路径: /, 指标: 无User-Agent [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 01:23:52,843 WARNING: [安全监控] 2025-06-23 01:23:52 - 可疑请求 | IP: ************** | 路径: /, 指标: 无User-Agent [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 01:23:53,696 WARNING: [安全监控] 2025-06-23 01:23:53 - 可疑请求 | IP: ************** | 路径: /phpmyadmin/index.php, 指标: 无User-Agent, 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 01:23:54,446 WARNING: [安全监控] 2025-06-23 01:23:54 - 可疑请求 | IP: ************** | 路径: /phpMyAdmin/index.php, 指标: 无User-Agent, 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 02:05:51,576 WARNING: [安全监控] 2025-06-23 02:05:51 - 可疑请求 | IP: *************:59296 | 路径: /production/.env, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 02:45:47,847 WARNING: [安全监控] 2025-06-23 02:45:47 - 可疑请求 | IP: ************* | 路径: /, 指标: 可疑机器人User-Agent [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 02:49:42,248 WARNING: [安全监控] 2025-06-23 02:49:42 - 可疑请求 | IP: **************:40734 | 路径: /, 指标: 无User-Agent [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 02:52:03,945 WARNING: [安全监控] 2025-06-23 02:52:03 - 可疑请求 | IP: **************:52362 | 路径: /, 指标: 无User-Agent [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 02:52:05,304 WARNING: [安全监控] 2025-06-23 02:52:05 - 可疑请求 | IP: **************:52376 | 路径: /phpmyadmin/index.php, 指标: 无User-Agent, 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 02:52:12,337 WARNING: [安全监控] 2025-06-23 02:52:12 - 可疑请求 | IP: **************:35468 | 路径: /phpMyAdmin/index.php, 指标: 无User-Agent, 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 03:02:33,244 WARNING: [安全监控] 2025-06-23 03:02:33 - 可疑请求 | IP: ************* | 路径: /, 指标: 无User-Agent [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 03:10:48,445 WARNING: [安全监控] 2025-06-23 03:10:48 - 可疑请求 | IP: **************:51466 | 路径: /wp-admin/setup-config.php, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 03:13:24,482 WARNING: [安全监控] 2025-06-23 03:13:24 - 可疑请求 | IP: **************:51840 | 路径: /wordpress/wp-admin/setup-config.php, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 03:36:45,507 WARNING: [安全监控] 2025-06-23 03:36:45 - 可疑请求 | IP: **************:52802 | 路径: /, 指标: 无User-Agent [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 03:47:53,451 WARNING: [安全监控] 2025-06-23 03:47:53 - 可疑请求 | IP: *************:50660 | 路径: /.env, 指标: 可疑机器人User-Agent, 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 03:47:54,199 WARNING: [安全监控] 2025-06-23 03:47:54 - 可疑请求 | IP: *************:50672 | 路径: /.git/config, 指标: 可疑机器人User-Agent, 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 04:01:48,655 WARNING: [安全监控] 2025-06-23 04:01:48 - 可疑请求 | IP: *************:37799 | 路径: /setup.cgi, 指标: 无User-Agent [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 04:06:01,523 WARNING: [安全监控] 2025-06-23 04:06:01 - 可疑请求 | IP: ************* | 路径: /, 指标: 无User-Agent [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 04:08:50,466 WARNING: [安全监控] 2025-06-23 04:08:50 - 可疑请求 | IP: **************:49418 | 路径: /wp-admin/setup-config.php, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 04:09:19,612 WARNING: [安全监控] 2025-06-23 04:09:19 - 可疑请求 | IP: ************** | 路径: /example.com:443, 指标: 可疑HTTP方法 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 04:09:20,687 WARNING: [安全监控] 2025-06-23 04:09:20 - 可疑请求 | IP: ************** | 路径: /example.com:443, 指标: 可疑HTTP方法 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 04:10:47,122 WARNING: [安全监控] 2025-06-23 04:10:47 - 可疑请求 | IP: **************:26142 | 路径: /wordpress/wp-admin/setup-config.php, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 04:19:52,283 WARNING: [安全监控] 2025-06-23 04:19:52 - 可疑请求 | IP: ************** | 路径: /, 指标: 可疑机器人User-Agent [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 04:47:24,794 WARNING: [安全监控] 2025-06-23 04:47:24 - 可疑请求 | IP: ************:54132 | 路径: /, 指标: 无User-Agent [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 04:47:26,223 WARNING: [安全监控] 2025-06-23 04:47:26 - 可疑请求 | IP: ************:34592 | 路径: /.git/config, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 05:09:38,170 WARNING: [安全监控] 2025-06-23 05:09:38 - 可疑请求 | IP: ************:23645 | 路径: /.env, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 05:09:40,419 WARNING: [安全监控] 2025-06-23 05:09:40 - 可疑请求 | IP: ************:29527 | 路径: /.env, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 05:09:45,609 WARNING: [安全监控] 2025-06-23 05:09:45 - 可疑请求 | IP: ************:21047 | 路径: /.env.save, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 05:09:52,802 WARNING: [安全监控] 2025-06-23 05:09:52 - 可疑请求 | IP: ************:57671 | 路径: /.env.save, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 05:09:55,363 WARNING: [安全监控] 2025-06-23 05:09:55 - 可疑请求 | IP: ************:13287 | 路径: /.env.old, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 05:09:57,183 WARNING: [安全监控] 2025-06-23 05:09:57 - 可疑请求 | IP: ************:19809 | 路径: /.env.old, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 05:10:00,514 WARNING: [安全监控] 2025-06-23 05:10:00 - 可疑请求 | IP: ************:54627 | 路径: /.env.prod, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 05:10:03,668 WARNING: [安全监控] 2025-06-23 05:10:03 - 可疑请求 | IP: ************:42129 | 路径: /.env.prod, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 05:10:05,398 WARNING: [安全监控] 2025-06-23 05:10:05 - 可疑请求 | IP: *************:51093 | 路径: /.env.production, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 05:10:08,669 WARNING: [安全监控] 2025-06-23 05:10:08 - 可疑请求 | IP: ************:33077 | 路径: /.env.production, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 05:10:11,043 WARNING: [安全监控] 2025-06-23 05:10:11 - 可疑请求 | IP: *************:12711 | 路径: /.env.development , 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 05:10:22,115 WARNING: [安全监控] 2025-06-23 05:10:22 - 可疑请求 | IP: ************:35001 | 路径: /.env.development , 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 05:10:27,490 WARNING: [安全监控] 2025-06-23 05:10:27 - 可疑请求 | IP: ***********:46835 | 路径: /laravel/.env , 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 05:10:30,636 WARNING: [安全监控] 2025-06-23 05:10:30 - 可疑请求 | IP: *************:6103 | 路径: /laravel/.env , 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 05:10:34,780 WARNING: [安全监控] 2025-06-23 05:10:34 - 可疑请求 | IP: ************:37317 | 路径: /admin-app/.env , 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 05:10:38,673 WARNING: [安全监控] 2025-06-23 05:10:38 - 可疑请求 | IP: *************:57645 | 路径: /admin-app/.env , 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 05:10:42,046 WARNING: [安全监控] 2025-06-23 05:10:42 - 可疑请求 | IP: *************:25453 | 路径: /api/.env, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 05:10:43,791 WARNING: [安全监控] 2025-06-23 05:10:43 - 可疑请求 | IP: ************:2953 | 路径: /api/.env, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 05:10:45,353 WARNING: [安全监控] 2025-06-23 05:10:45 - 可疑请求 | IP: ************:32695 | 路径: /app/.env , 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 05:10:51,004 WARNING: [安全监控] 2025-06-23 05:10:51 - 可疑请求 | IP: ************:35353 | 路径: /app/.env , 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 05:10:54,336 WARNING: [安全监控] 2025-06-23 05:10:54 - 可疑请求 | IP: ************:16639 | 路径: /development/.env , 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 05:10:57,921 WARNING: [安全监控] 2025-06-23 05:10:57 - 可疑请求 | IP: *************:6463 | 路径: /development/.env , 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 05:10:59,604 WARNING: [安全监控] 2025-06-23 05:10:59 - 可疑请求 | IP: ************:14455 | 路径: /apps/.env , 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 05:11:02,199 WARNING: [安全监控] 2025-06-23 05:11:02 - 可疑请求 | IP: ************:25585 | 路径: /apps/.env , 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 05:11:05,275 WARNING: [安全监控] 2025-06-23 05:11:05 - 可疑请求 | IP: ***********:36333 | 路径: /cp/.env, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 05:11:08,575 WARNING: [安全监控] 2025-06-23 05:11:08 - 可疑请求 | IP: ************:54947 | 路径: /cp/.env, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 05:11:10,180 WARNING: [安全监控] 2025-06-23 05:11:10 - 可疑请求 | IP: ************:35371 | 路径: /private/.env, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 05:11:11,573 WARNING: [安全监控] 2025-06-23 05:11:11 - 可疑请求 | IP: ***********45:61567 | 路径: /private/.env, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 05:11:14,280 WARNING: [安全监控] 2025-06-23 05:11:14 - 可疑请求 | IP: ************:55959 | 路径: /system/.env, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 05:11:23,401 WARNING: [安全监控] 2025-06-23 05:11:23 - 可疑请求 | IP: ***********:13309 | 路径: /system/.env, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 05:11:24,916 WARNING: [安全监控] 2025-06-23 05:11:24 - 可疑请求 | IP: ************:52721 | 路径: /redmine/.env, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 05:11:27,480 WARNING: [安全监控] 2025-06-23 05:11:27 - 可疑请求 | IP: ************:60055 | 路径: /redmine/.env, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 05:11:33,443 WARNING: [安全监控] 2025-06-23 05:11:33 - 可疑请求 | IP: *************:21547 | 路径: /docker/.env, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 05:11:35,970 WARNING: [安全监控] 2025-06-23 05:11:35 - 可疑请求 | IP: *************:11175 | 路径: /docker/.env, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 05:11:37,567 WARNING: [安全监控] 2025-06-23 05:11:37 - 可疑请求 | IP: ***********:29307 | 路径: /cms/.env, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 05:11:44,992 WARNING: [安全监控] 2025-06-23 05:11:44 - 可疑请求 | IP: ***********:12801 | 路径: /script/.env, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 05:11:47,744 WARNING: [安全监控] 2025-06-23 05:11:47 - 可疑请求 | IP: ************:38433 | 路径: /script/.env, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 05:11:54,477 WARNING: [安全监控] 2025-06-23 05:11:54 - 可疑请求 | IP: ************:52269 | 路径: /application/.env, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 05:11:56,887 WARNING: [安全监控] 2025-06-23 05:11:56 - 可疑请求 | IP: ************:36745 | 路径: /application/.env, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 05:12:00,264 WARNING: [安全监控] 2025-06-23 05:12:00 - 可疑请求 | IP: ************:65143 | 路径: /.env.project , 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 05:12:03,936 WARNING: [安全监控] 2025-06-23 05:12:03 - 可疑请求 | IP: ************:25055 | 路径: /.env.project , 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 05:12:07,328 WARNING: [安全监控] 2025-06-23 05:12:07 - 可疑请求 | IP: ************:31681 | 路径: /.env.dist, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 05:12:11,204 WARNING: [安全监控] 2025-06-23 05:12:11 - 可疑请求 | IP: ************:50363 | 路径: /.env.dist, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 05:12:12,602 WARNING: [安全监控] 2025-06-23 05:12:12 - 可疑请求 | IP: ************:45051 | 路径: /back/.env, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 05:12:15,121 WARNING: [安全监控] 2025-06-23 05:12:15 - 可疑请求 | IP: ************:30991 | 路径: /back/.env, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 05:12:17,716 WARNING: [安全监控] 2025-06-23 05:12:17 - 可疑请求 | IP: ************:18067 | 路径: /core/.env, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 05:12:19,260 WARNING: [安全监控] 2025-06-23 05:12:19 - 可疑请求 | IP: *************:64519 | 路径: /core/.env, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 05:12:21,363 WARNING: [安全监控] 2025-06-23 05:12:21 - 可疑请求 | IP: ***********6:20777 | 路径: /docker/.env, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 05:12:24,764 WARNING: [安全监控] 2025-06-23 05:12:24 - 可疑请求 | IP: ************:39815 | 路径: /docker/.env, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 05:12:27,481 WARNING: [安全监控] 2025-06-23 05:12:27 - 可疑请求 | IP: *************:40893 | 路径: /fedex/.env, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 05:12:30,376 WARNING: [安全监控] 2025-06-23 05:12:30 - 可疑请求 | IP: *************:30149 | 路径: /fedex/.env, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 05:12:32,764 WARNING: [安全监控] 2025-06-23 05:12:32 - 可疑请求 | IP: ************:61807 | 路径: /__tests__/test-become/.env, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 05:12:46,341 WARNING: [安全监控] 2025-06-23 05:12:46 - 可疑请求 | IP: ************:9893 | 路径: /rest/.env, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 05:12:49,889 WARNING: [安全监控] 2025-06-23 05:12:49 - 可疑请求 | IP: ***********:1717 | 路径: /rest/.env, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 05:12:52,515 WARNING: [安全监控] 2025-06-23 05:12:52 - 可疑请求 | IP: *************:33315 | 路径: /shared/.env , 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 05:12:54,791 WARNING: [安全监控] 2025-06-23 05:12:54 - 可疑请求 | IP: ************:27563 | 路径: /shared/.env , 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 05:12:58,406 WARNING: [安全监控] 2025-06-23 05:12:58 - 可疑请求 | IP: ************:24437 | 路径: /sources/.env, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 05:13:02,143 WARNING: [安全监控] 2025-06-23 05:13:02 - 可疑请求 | IP: *************:46583 | 路径: /sources/.env, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 05:13:07,407 WARNING: [安全监控] 2025-06-23 05:13:07 - 可疑请求 | IP: *************:62687 | 路径: /enviroments/.env.production, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 05:13:12,967 WARNING: [安全监控] 2025-06-23 05:13:12 - 可疑请求 | IP: ************:34299 | 路径: /enviroments/.env.production, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 05:19:03,502 WARNING: [安全监控] 2025-06-23 05:19:03 - 可疑请求 | IP: ************** | 路径: /, 指标: 无User-Agent [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 05:22:20,186 WARNING: [安全监控] 2025-06-23 05:22:20 - 可疑请求 | IP: **************:41422 | 路径: /.env, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 05:22:26,307 WARNING: [安全监控] 2025-06-23 05:22:26 - 可疑请求 | IP: **************:47458 | 路径: /api/.env, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 05:22:27,619 WARNING: [安全监控] 2025-06-23 05:22:27 - 可疑请求 | IP: **************:33766 | 路径: /backend/.env, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 05:22:28,634 WARNING: [安全监控] 2025-06-23 05:22:28 - 可疑请求 | IP: **************:44010 | 路径: /admin/.env, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 05:22:29,289 WARNING: [安全监控] 2025-06-23 05:22:29 - 可疑请求 | IP: **************:51456 | 路径: /.env.example, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 05:22:32,808 WARNING: [安全监控] 2025-06-23 05:22:32 - 可疑请求 | IP: **************:34442 | 路径: /stats/.env, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 05:22:33,565 WARNING: [安全监控] 2025-06-23 05:22:33 - 可疑请求 | IP: **************:43412 | 路径: /tmp/.env, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 05:22:34,196 WARNING: [安全监控] 2025-06-23 05:22:34 - 可疑请求 | IP: **************:50140 | 路径: /htdocs/.env, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 05:22:34,853 WARNING: [安全监控] 2025-06-23 05:22:34 - 可疑请求 | IP: **************:56664 | 路径: /facturacion/.env, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 05:22:36,790 WARNING: [安全监控] 2025-06-23 05:22:36 - 可疑请求 | IP: **************:49486 | 路径: /videos/.env, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 05:22:39,322 WARNING: [安全监控] 2025-06-23 05:22:39 - 可疑请求 | IP: **************:47828 | 路径: /horde/.env, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 05:22:40,066 WARNING: [安全监控] 2025-06-23 05:22:40 - 可疑请求 | IP: **************:58138 | 路径: /fileserver/.env, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 05:22:40,726 WARNING: [安全监控] 2025-06-23 05:22:40 - 可疑请求 | IP: **************:37082 | 路径: /.env.swp, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 05:22:41,385 WARNING: [安全监控] 2025-06-23 05:22:41 - 可疑请求 | IP: **************:44028 | 路径: /root/.env, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 05:22:42,088 WARNING: [安全监控] 2025-06-23 05:22:42 - 可疑请求 | IP: **************:51074 | 路径: /logging/.env, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 05:22:42,805 WARNING: [安全监控] 2025-06-23 05:22:42 - 可疑请求 | IP: **************:59090 | 路径: /rest/.env, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 05:22:44,197 WARNING: [安全监控] 2025-06-23 05:22:44 - 可疑请求 | IP: **************:45174 | 路径: /.env.development.local, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 05:22:46,390 WARNING: [安全监控] 2025-06-23 05:22:46 - 可疑请求 | IP: **************:58650 | 路径: /http/.env, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 05:22:47,433 WARNING: [安全监控] 2025-06-23 05:22:47 - 可疑请求 | IP: **************:54842 | 路径: /sessions/.env, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 05:22:48,150 WARNING: [安全监控] 2025-06-23 05:22:48 - 可疑请求 | IP: **************:34340 | 路径: /example/.env, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 05:22:49,540 WARNING: [安全监控] 2025-06-23 05:22:49 - 可疑请求 | IP: **************:49760 | 路径: /m/.env, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 05:22:51,073 WARNING: [安全监控] 2025-06-23 05:22:51 - 可疑请求 | IP: **************:38502 | 路径: /.env.dev, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 05:22:51,729 WARNING: [安全监控] 2025-06-23 05:22:51 - 可疑请求 | IP: **************:45012 | 路径: /user/.env, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 05:31:07,193 WARNING: [安全监控] 2025-06-23 05:31:07 - 可疑请求 | IP: ************:37794 | 路径: /.git/index, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 06:41:59,472 WARNING: [安全监控] 2025-06-23 06:41:59 - 可疑请求 | IP: *************:57252 | 路径: /.git/refs/remotes/, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 06:54:09,805 WARNING: [安全监控] 2025-06-23 06:54:09 - 可疑请求 | IP: *************:56416 | 路径: /.env, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 06:55:04,880 WARNING: [安全监控] 2025-06-23 06:55:04 - 可疑请求 | IP: *************:60545 | 路径: /wp-admin/install.php, 指标: 无User-Agent, 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 06:55:05,239 WARNING: [安全监控] 2025-06-23 06:55:05 - 可疑请求 | IP: *************:60545 | 路径: /wp-admin/install.php, 指标: 无User-Agent, 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 07:03:50,720 WARNING: [安全监控] 2025-06-23 07:03:50 - 可疑请求 | IP: *************:34316 | 路径: /, 指标: 无User-Agent [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 07:22:11,983 WARNING: [安全监控] 2025-06-23 07:22:11 - 可疑请求 | IP: **************:58771 | 路径: /boaform/admin/formLogin, 指标: 无User-Agent [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 07:23:08,356 WARNING: [安全监控] 2025-06-23 07:23:08 - 可疑请求 | IP: **************:54625 | 路径: /, 指标: 可疑机器人User-Agent [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 07:49:31,530 WARNING: [安全监控] 2025-06-23 07:49:31 - 可疑请求 | IP: ************* | 路径: /, 指标: 无User-Agent [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 08:27:49,110 WARNING: [安全监控] 2025-06-23 08:27:49 - 可疑请求 | IP: ************* | 路径: /, 指标: 无User-Agent [in C:\StudentsCMSSP\app\security_monitor.py:128]
