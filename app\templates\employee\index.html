{% extends 'base.html' %}

{% block title %}员工管理 - {{ super() }}{% endblock %}

{% block styles %}
{{ super() }}
<style nonce="{{ csp_nonce }}">
    /* 员工管理仪表盘 - 简洁企业级样式 */

    /* 页面头部 */
    .page-header {
        margin-bottom: 20px;
    }

    .page-header h3 {
        color: #2c5aa0;
        font-weight: 600;
        font-size: 18px;
        margin: 0;
    }

    /* 统计卡片 */
    .stats-card {
        background: white;
        border: 1px solid #e9ecef;
        border-radius: 4px;
        padding: 16px;
        margin-bottom: 16px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        height: 100%;
    }

    .stats-card-title {
        font-size: 13px;
        font-weight: 600;
        color: #6c757d;
        margin: 0 0 8px 0;
    }

    .stats-card-value {
        font-size: 24px;
        font-weight: 700;
        color: #2c5aa0;
        margin: 0 0 4px 0;
        line-height: 1;
    }

    .stats-card-icon {
        float: right;
        width: 32px;
        height: 32px;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        color: white;
        background: var(--theme-primary);
        margin-top: -8px;
    }

    .stats-card.warning .stats-card-icon { background: #ffc107; }
    .stats-card.success .stats-card-icon { background: #28a745; }
    .stats-card.danger .stats-card-icon { background: #dc3545; }
    .stats-card.info .stats-card-icon { background: #17a2b8; }
    .stats-card.secondary .stats-card-icon { background: #6c757d; }

    .stats-card-footer {
        font-size: 12px;
        color: #6c757d;
        margin-top: 8px;
        clear: both;
    }

    /* 图表卡片 */
    .chart-card {
        background: white;
        border: 1px solid #e9ecef;
        border-radius: 4px;
        padding: 16px;
        margin-bottom: 16px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    }

    .chart-card-header {
        border-bottom: 1px solid #e9ecef;
        padding-bottom: 12px;
        margin-bottom: 16px;
    }

    .chart-card-title {
        font-size: 14px;
        font-weight: 600;
        color: #2c5aa0;
        margin: 0;
    }

    .chart-container {
        height: 250px;
        position: relative;
    }

    /* 提醒卡片 */
    .alert-card {
        background: white;
        border: 1px solid #e9ecef;
        border-radius: 4px;
        margin-bottom: 16px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        overflow: hidden;
    }

    .alert-card-header {
        padding: 12px 16px;
        font-weight: 600;
        font-size: 13px;
        border-bottom: 1px solid #e9ecef;
    }

    .alert-card-header.warning {
        background: #ffc107;
        color: white;
        border-bottom-color: #e0a800;
    }

    .alert-card-header.danger {
        background: #dc3545;
        color: white;
        border-bottom-color: #c82333;
    }

    .alert-items {
        max-height: 200px;
        overflow-y: auto;
        padding: 16px;
    }

    .alert-items ul {
        margin: 0;
        padding: 0;
        list-style: none;
    }

    .alert-items li {
        padding: 8px 0;
        border-bottom: 1px solid #f8f9fa;
        font-size: 13px;
        line-height: 1.4;
    }

    .alert-items li:last-child {
        border-bottom: none;
    }

    /* 按钮组 */
    .btn-group .btn {
        font-size: 13px;
        padding: 8px 12px;
        font-weight: 500;
    }

    /* 移动端优化 */
    @media (max-width: 768px) {
        .stats-card {
            padding: 12px;
            margin-bottom: 12px;
        }

        .stats-card-value {
            font-size: 20px;
        }

        .chart-container {
            height: 200px;
        }

        .btn-group .btn {
            font-size: 12px;
            padding: 6px 10px;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- 页面头部 -->
<div class="page-header">
    <div class="row align-items-center">
        <div class="col-md-6">
            <h3><i class="fas fa-users mr-2"></i>员工管理</h3>
        </div>
        <div class="col-md-6 text-right">
            <div class="btn-group">
                <a href="{{ url_for('employee.add_employee') }}" class="btn btn-primary">
                    <i class="fas fa-plus mr-1"></i>添加员工
                </a>
                <a href="{{ url_for('employee.health_certificates') }}" class="btn btn-info">
                    <i class="fas fa-id-card mr-1"></i>健康证管理
                </a>
                <a href="{{ url_for('employee.daily_health_check') }}" class="btn btn-success">
                    <i class="fas fa-heartbeat mr-1"></i>健康检查
                </a>
            </div>
        </div>
    </div>
</div>

<!-- 员工统计概览 -->
<div class="row">
    <div class="col-lg-3 col-md-6 col-12">
        <div class="stats-card">
            <div class="stats-card-icon">
                <i class="fas fa-users"></i>
            </div>
            <h6 class="stats-card-title">员工总数</h6>
            <div class="stats-card-value">{{ dashboard_data.total_employees }}</div>
            <div class="stats-card-footer">包含所有状态的员工</div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 col-12">
        <div class="stats-card success">
            <div class="stats-card-icon">
                <i class="fas fa-user-check"></i>
            </div>
            <h6 class="stats-card-title">在职员工</h6>
            <div class="stats-card-value">{{ dashboard_data.active_employees }}</div>
            <div class="stats-card-footer">正常工作状态</div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 col-12">
        <div class="stats-card warning">
            <div class="stats-card-icon">
                <i class="fas fa-user-clock"></i>
            </div>
            <h6 class="stats-card-title">休假员工</h6>
            <div class="stats-card-value">{{ dashboard_data.on_leave_employees }}</div>
            <div class="stats-card-footer">临时休假状态</div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 col-12">
        <div class="stats-card secondary">
            <div class="stats-card-icon">
                <i class="fas fa-user-times"></i>
            </div>
            <h6 class="stats-card-title">离职员工</h6>
            <div class="stats-card-value">{{ dashboard_data.inactive_employees }}</div>
            <div class="stats-card-footer">已离职状态</div>
        </div>
    </div>
</div>

<!-- 健康证状态统计 -->
<div class="row">
    <div class="col-lg-3 col-md-6 col-12">
        <div class="stats-card info">
            <div class="stats-card-icon">
                <i class="fas fa-id-card"></i>
            </div>
            <h6 class="stats-card-title">健康证有效</h6>
            <div class="stats-card-value">{{ dashboard_data.health_cert_valid }}</div>
            <div class="stats-card-footer">证件状态正常</div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 col-12">
        <div class="stats-card warning">
            <div class="stats-card-icon">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            <h6 class="stats-card-title">即将到期</h6>
            <div class="stats-card-value">{{ dashboard_data.health_cert_expiring }}</div>
            <div class="stats-card-footer">需要及时续办</div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 col-12">
        <div class="stats-card danger">
            <div class="stats-card-icon">
                <i class="fas fa-calendar-times"></i>
            </div>
            <h6 class="stats-card-title">已过期</h6>
            <div class="stats-card-value">{{ dashboard_data.health_cert_expired }}</div>
            <div class="stats-card-footer">需要立即处理</div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 col-12">
        <div class="stats-card secondary">
            <div class="stats-card-icon">
                <i class="fas fa-file-alt"></i>
            </div>
            <h6 class="stats-card-title">未办理</h6>
            <div class="stats-card-value">{{ dashboard_data.health_cert_none }}</div>
            <div class="stats-card-footer">需要办理证件</div>
        </div>
    </div>
</div>

<!-- 数据分析图表 -->
<div class="row">
    <div class="col-lg-6 col-12">
        <div class="chart-card">
            <div class="chart-card-header">
                <h6 class="chart-card-title">部门分布</h6>
            </div>
            <div class="chart-container">
                <canvas id="departmentChart"></canvas>
            </div>
        </div>
    </div>
    <div class="col-lg-6 col-12">
        <div class="chart-card">
            <div class="chart-card-header">
                <h6 class="chart-card-title">职位分布</h6>
            </div>
            <div class="chart-container">
                <canvas id="positionChart"></canvas>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-6 col-12">
        <div class="chart-card">
            <div class="chart-card-header">
                <h6 class="chart-card-title">区域分布</h6>
            </div>
            <div class="chart-container">
                <canvas id="areaChart"></canvas>
            </div>
        </div>
    </div>
    <div class="col-lg-6 col-12">
        <div class="chart-card">
            <div class="chart-card-header">
                <h6 class="chart-card-title">系统账号关联情况</h6>
            </div>
            <div class="chart-container">
                <canvas id="accountChart"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- 健康证提醒 -->
{% if expiring_certs or expired_certs %}
<div class="row">
    {% if expiring_certs %}
    <div class="col-lg-6 col-12">
        <div class="alert-card">
            <div class="alert-card-header warning">
                <i class="fas fa-exclamation-triangle mr-1"></i>健康证即将到期
            </div>
            <div class="alert-items">
                <ul>
                    {% for item in expiring_certs %}
                    <li>
                        <strong>{{ item.employee.name }}</strong> - {{ item.days_left }}天后到期
                        <div class="text-muted small">{{ item.certificate.expire_date|format_datetime('%Y-%m-%d') }}</div>
                    </li>
                    {% endfor %}
                </ul>
            </div>
        </div>
    </div>
    {% endif %}
    {% if expired_certs %}
    <div class="col-lg-6 col-12">
        <div class="alert-card">
            <div class="alert-card-header danger">
                <i class="fas fa-calendar-times mr-1"></i>健康证已过期
            </div>
            <div class="alert-items">
                <ul>
                    {% for item in expired_certs %}
                    <li>
                        <strong>{{ item.employee.name }}</strong> - 已过期{{ item.days_expired }}天
                        <div class="text-muted small">{{ item.certificate.expire_date|format_datetime('%Y-%m-%d') }}</div>
                    </li>
                    {% endfor %}
                </ul>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endif %}

<div class="card">
    <div class="card-body">
        <!-- 桌面端表格 -->
        <div class="table-responsive desktop-only">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>姓名</th>
                        <th>性别</th>
                        <th>职位</th>
                        <th>部门</th>
                        <th>所属区域</th>
                        <th>联系电话</th>
                        <th>状态</th>
                        <th>健康证状态</th>
                        <th>系统账号</th>
                        <th>食品安全</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for employee in employees.items %}
                    <tr>
                        <td>{{ employee.id }}</td>
                        <td>{{ employee.name }}</td>
                        <td>{{ employee.gender }}</td>
                        <td>{{ employee.position }}</td>
                        <td>{{ employee.department }}</td>
                        <td>
                            {% if employee.area %}
                            <span class="badge badge-info">{{ employee.area.get_level_name() }}</span>
                            {{ employee.area.name }}
                            {% else %}
                            <span class="text-muted">未设置</span>
                            {% endif %}
                        </td>
                        <td>{{ employee.phone }}</td>
                        <td>
                            {% if employee.status == 1 %}
                            <span class="badge badge-success">在职</span>
                            {% elif employee.status == 0 %}
                            <span class="badge badge-secondary">离职</span>
                            {% elif employee.status == 2 %}
                            <span class="badge badge-warning">休假</span>
                            {% endif %}
                        </td>
                        <td>
                            {% set cert_status = employee.get_health_certificate_status() %}
                            {% if cert_status == "有效" %}
                            <span class="badge badge-success">{{ cert_status }}</span>
                            {% elif cert_status == "未办理" %}
                            <span class="badge badge-secondary">{{ cert_status }}</span>
                            {% elif cert_status == "已过期" %}
                            <span class="badge badge-danger">{{ cert_status }}</span>
                            {% elif "即将过期" in cert_status %}
                            <span class="badge badge-warning">{{ cert_status }}</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if employee.user %}
                                <span class="badge badge-primary">已关联</span>
                                {% if employee.user.status == 0 %}
                                    <span class="badge badge-danger">已禁用</span>
                                {% endif %}
                            {% else %}
                                <span class="badge badge-secondary">未关联</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if employee.responsible_areas %}
                                <span class="badge badge-info" title="负责区域">
                                    <i class="fas fa-map-marker-alt"></i>
                                </span>
                            {% endif %}

                            {% if employee.food_safety_certifications %}
                                <span class="badge badge-success" title="食品安全证书">
                                    <i class="fas fa-certificate"></i>
                                </span>
                            {% endif %}

                            {% if employee.safety_violation_count and employee.safety_violation_count > 0 %}
                                <span class="badge badge-danger" title="安全违规: {{ employee.safety_violation_count }}次">
                                    <i class="fas fa-exclamation-triangle"></i> {{ employee.safety_violation_count }}
                                </span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="{{ url_for('employee.view_employee', id=employee.id) }}" class="btn btn-info" title="查看详情">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ url_for('employee.edit_employee', id=employee.id) }}" class="btn btn-primary" title="编辑信息">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="{{ url_for('employee.add_health_certificate', employee_id=employee.id) }}" class="btn btn-warning" title="添加健康证">
                                    <i class="fas fa-id-card"></i>
                                </a>
                                <a href="{{ url_for('employee.add_health_check', employee_id=employee.id) }}" class="btn btn-success" title="健康检查">
                                    <i class="fas fa-heartbeat"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% else %}
                    <tr>
                        <td colspan="12" class="text-center">暂无员工数据</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- 移动端员工卡片 -->
        <div class="mobile-only">
            {% for employee in employees.items %}
            <div class="card mb-3 border-left-{% if employee.status == 1 %}success{% elif employee.status == 2 %}warning{% else %}secondary{% endif %}">
                <div class="card-body py-2">
                    <div class="row">
                        <div class="col-8">
                            <h6 class="mb-1">{{ employee.name }}</h6>
                            <small class="text-muted">ID: {{ employee.id }} | {{ employee.gender }}</small>
                        </div>
                        <div class="col-4 text-right">
                            {% if employee.status == 1 %}
                            <span class="badge badge-success">在职</span>
                            {% elif employee.status == 0 %}
                            <span class="badge badge-secondary">离职</span>
                            {% elif employee.status == 2 %}
                            <span class="badge badge-warning">休假</span>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row mt-2">
                        <div class="col-6">
                            <small class="text-muted">职位</small>
                            <div class="small">{{ employee.position }}</div>
                        </div>
                        <div class="col-6">
                            <small class="text-muted">部门</small>
                            <div class="small">{{ employee.department }}</div>
                        </div>
                    </div>

                    <div class="row mt-2">
                        <div class="col-6">
                            <small class="text-muted">联系电话</small>
                            <div class="small">{{ employee.phone }}</div>
                        </div>
                        <div class="col-6">
                            <small class="text-muted">所属区域</small>
                            <div class="small">
                                {% if employee.area %}
                                {{ employee.area.name }}
                                {% else %}
                                未设置
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="row mt-2">
                        <div class="col-6">
                            <small class="text-muted">健康证状态</small>
                            <div>
                                {% set cert_status = employee.get_health_certificate_status() %}
                                {% if cert_status == "有效" %}
                                <span class="badge badge-success badge-sm">{{ cert_status }}</span>
                                {% elif cert_status == "未办理" %}
                                <span class="badge badge-secondary badge-sm">{{ cert_status }}</span>
                                {% elif cert_status == "已过期" %}
                                <span class="badge badge-danger badge-sm">{{ cert_status }}</span>
                                {% elif "即将过期" in cert_status %}
                                <span class="badge badge-warning badge-sm">{{ cert_status }}</span>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-6">
                            <small class="text-muted">系统账号</small>
                            <div>
                                {% if employee.user %}
                                <span class="badge badge-primary badge-sm">已关联</span>
                                {% if employee.user.status == 0 %}
                                <span class="badge badge-danger badge-sm">已禁用</span>
                                {% endif %}
                                {% else %}
                                <span class="badge badge-secondary badge-sm">未关联</span>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="row mt-2">
                        <div class="col-12">
                            <div class="btn-group btn-group-sm w-100" role="group">
                                <a href="{{ url_for('employee.view_employee', id=employee.id) }}" class="btn btn-outline-info" title="查看详情">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ url_for('employee.edit_employee', id=employee.id) }}" class="btn btn-outline-primary" title="编辑信息">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="{{ url_for('employee.add_health_certificate', employee_id=employee.id) }}" class="btn btn-outline-warning" title="添加健康证">
                                    <i class="fas fa-id-card"></i>
                                </a>
                                <a href="{{ url_for('employee.add_health_check', employee_id=employee.id) }}" class="btn btn-outline-success" title="健康检查">
                                    <i class="fas fa-heartbeat"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% else %}
            <div class="text-center py-4">
                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                <h5>暂无员工数据</h5>
                <p class="text-muted">您可以添加新的员工信息</p>
            </div>
            {% endfor %}
        </div>
    </div>
    {% if employees.pages > 1 %}
    <div class="card-footer">
        <nav aria-label="Page navigation">
            <ul class="pagination justify-content-center mb-0">
                <li class="page-item {% if not employees.has_prev %}disabled{% endif %}">
                    <a class="page-link" href="{{ url_for('employee.index', page=employees.prev_num) if employees.has_prev else '#' }}">
                        <i class="fas fa-chevron-left"></i> 上一页
                    </a>
                </li>
                {% for page in employees.iter_pages() %}
                    {% if page %}
                        <li class="page-item {% if page == employees.page %}active{% endif %}">
                            <a class="page-link" href="{{ url_for('employee.index', page=page) }}">{{ page }}</a>
                        </li>
                    {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">...</span>
                        </li>
                    {% endif %}
                {% endfor %}
                <li class="page-item {% if not employees.has_next %}disabled{% endif %}">
                    <a class="page-link" href="{{ url_for('employee.index', page=employees.next_num) if employees.has_next else '#' }}">
                        下一页 <i class="fas fa-chevron-right"></i>
                    </a>
                </li>
            </ul>
        </nav>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/chart-js/chart.min.js') }}"></script>
<script nonce="{{ csp_nonce }}">
    $(document).ready(function() {
        // 部门分布图表
        var departmentCtx = document.getElementById('departmentChart').getContext('2d');
        var departmentChart = new Chart(departmentCtx, {
            type: 'bar',
            data: {
                labels: [
                    {% for dept, count in dashboard_data.departments.items() %}
                    '{{ dept }}',
                    {% endfor %}
                ],
                datasets: [{
                    label: '员工数量',
                    data: [
                        {% for dept, count in dashboard_data.departments.items() %}
                        {{ count }},
                        {% endfor %}
                    ],
                    backgroundColor: [
                        'rgba(54, 162, 235, 0.7)',
                        'rgba(75, 192, 192, 0.7)',
                        'rgba(153, 102, 255, 0.7)',
                        'rgba(255, 159, 64, 0.7)',
                        'rgba(255, 99, 132, 0.7)'
                    ],
                    borderColor: [
                        'rgba(54, 162, 235, 1)',
                        'rgba(75, 192, 192, 1)',
                        'rgba(153, 102, 255, 1)',
                        'rgba(255, 159, 64, 1)',
                        'rgba(255, 99, 132, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            precision: 0
                        }
                    }
                }
            }
        });

        // 职位分布图表
        var positionCtx = document.getElementById('positionChart').getContext('2d');
        var positionChart = new Chart(positionCtx, {
            type: 'bar',
            data: {
                labels: [
                    {% for position, count in dashboard_data.positions.items() %}
                    '{{ position }}',
                    {% endfor %}
                ],
                datasets: [{
                    label: '员工数量',
                    data: [
                        {% for position, count in dashboard_data.positions.items() %}
                        {{ count }},
                        {% endfor %}
                    ],
                    backgroundColor: [
                        'rgba(255, 99, 132, 0.7)',
                        'rgba(54, 162, 235, 0.7)',
                        'rgba(255, 206, 86, 0.7)',
                        'rgba(75, 192, 192, 0.7)',
                        'rgba(153, 102, 255, 0.7)'
                    ],
                    borderColor: [
                        'rgba(255, 99, 132, 1)',
                        'rgba(54, 162, 235, 1)',
                        'rgba(255, 206, 86, 1)',
                        'rgba(75, 192, 192, 1)',
                        'rgba(153, 102, 255, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            precision: 0
                        }
                    }
                }
            }
        });

        // 区域分布图表
        var areaCtx = document.getElementById('areaChart').getContext('2d');
        var areaChart = new Chart(areaCtx, {
            type: 'pie',
            data: {
                labels: [
                    {% for area, count in dashboard_data.areas.items() %}
                    '{{ area }}',
                    {% endfor %}
                ],
                datasets: [{
                    data: [
                        {% for area, count in dashboard_data.areas.items() %}
                        {{ count }},
                        {% endfor %}
                    ],
                    backgroundColor: [
                        'rgba(255, 99, 132, 0.7)',
                        'rgba(54, 162, 235, 0.7)',
                        'rgba(255, 206, 86, 0.7)',
                        'rgba(75, 192, 192, 0.7)',
                        'rgba(153, 102, 255, 0.7)'
                    ],
                    borderColor: [
                        'rgba(255, 99, 132, 1)',
                        'rgba(54, 162, 235, 1)',
                        'rgba(255, 206, 86, 1)',
                        'rgba(75, 192, 192, 1)',
                        'rgba(153, 102, 255, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right'
                    }
                }
            }
        });

        // 系统账号关联情况图表
        var accountCtx = document.getElementById('accountChart').getContext('2d');
        var accountChart = new Chart(accountCtx, {
            type: 'doughnut',
            data: {
                labels: ['已关联系统账号', '未关联系统账号'],
                datasets: [{
                    data: [
                        {{ dashboard_data.with_system_account }},
                        {{ dashboard_data.without_system_account }}
                    ],
                    backgroundColor: [
                        'rgba(54, 162, 235, 0.7)',
                        'rgba(255, 99, 132, 0.7)'
                    ],
                    borderColor: [
                        'rgba(54, 162, 235, 1)',
                        'rgba(255, 99, 132, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right'
                    }
                }
            }
        });
    });
</script>
{% endblock %}