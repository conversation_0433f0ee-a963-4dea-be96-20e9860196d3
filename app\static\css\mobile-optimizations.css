/* 移动端优化样式 */

/* ===== 移动端基础优化 ===== */
@media (max-width: 768px) {
    /* 基础布局优化 */
    .container-fluid {
        padding-left: 12px !important;
        padding-right: 12px !important;
    }
    
    /* 字体大小优化 */
    body {
        font-size: 14px;
        line-height: 1.5;
    }
    
    h1 { font-size: 24px; }
    h2 { font-size: 20px; }
    h3 { font-size: 18px; }
    h4 { font-size: 16px; }
    h5 { font-size: 14px; }
    h6 { font-size: 13px; }
    
    /* 触摸友好的按钮 */
    .btn {
        min-height: 44px;
        padding: 12px 16px;
        font-size: 14px;
        border-radius: 8px;
    }
    
    .btn-sm {
        min-height: 36px;
        padding: 8px 12px;
        font-size: 13px;
    }
    
    /* 表单控件优化 */
    .form-control {
        min-height: 44px;
        padding: 12px 16px;
        font-size: 16px; /* 防止iOS缩放 */
        border-radius: 8px;
    }
    
    .form-select {
        min-height: 44px;
        padding: 12px 16px;
        font-size: 16px;
    }
    
    /* 表格移动端优化 */
    .table-responsive {
        border: none;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }
    
    .table {
        font-size: 13px;
        margin-bottom: 0;
    }
    
    .table th,
    .table td {
        padding: 8px 6px;
        white-space: nowrap;
    }
    
    /* 卡片优化 */
    .card {
        border-radius: 12px;
        margin-bottom: 16px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    
    .card-header {
        padding: 16px;
        border-radius: 12px 12px 0 0;
    }
    
    .card-body {
        padding: 16px;
    }
    
    /* 模态框优化 */
    .modal-dialog {
        margin: 8px;
        max-width: calc(100% - 16px);
    }
    
    .modal-content {
        border-radius: 12px;
    }
    
    .modal-header {
        padding: 16px;
    }
    
    .modal-body {
        padding: 16px;
    }
    
    .modal-footer {
        padding: 16px;
        flex-direction: column;
        gap: 8px;
    }
    
    .modal-footer .btn {
        width: 100%;
        margin: 0;
    }
}

/* ===== 财务模块移动端优化 ===== */
@media (max-width: 768px) {
    /* 财务凭证列表优化 */
    .voucher-header {
        padding: 16px;
        border-radius: 12px;
    }
    
    .voucher-header .stats {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;
        margin-top: 16px;
    }
    
    .stat-item {
        background: rgba(255,255,255,0.2);
        padding: 12px;
        border-radius: 8px;
        text-align: center;
    }
    
    /* 搜索面板移动端优化 */
    .search-panel {
        padding: 16px;
        border-radius: 12px;
    }
    
    .search-form {
        display: flex;
        flex-direction: column;
        gap: 12px;
    }
    
    .search-form .form-group {
        margin-bottom: 0;
    }
    
    .search-form .btn {
        width: 100%;
        margin-top: 8px;
    }
    
    /* 凭证表格移动端优化 */
    .voucher-table-container {
        border-radius: 12px;
        overflow: hidden;
    }
    
    .table-header {
        padding: 16px;
        flex-direction: column;
        gap: 12px;
        align-items: stretch;
    }
    
    .table-actions {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 8px;
    }
    
    .voucher-table {
        font-size: 12px;
    }
    
    .voucher-table th,
    .voucher-table td {
        padding: 8px 4px;
    }
    
    /* 隐藏不重要的列 */
    .voucher-table .d-none-mobile {
        display: none;
    }
    
    /* 操作按钮优化 */
    .action-buttons {
        flex-direction: column;
        gap: 4px;
    }
    
    .btn-icon {
        width: 100%;
        height: 32px;
        justify-content: flex-start;
        padding: 0 8px;
        font-size: 12px;
    }
    
    .btn-icon i {
        margin-right: 6px;
    }
    
    /* 分页优化 */
    .pagination-container {
        padding: 16px;
        flex-direction: column;
        gap: 12px;
        text-align: center;
    }
    
    .pagination {
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .page-btn {
        min-width: 40px;
        height: 40px;
    }
}

/* ===== 财务凭证创建/编辑移动端优化 ===== */
@media (max-width: 768px) {
    .uf-voucher-editor {
        padding: 8px !important;
    }
    
    .uf-voucher-window {
        margin: 0 !important;
        border-radius: 12px !important;
    }
    
    .uf-window-header {
        padding: 16px;
        flex-direction: column;
        gap: 8px;
        align-items: stretch;
    }
    
    .uf-window-title {
        justify-content: center;
        font-size: 16px;
    }
    
    .uf-toolbar {
        padding: 12px 16px;
        flex-direction: column;
        gap: 12px;
        align-items: stretch;
    }
    
    .uf-toolbar-group {
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .uf-balance-indicator {
        margin-left: 0;
        justify-content: center;
    }
    
    /* 凭证信息栏移动端优化 */
    .uf-voucher-info {
        padding: 16px !important;
    }
    
    .uf-info-row {
        flex-direction: column !important;
        gap: 12px !important;
        align-items: stretch !important;
    }
    
    .uf-form-group {
        flex-direction: row !important;
        justify-content: space-between !important;
        align-items: center !important;
        padding: 8px 0 !important;
        border-bottom: 1px solid #e0e0e0 !important;
    }
    
    .uf-form-group:last-child {
        border-bottom: none !important;
    }
    
    .uf-form-label {
        font-weight: 600 !important;
        min-width: 80px !important;
    }
    
    .uf-voucher-type,
    .uf-voucher-number,
    .uf-voucher-date,
    .uf-attachment-count {
        width: auto !important;
        min-width: 120px !important;
        text-align: right !important;
    }
    
    /* 凭证表格移动端优化 */
    .uf-voucher-table-container {
        padding: 8px !important;
        overflow-x: auto !important;
        -webkit-overflow-scrolling: touch !important;
    }
    
    .uf-voucher-table {
        min-width: 600px !important;
        font-size: 12px !important;
    }
    
    .uf-voucher-table th,
    .uf-voucher-table td {
        padding: 6px 4px !important;
    }
    
    /* 摘要输入框移动端优化 */
    .uf-summary-input {
        font-size: 14px !important;
        padding: 8px !important;
        min-height: 44px !important;
    }
    
    /* 科目选择器移动端优化 */
    .uf-subject-selector {
        min-height: 44px !important;
    }
    
    .uf-subject-code {
        font-size: 12px !important;
        padding: 6px 4px !important;
    }
    
    .uf-subject-name {
        font-size: 10px !important;
        padding: 2px 4px !important;
    }
    
    /* 金额输入框移动端优化 */
    .uf-amount-input {
        font-size: 14px !important;
        text-align: right !important;
        min-height: 44px !important;
    }
}

/* ===== 智能助手移动端优化 ===== */
@media (max-width: 768px) {
    .assistant-card {
        padding: 16px;
        margin-bottom: 16px;
    }
    
    .quick-actions {
        grid-template-columns: 1fr;
        gap: 16px;
    }
    
    .business-type-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;
    }
    
    .business-type-card {
        padding: 16px 12px;
    }
    
    .business-type-card i {
        font-size: 24px;
    }
    
    .business-type-card h5 {
        font-size: 13px;
    }
    
    .wizard-content {
        padding: 16px;
    }
    
    .wizard-steps {
        flex-direction: column;
        gap: 8px;
    }
    
    .step {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 8px;
        border-radius: 8px;
        background: rgba(255,255,255,0.1);
    }
    
    .step.active {
        background: rgba(255,255,255,0.2);
    }
    
    .step-circle {
        width: 24px;
        height: 24px;
        font-size: 12px;
        margin: 0;
    }
    
    .step-label {
        font-size: 13px;
        margin: 0;
    }
    
    .step:not(:last-child)::after {
        display: none;
    }
}

/* ===== 触摸优化 ===== */
@media (max-width: 768px) {
    /* 增大可点击区域 */
    .clickable {
        min-height: 44px;
        min-width: 44px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    /* 滑动手势支持 */
    .swipeable {
        touch-action: pan-x;
        -webkit-overflow-scrolling: touch;
    }
    
    /* 防止意外缩放 */
    input[type="number"],
    input[type="email"],
    input[type="tel"],
    input[type="url"] {
        font-size: 16px; /* 防止iOS Safari缩放 */
    }
    
    /* 优化选择器 */
    select {
        font-size: 16px;
        padding: 12px;
        border-radius: 8px;
    }
    
    /* 优化复选框和单选框 */
    input[type="checkbox"],
    input[type="radio"] {
        width: 20px;
        height: 20px;
        margin: 4px;
    }
    
    /* 优化文本区域 */
    textarea {
        font-size: 16px;
        padding: 12px;
        border-radius: 8px;
        resize: vertical;
    }
}

/* ===== 横屏优化 ===== */
@media (max-width: 768px) and (orientation: landscape) {
    .modal-dialog {
        max-height: 90vh;
        overflow-y: auto;
    }
    
    .uf-voucher-window {
        max-height: 95vh !important;
        overflow-y: auto !important;
    }
    
    .wizard-header {
        padding: 12px 20px;
    }
    
    .wizard-steps {
        flex-direction: row;
        justify-content: space-between;
    }
    
    .step {
        flex-direction: column;
        padding: 4px;
        background: transparent;
    }
}

/* ===== 可访问性增强 ===== */
@media (max-width: 768px) {
    /* 高对比度模式支持 */
    @media (prefers-contrast: high) {
        .btn {
            border: 2px solid currentColor;
        }
        
        .form-control {
            border: 2px solid #333;
        }
        
        .card {
            border: 2px solid #333;
        }
    }
    
    /* 减少动画模式支持 */
    @media (prefers-reduced-motion: reduce) {
        * {
            animation-duration: 0.01ms !important;
            animation-iteration-count: 1 !important;
            transition-duration: 0.01ms !important;
        }
    }
    
    /* 焦点指示器增强 */
    .btn:focus,
    .form-control:focus,
    .form-select:focus {
        outline: 3px solid #007bff;
        outline-offset: 2px;
    }
}
