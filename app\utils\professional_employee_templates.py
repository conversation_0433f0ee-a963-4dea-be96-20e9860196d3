"""
专业员工管理模板生成器
为员工管理模块提供专业、优雅的打印和导出模板
"""

from flask import current_app, send_file
from .professional_template_generator import ProfessionalTemplateGenerator
from reportlab.lib.pagesizes import A4, landscape
from reportlab.lib import colors
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, KeepTogether
from reportlab.lib.units import mm
from decimal import Decimal
import os
from datetime import datetime, timedelta

class ProfessionalEmployeeTemplates(ProfessionalTemplateGenerator):
    """专业员工管理模板生成器"""
    
    def generate_employee_list_excel(self, employees, user_area):
        """生成员工信息清单Excel"""
        # 准备数据
        data = []
        for employee in employees:
            data.append([
                employee.name,
                employee.employee_id or "",
                employee.position or "",
                employee.department or "",
                employee.phone or "",
                employee.email or "",
                self.format_date(employee.hire_date) if hasattr(employee, 'hire_date') and employee.hire_date else "",
                '在职' if employee.is_active else '离职',
                self.format_date(employee.health_certificate_expiry) if hasattr(employee, 'health_certificate_expiry') and employee.health_certificate_expiry else "",
                employee.emergency_contact or "" if hasattr(employee, 'emergency_contact') else "",
                employee.emergency_phone or "" if hasattr(employee, 'emergency_phone') else "",
                self.format_date(employee.created_at)
            ])
        
        headers = ["姓名", "工号", "职位", "部门", "联系电话", "邮箱", "入职日期", "状态", "健康证到期", "紧急联系人", "紧急联系电话", "创建日期"]
        
        # 创建Excel文件
        temp_file, filename = self.create_professional_excel(
            data=data,
            headers=headers,
            title="员工信息清单",
            subtitle="员工基础信息统计表",
            company_name=user_area.name,
            report_date=datetime.now().date()
        )
        
        return send_file(
            temp_file,
            as_attachment=True,
            download_name=filename,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
    
    def generate_health_certificate_warning_excel(self, employees, user_area, warning_days=30):
        """生成健康证到期预警Excel"""
        # 准备数据
        data = []
        warning_date = datetime.now().date() + timedelta(days=warning_days)
        
        for employee in employees:
            if hasattr(employee, 'health_certificate_expiry') and employee.health_certificate_expiry:
                days_to_expire = (employee.health_certificate_expiry - datetime.now().date()).days
                
                if days_to_expire <= warning_days:
                    status = '已过期' if days_to_expire < 0 else f'{days_to_expire}天后过期'
                    urgency = '紧急' if days_to_expire <= 7 else '警告' if days_to_expire <= 15 else '提醒'
                    
                    data.append([
                        employee.name,
                        employee.employee_id or "",
                        employee.position or "",
                        employee.department or "",
                        employee.phone or "",
                        self.format_date(employee.health_certificate_expiry),
                        status,
                        urgency,
                        employee.emergency_contact or "" if hasattr(employee, 'emergency_contact') else "",
                        employee.emergency_phone or "" if hasattr(employee, 'emergency_phone') else ""
                    ])
        
        headers = ["姓名", "工号", "职位", "部门", "联系电话", "健康证到期日期", "状态", "紧急程度", "紧急联系人", "紧急联系电话"]
        
        # 创建Excel文件
        temp_file, filename = self.create_professional_excel(
            data=data,
            headers=headers,
            title="员工健康证到期预警",
            subtitle=f"预警期限：{warning_days}天",
            company_name=user_area.name,
            report_date=datetime.now().date()
        )
        
        return send_file(
            temp_file,
            as_attachment=True,
            download_name=filename,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
    
    def generate_employee_profile_pdf(self, employee, user_area):
        """生成员工档案PDF"""
        # 创建PDF保存目录
        pdf_dir = self.create_pdf_dir('employee_profiles')
        
        # 生成PDF文件名
        filename = f"员工档案_{employee.name}_{datetime.now().strftime('%Y%m%d%H%M%S')}.pdf"
        pdf_path = os.path.join(pdf_dir, filename)
        
        # 创建PDF文档
        doc = SimpleDocTemplate(
            pdf_path,
            pagesize=A4,
            rightMargin=20*mm,
            leftMargin=20*mm,
            topMargin=20*mm,
            bottomMargin=20*mm
        )
        
        content = []
        
        # 创建页眉
        self.create_pdf_header(
            content,
            title="员工档案",
            subtitle=f"姓名：{employee.name}",
            company_name=user_area.name,
            report_date=datetime.now().date()
        )
        
        # 基本信息
        content.append(Paragraph("一、基本信息", self.pdf_styles['section_title']))
        
        basic_info = [
            [
                Paragraph("姓名:", self.pdf_styles['body_text']),
                Paragraph(employee.name, self.pdf_styles['body_text']),
                Paragraph("工号:", self.pdf_styles['body_text']),
                Paragraph(employee.employee_id or "-", self.pdf_styles['body_text'])
            ],
            [
                Paragraph("职位:", self.pdf_styles['body_text']),
                Paragraph(employee.position or "-", self.pdf_styles['body_text']),
                Paragraph("部门:", self.pdf_styles['body_text']),
                Paragraph(employee.department or "-", self.pdf_styles['body_text'])
            ],
            [
                Paragraph("联系电话:", self.pdf_styles['body_text']),
                Paragraph(employee.phone or "-", self.pdf_styles['body_text']),
                Paragraph("邮箱:", self.pdf_styles['body_text']),
                Paragraph(employee.email or "-", self.pdf_styles['body_text'])
            ],
            [
                Paragraph("入职日期:", self.pdf_styles['body_text']),
                Paragraph(self.format_date(employee.hire_date) if hasattr(employee, 'hire_date') and employee.hire_date else "-", self.pdf_styles['body_text']),
                Paragraph("当前状态:", self.pdf_styles['body_text']),
                Paragraph('在职' if employee.is_active else '离职', self.pdf_styles['body_text'])
            ]
        ]
        
        info_table = self.create_info_table(basic_info)
        content.append(info_table)
        content.append(Spacer(1, 10*mm))
        
        # 证件信息
        content.append(Paragraph("二、证件信息", self.pdf_styles['section_title']))
        
        certificate_info = [
            [
                Paragraph("身份证号:", self.pdf_styles['body_text']),
                Paragraph(employee.id_card or "-" if hasattr(employee, 'id_card') else "-", self.pdf_styles['body_text']),
                Paragraph("健康证号:", self.pdf_styles['body_text']),
                Paragraph(employee.health_certificate_no or "-" if hasattr(employee, 'health_certificate_no') else "-", self.pdf_styles['body_text'])
            ],
            [
                Paragraph("健康证到期:", self.pdf_styles['body_text']),
                Paragraph(self.format_date(employee.health_certificate_expiry) if hasattr(employee, 'health_certificate_expiry') and employee.health_certificate_expiry else "-", self.pdf_styles['body_text']),
                Paragraph("食品安全培训:", self.pdf_styles['body_text']),
                Paragraph(self.format_date(employee.food_safety_training) if hasattr(employee, 'food_safety_training') and employee.food_safety_training else "-", self.pdf_styles['body_text'])
            ]
        ]
        
        certificate_table = self.create_info_table(certificate_info)
        content.append(certificate_table)
        content.append(Spacer(1, 10*mm))
        
        # 紧急联系信息
        content.append(Paragraph("三、紧急联系信息", self.pdf_styles['section_title']))
        
        emergency_info = [
            [
                Paragraph("紧急联系人:", self.pdf_styles['body_text']),
                Paragraph(employee.emergency_contact or "-" if hasattr(employee, 'emergency_contact') else "-", self.pdf_styles['body_text']),
                Paragraph("关系:", self.pdf_styles['body_text']),
                Paragraph(employee.emergency_relationship or "-" if hasattr(employee, 'emergency_relationship') else "-", self.pdf_styles['body_text'])
            ],
            [
                Paragraph("联系电话:", self.pdf_styles['body_text']),
                Paragraph(employee.emergency_phone or "-" if hasattr(employee, 'emergency_phone') else "-", self.pdf_styles['body_text']),
                Paragraph("地址:", self.pdf_styles['body_text']),
                Paragraph(employee.emergency_address or "-" if hasattr(employee, 'emergency_address') else "-", self.pdf_styles['body_text'])
            ]
        ]
        
        emergency_table = self.create_info_table(emergency_info)
        content.append(emergency_table)
        content.append(Spacer(1, 10*mm))
        
        # 工作履历
        content.append(Paragraph("四、工作履历", self.pdf_styles['section_title']))
        
        # 这里可以添加工作履历表格
        work_headers = ["开始日期", "结束日期", "职位", "部门", "工作内容", "备注"]
        work_data = [
            [
                self.format_date(employee.hire_date) if hasattr(employee, 'hire_date') and employee.hire_date else "-",
                "至今" if employee.is_active else self.format_date(employee.leave_date) if hasattr(employee, 'leave_date') and employee.leave_date else "-",
                employee.position or "-",
                employee.department or "-",
                "食堂工作",
                ""
            ]
        ]
        
        work_table = self.create_professional_table(
            work_data,
            work_headers,
            col_widths=[25*mm, 25*mm, 30*mm, 25*mm, 50*mm, 30*mm]
        )
        
        content.append(work_table)
        content.append(Spacer(1, 10*mm))
        
        # 培训记录
        content.append(Paragraph("五、培训记录", self.pdf_styles['section_title']))
        
        training_headers = ["培训日期", "培训内容", "培训时长", "培训结果", "备注"]
        training_data = [
            [
                self.format_date(employee.food_safety_training) if hasattr(employee, 'food_safety_training') and employee.food_safety_training else "-",
                "食品安全培训",
                "4小时",
                "合格",
                ""
            ]
        ]
        
        training_table = self.create_professional_table(
            training_data,
            training_headers,
            col_widths=[30*mm, 50*mm, 25*mm, 25*mm, 35*mm]
        )
        
        content.append(training_table)
        content.append(Spacer(1, 10*mm))
        
        # 备注信息
        if hasattr(employee, 'notes') and employee.notes:
            content.append(Paragraph("六、备注信息", self.pdf_styles['section_title']))
            content.append(Paragraph(employee.notes, self.pdf_styles['body_text']))
        
        # 创建页脚
        signatures = [
            {'label': '人事专员', 'value': ""},
            {'label': '部门主管', 'value': ""},
            {'label': '人事主管', 'value': ""}
        ]
        
        self.create_pdf_footer(content, signatures=signatures)
        
        # 生成PDF
        doc.build(content)
        
        # 返回相对路径
        return os.path.join('pdf', 'employee_profiles', filename)
    
    def generate_attendance_summary_pdf(self, attendance_data, user_area, year, month):
        """生成考勤汇总PDF"""
        # 创建PDF保存目录
        pdf_dir = self.create_pdf_dir('attendance_reports')
        
        # 生成PDF文件名
        filename = f"考勤汇总_{year}年{month:02d}月_{datetime.now().strftime('%Y%m%d%H%M%S')}.pdf"
        pdf_path = os.path.join(pdf_dir, filename)
        
        # 创建PDF文档
        doc = SimpleDocTemplate(
            pdf_path,
            pagesize=landscape(A4),  # 横向布局
            rightMargin=15*mm,
            leftMargin=15*mm,
            topMargin=15*mm,
            bottomMargin=15*mm
        )
        
        content = []
        
        # 创建页眉
        self.create_pdf_header(
            content,
            title="员工考勤汇总表",
            subtitle=f"{year}年{month}月",
            company_name=user_area.name,
            report_date=datetime.now().date()
        )
        
        # 考勤汇总表
        headers = ["姓名", "工号", "部门", "应出勤", "实际出勤", "迟到", "早退", "请假", "旷工", "加班", "出勤率"]
        
        # 准备考勤数据
        attendance_table_data = []
        total_should_attend = 0
        total_actual_attend = 0
        
        for record in attendance_data:
            should_attend = record.get('should_attend', 0)
            actual_attend = record.get('actual_attend', 0)
            attendance_rate = f"{(actual_attend / should_attend * 100):.1f}%" if should_attend > 0 else "0.0%"
            
            total_should_attend += should_attend
            total_actual_attend += actual_attend
            
            attendance_table_data.append([
                record.get('name', ''),
                record.get('employee_id', ''),
                record.get('department', ''),
                str(should_attend),
                str(actual_attend),
                str(record.get('late_count', 0)),
                str(record.get('early_leave_count', 0)),
                str(record.get('leave_count', 0)),
                str(record.get('absent_count', 0)),
                str(record.get('overtime_count', 0)),
                attendance_rate
            ])
        
        # 添加合计行
        total_attendance_rate = f"{(total_actual_attend / total_should_attend * 100):.1f}%" if total_should_attend > 0 else "0.0%"
        attendance_table_data.append([
            "合计", "", "",
            str(total_should_attend),
            str(total_actual_attend),
            "", "", "", "", "",
            total_attendance_rate
        ])
        
        # 创建考勤表格
        attendance_table = self.create_professional_table(
            attendance_table_data,
            headers,
            col_widths=[25*mm, 20*mm, 25*mm, 20*mm, 20*mm, 15*mm, 15*mm, 15*mm, 15*mm, 15*mm, 20*mm],
            has_total_row=True
        )
        
        content.append(attendance_table)
        content.append(Spacer(1, 10*mm))
        
        # 考勤统计
        content.append(Paragraph("考勤统计分析", self.pdf_styles['section_title']))
        
        stats_text = f"""
        本月考勤情况总结：
        • 应出勤总天数：{total_should_attend}天
        • 实际出勤总天数：{total_actual_attend}天
        • 整体出勤率：{total_attendance_rate}
        • 考勤情况：{'良好' if float(total_attendance_rate.rstrip('%')) >= 95 else '一般' if float(total_attendance_rate.rstrip('%')) >= 90 else '需改进'}
        """
        
        content.append(Paragraph(stats_text, self.pdf_styles['body_text']))
        
        # 创建页脚
        signatures = [
            {'label': '统计员', 'value': ""},
            {'label': '人事主管', 'value': ""},
            {'label': '部门负责人', 'value': ""}
        ]
        
        self.create_pdf_footer(content, signatures=signatures)
        
        # 生成PDF
        doc.build(content)
        
        # 返回相对路径
        return os.path.join('pdf', 'attendance_reports', filename)
