{% extends "base.html" %}

{% block title %}先进先出智能提醒{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-sort-amount-down"></i> 先进先出智能提醒
                    </h3>
                    <div class="card-tools">
                        <button type="button" class="btn btn-primary btn-sm" onclick="refreshRecommendations()">
                            <i class="fas fa-sync"></i> 刷新推荐
                        </button>
                        <a href="{{ url_for('inventory.index') }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> 返回库存
                        </a>
                    </div>
                </div>
                
                <div class="card-body">
                    <!-- 优先库存汇总 -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="info-box bg-info">
                                <span class="info-box-icon"><i class="fas fa-star"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">优先使用项目</span>
                                    <span class="info-box-number">{{ priority_summary.total_items }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box bg-warning">
                                <span class="info-box-icon"><i class="fas fa-exclamation-triangle"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">紧急处理</span>
                                    <span class="info-box-number">{{ priority_summary.urgent_items|length }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box bg-success">
                                <span class="info-box-icon"><i class="fas fa-list"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">分类数量</span>
                                    <span class="info-box-number">{{ priority_summary.by_category|length }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box bg-primary">
                                <span class="info-box-icon"><i class="fas fa-chart-line"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">推荐项目</span>
                                    <span class="info-box-number">{{ recommendations|length }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 紧急处理项目 -->
                    {% if priority_summary.urgent_items %}
                    <div class="alert alert-danger">
                        <h5><i class="fas fa-exclamation-triangle"></i> 紧急处理项目</h5>
                        <div class="row">
                            {% for item in priority_summary.urgent_items %}
                            <div class="col-md-4 mb-2">
                                <div class="card card-danger">
                                    <div class="card-body p-2">
                                        <strong>{{ item.ingredient_name }}</strong><br>
                                        <small>数量: {{ item.quantity }} {{ item.unit }}</small><br>
                                        <small>批次: {{ item.batch_number }}</small><br>
                                        <span class="badge badge-danger">{{ item.days_to_expire }}天后过期</span>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}
                    
                    <!-- FIFO推荐列表 -->
                    <div class="row">
                        {% for recommendation in recommendations %}
                        <div class="col-md-6 mb-4">
                            <div class="card {% if recommendation.priority == 'urgent' %}card-danger{% elif recommendation.priority == 'high' %}card-warning{% elif recommendation.priority == 'medium' %}card-info{% else %}card-secondary{% endif %}">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">
                                        {{ recommendation.ingredient_name }}
                                        <span class="badge {% if recommendation.priority == 'urgent' %}badge-danger{% elif recommendation.priority == 'high' %}badge-warning{% elif recommendation.priority == 'medium' %}badge-info{% else %}badge-secondary{% endif %} float-right">
                                            {% if recommendation.days_to_expire < 0 %}
                                                已过期
                                            {% else %}
                                                {{ recommendation.days_to_expire }}天后过期
                                            {% endif %}
                                        </span>
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="row mb-3">
                                        <div class="col-6">
                                            <small class="text-muted">总库存</small><br>
                                            <strong>{{ recommendation.total_quantity|round(1) }}</strong>
                                        </div>
                                        <div class="col-6">
                                            <small class="text-muted">建议使用</small><br>
                                            <strong class="text-primary">{{ recommendation.suggested_quantity|round(1) }}</strong>
                                        </div>
                                    </div>
                                    
                                    <p class="text-sm">{{ recommendation.message }}</p>
                                    
                                    <!-- 库存详情 -->
                                    <div class="mb-3">
                                        <small class="text-muted">库存明细:</small>
                                        {% for inventory in recommendation.inventories %}
                                        <div class="border rounded p-2 mb-1">
                                            <div class="row">
                                                <div class="col-4">
                                                    <small>批次: {{ inventory.batch_number }}</small>
                                                </div>
                                                <div class="col-4">
                                                    <small>数量: {{ inventory.quantity }} {{ inventory.unit }}</small>
                                                </div>
                                                <div class="col-4">
                                                    <small>{{ inventory.days_to_expire }}天后过期</small>
                                                </div>
                                            </div>
                                            {% if inventory.storage_location %}
                                            <small class="text-muted">位置: {{ inventory.storage_location }}</small>
                                            {% endif %}
                                        </div>
                                        {% endfor %}
                                    </div>
                                    
                                    <!-- 推荐操作 -->
                                    <div class="btn-group-vertical w-100">
                                        {% for action in recommendation.actions %}
                                        <button type="button" class="btn btn-sm {% if action == '立即报废' %}btn-danger{% elif action == '优先使用' %}btn-warning{% elif action == '加入消耗计划' %}btn-success{% else %}btn-info{% endif %} mb-1" 
                                                onclick="performAction('{{ recommendation.ingredient_id }}', '{{ action }}', {{ recommendation.inventories|tojson }})">
                                            <i class="fas {% if action == '立即报废' %}fa-trash{% elif action == '优先使用' %}fa-star{% elif action == '加入消耗计划' %}fa-plus{% elif action == '申请退货' %}fa-undo{% elif action == '转移处理' %}fa-exchange-alt{% else %}fa-cog{% endif %}"></i>
                                            {{ action }}
                                        </button>
                                        {% endfor %}
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% else %}
                        <div class="col-12">
                            <div class="alert alert-info text-center">
                                <i class="fas fa-info-circle"></i> 暂无需要特别关注的库存项目
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 操作确认对话框 -->
<div class="modal fade" id="actionModal" tabindex="-1" role="dialog" aria-labelledby="actionModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="actionModalLabel">确认操作</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p id="actionMessage"></p>
                <div id="actionDetails"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="confirmActionBtn">确认</button>
            </div>
        </div>
    </div>
</div>

<script>
let currentAction = null;

// 刷新推荐
function refreshRecommendations() {
    location.reload();
}

// 执行操作
function performAction(ingredientId, action, inventories) {
    currentAction = {
        ingredientId: ingredientId,
        action: action,
        inventories: inventories
    };
    
    let message = '';
    let details = '';
    
    switch(action) {
        case '优先使用':
            message = '确认将这些库存标记为优先使用吗？';
            details = '标记后，这些库存将在消耗计划中优先安排使用。';
            break;
        case '加入消耗计划':
            message = '确认将这些食材加入消耗计划吗？';
            details = '系统将自动创建消耗计划，优先使用临期库存。';
            break;
        case '立即报废':
            message = '确认立即报废这些库存吗？';
            details = '此操作将创建报废处理记录，请谨慎操作。';
            break;
        case '申请退货':
            message = '确认申请退货吗？';
            details = '系统将创建退货申请，需要联系供应商处理。';
            break;
        case '转移处理':
            message = '确认转移处理这些库存吗？';
            details = '请在确认后指定转移目标和用途。';
            break;
        default:
            message = `确认执行"${action}"操作吗？`;
            details = '';
    }
    
    document.getElementById('actionMessage').textContent = message;
    document.getElementById('actionDetails').textContent = details;
    
    $('#actionModal').modal('show');
}

// 确认操作
document.getElementById('confirmActionBtn').addEventListener('click', function() {
    if (!currentAction) return;
    
    const action = currentAction.action;
    const inventories = currentAction.inventories;
    
    if (action === '优先使用') {
        // 标记为优先使用
        markAsPriority(inventories);
    } else if (action === '加入消耗计划') {
        // 加入消耗计划
        addToConsumptionPlan(currentAction.ingredientId, inventories);
    } else if (['立即报废', '申请退货', '转移处理'].includes(action)) {
        // 创建处理记录
        createHandlingRecord(inventories, action);
    } else {
        alert('功能开发中...');
    }
    
    $('#actionModal').modal('hide');
});

// 标记为优先使用
function markAsPriority(inventories) {
    const promises = inventories.map(inventory => {
        return fetch('/inventory/api/mark-priority', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                inventory_id: inventory.id,
                priority_level: '优先使用'
            })
        });
    });
    
    Promise.all(promises)
        .then(responses => Promise.all(responses.map(r => r.json())))
        .then(results => {
            const successCount = results.filter(r => r.success).length;
            alert(`成功标记 ${successCount} 项库存为优先使用`);
            location.reload();
        })
        .catch(error => {
            console.error('Error:', error);
            alert('标记失败');
        });
}

// 加入消耗计划
function addToConsumptionPlan(ingredientId, inventories) {
    // 这里应该跳转到消耗计划页面或打开创建对话框
    const totalQuantity = inventories.reduce((sum, inv) => sum + inv.quantity, 0);
    const url = `/consumption-plan/create?ingredient_id=${ingredientId}&suggested_quantity=${totalQuantity}&priority=true`;
    window.open(url, '_blank');
}

// 创建处理记录
function createHandlingRecord(inventories, handlingType) {
    const promises = inventories.map(inventory => {
        return fetch('/expiry-handling/create', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                inventory_id: inventory.id,
                handling_type: handlingType === '立即报废' ? '报废' : handlingType === '申请退货' ? '退货' : '转移',
                quantity: inventory.quantity,
                planned_date: new Date().toISOString().split('T')[0],
                reason: `FIFO智能推荐：${handlingType}`
            })
        });
    });
    
    Promise.all(promises)
        .then(responses => Promise.all(responses.map(r => r.json())))
        .then(results => {
            const successCount = results.filter(r => r.success).length;
            alert(`成功创建 ${successCount} 条处理记录`);
            location.reload();
        })
        .catch(error => {
            console.error('Error:', error);
            alert('创建处理记录失败');
        });
}
</script>

{% endblock %}
