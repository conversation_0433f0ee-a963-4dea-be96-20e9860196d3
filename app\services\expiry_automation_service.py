"""
过期食材自动化处理服务
"""
from datetime import datetime, date, timedelta
from app import db
from app.models import Inventory, ExpiryNotification, ExpiryHandling, User, StockIn, StockInItem
from sqlalchemy import and_, or_
import logging

logger = logging.getLogger(__name__)

class ExpiryAutomationService:
    """过期食材自动化处理服务"""
    
    def __init__(self):
        self.notification_levels = {
            'expired': '紧急',
            'expiring_3': '高',
            'expiring_7': '中',
            'expiring_15': '低',
            'expiring_30': '低'
        }
    
    def run_daily_automation(self):
        """运行每日自动化任务"""
        try:
            logger.info("开始执行过期食材每日自动化任务")
            
            # 1. 自动更新过期状态
            expired_count = self.auto_update_expired_status()
            
            # 2. 生成过期通知
            notification_count = self.generate_expiry_notifications()
            
            # 3. 自动计算损失金额
            loss_calculation_count = self.auto_calculate_losses()
            
            # 4. 清理过期通知
            cleaned_count = self.cleanup_old_notifications()
            
            logger.info(f"每日自动化任务完成: 过期状态更新{expired_count}条, 生成通知{notification_count}条, "
                       f"损失计算{loss_calculation_count}条, 清理通知{cleaned_count}条")
            
            return {
                'success': True,
                'expired_count': expired_count,
                'notification_count': notification_count,
                'loss_calculation_count': loss_calculation_count,
                'cleaned_count': cleaned_count
            }
            
        except Exception as e:
            logger.error(f"每日自动化任务执行失败: {str(e)}")
            return {'success': False, 'error': str(e)}
    
    def auto_update_expired_status(self):
        """自动更新过期库存状态"""
        try:
            today = date.today()
            
            # 查找已过期但状态未更新的库存
            expired_inventories = Inventory.query.filter(
                and_(
                    Inventory.expiry_date < today,
                    Inventory.status != '已过期',
                    Inventory.quantity > 0
                )
            ).all()
            
            updated_count = 0
            for inventory in expired_inventories:
                old_status = inventory.status
                inventory.status = '已过期'
                
                # 记录状态变更日志
                logger.info(f"库存ID {inventory.id} ({inventory.ingredient.name}) "
                           f"状态从 '{old_status}' 自动更新为 '已过期'")
                
                updated_count += 1
            
            if updated_count > 0:
                db.session.commit()
                logger.info(f"自动更新了 {updated_count} 条过期库存状态")
            
            return updated_count
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"自动更新过期状态失败: {str(e)}")
            return 0
    
    def generate_expiry_notifications(self):
        """生成过期预警通知"""
        try:
            today = date.today()
            notification_count = 0
            
            # 定义预警时间点
            warning_dates = {
                'expired': today,  # 已过期
                'expiring_3': today + timedelta(days=3),   # 3天内过期
                'expiring_7': today + timedelta(days=7),   # 7天内过期
                'expiring_15': today + timedelta(days=15), # 15天内过期
                'expiring_30': today + timedelta(days=30)  # 30天内过期
            }
            
            for warning_type, warning_date in warning_dates.items():
                # 查找需要预警的库存
                if warning_type == 'expired':
                    # 已过期的库存
                    inventories = Inventory.query.filter(
                        and_(
                            Inventory.expiry_date < today,
                            Inventory.status == '已过期',
                            Inventory.quantity > 0
                        )
                    ).all()
                else:
                    # 即将过期的库存
                    inventories = Inventory.query.filter(
                        and_(
                            Inventory.expiry_date == warning_date,
                            Inventory.status != '已过期',
                            Inventory.quantity > 0
                        )
                    ).all()
                
                for inventory in inventories:
                    # 检查是否已经发送过相同类型的通知
                    existing_notification = ExpiryNotification.query.filter(
                        and_(
                            ExpiryNotification.inventory_id == inventory.id,
                            ExpiryNotification.notification_type == self._get_notification_type(warning_type),
                            ExpiryNotification.created_at >= today
                        )
                    ).first()
                    
                    if not existing_notification:
                        # 创建新通知
                        notification = self._create_notification(inventory, warning_type)
                        if notification:
                            db.session.add(notification)
                            notification_count += 1
            
            if notification_count > 0:
                db.session.commit()
                logger.info(f"生成了 {notification_count} 条过期预警通知")
            
            return notification_count
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"生成过期通知失败: {str(e)}")
            return 0
    
    def auto_calculate_losses(self):
        """自动计算损失金额"""
        try:
            # 查找需要计算损失的处理记录
            handlings = ExpiryHandling.query.filter(
                and_(
                    ExpiryHandling.total_loss.is_(None),
                    ExpiryHandling.handling_type.in_(['报废', '过期处理'])
                )
            ).all()
            
            calculated_count = 0
            for handling in handlings:
                # 获取库存的成本信息
                cost_info = self._get_inventory_cost(handling.inventory_id)
                if cost_info and cost_info['unit_cost']:
                    handling.unit_cost = cost_info['unit_cost']
                    handling.calculate_total_loss()
                    calculated_count += 1
                    
                    logger.info(f"处理记录ID {handling.id} 自动计算损失金额: "
                               f"{handling.quantity} × {handling.unit_cost} = {handling.total_loss}")
            
            if calculated_count > 0:
                db.session.commit()
                logger.info(f"自动计算了 {calculated_count} 条处理记录的损失金额")
            
            return calculated_count
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"自动计算损失金额失败: {str(e)}")
            return 0
    
    def cleanup_old_notifications(self, days=30):
        """清理过期的通知"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days)
            
            # 删除已读且超过指定天数的通知
            deleted_count = ExpiryNotification.query.filter(
                and_(
                    ExpiryNotification.is_read == True,
                    ExpiryNotification.created_at < cutoff_date
                )
            ).delete()
            
            if deleted_count > 0:
                db.session.commit()
                logger.info(f"清理了 {deleted_count} 条过期通知")
            
            return deleted_count
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"清理过期通知失败: {str(e)}")
            return 0
    
    def _get_notification_type(self, warning_type):
        """获取通知类型"""
        if warning_type == 'expired':
            return '过期通知'
        else:
            return '临期预警'
    
    def _create_notification(self, inventory, warning_type):
        """创建通知"""
        try:
            days_to_expiry = (inventory.expiry_date - date.today()).days
            
            if warning_type == 'expired':
                message = f"食材 {inventory.ingredient.name} (批次: {inventory.batch_number}) 已过期 {abs(days_to_expiry)} 天，请及时处理"
            else:
                message = f"食材 {inventory.ingredient.name} (批次: {inventory.batch_number}) 将在 {days_to_expiry} 天后过期，请注意使用"
            
            # 获取接收通知的用户（这里简化为库存管理员）
            recipient = self._get_notification_recipient(inventory)
            if not recipient:
                return None
            
            notification = ExpiryNotification(
                inventory_id=inventory.id,
                notification_type=self._get_notification_type(warning_type),
                notification_level=self.notification_levels.get(warning_type, '低'),
                days_to_expiry=days_to_expiry,
                message=message,
                recipient_id=recipient.id,
                area_id=inventory.warehouse_info.area_id if inventory.warehouse_info else None
            )
            
            return notification
            
        except Exception as e:
            logger.error(f"创建通知失败: {str(e)}")
            return None
    
    def _get_notification_recipient(self, inventory):
        """获取通知接收人"""
        try:
            # 简化逻辑：获取该区域的第一个管理员用户
            # 实际应用中可以根据角色、部门等更精确地确定接收人
            if inventory.warehouse_info and inventory.warehouse_info.area_id:
                recipient = User.query.filter(
                    User.area_id == inventory.warehouse_info.area_id
                ).first()
                return recipient
            
            # 如果没有找到特定区域的用户，返回系统管理员
            return User.query.filter(User.is_admin == True).first()
            
        except Exception as e:
            logger.error(f"获取通知接收人失败: {str(e)}")
            return None
    
    def _get_inventory_cost(self, inventory_id):
        """获取库存成本信息"""
        try:
            # 从入库记录中获取最近的成本信息
            inventory = Inventory.query.get(inventory_id)
            if not inventory:
                return None
            
            # 查找相关的入库记录
            stock_in_item = StockInItem.query.join(StockIn).filter(
                and_(
                    StockInItem.ingredient_id == inventory.ingredient_id,
                    StockInItem.batch_number == inventory.batch_number,
                    StockIn.status == '已入库'
                )
            ).order_by(StockIn.stock_in_date.desc()).first()
            
            if stock_in_item and stock_in_item.unit_price:
                return {
                    'unit_cost': float(stock_in_item.unit_price),
                    'source': 'stock_in'
                }
            
            # 如果没有找到入库记录，使用平均成本
            avg_cost = self._calculate_average_cost(inventory.ingredient_id)
            if avg_cost:
                return {
                    'unit_cost': avg_cost,
                    'source': 'average'
                }
            
            return None
            
        except Exception as e:
            logger.error(f"获取库存成本失败: {str(e)}")
            return None
    
    def _calculate_average_cost(self, ingredient_id):
        """计算食材平均成本"""
        try:
            # 获取最近30天的入库记录计算平均成本
            thirty_days_ago = date.today() - timedelta(days=30)
            
            avg_result = db.session.query(
                db.func.avg(StockInItem.unit_price)
            ).join(StockIn).filter(
                and_(
                    StockInItem.ingredient_id == ingredient_id,
                    StockInItem.unit_price.isnot(None),
                    StockIn.stock_in_date >= thirty_days_ago,
                    StockIn.status == '已入库'
                )
            ).scalar()
            
            return float(avg_result) if avg_result else None
            
        except Exception as e:
            logger.error(f"计算平均成本失败: {str(e)}")
            return None

# 创建服务实例
expiry_automation_service = ExpiryAutomationService()
