"""
专业供应商管理模板生成器
为供应商管理模块提供专业、优雅的打印和导出模板
"""

from flask import current_app, send_file
from .professional_template_generator import ProfessionalTemplateGenerator
from reportlab.lib.pagesizes import A4, landscape
from reportlab.lib import colors
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, KeepTogether
from reportlab.lib.units import mm
from decimal import Decimal
import os
from datetime import datetime, timedelta
from sqlalchemy import func

class ProfessionalSupplierTemplates(ProfessionalTemplateGenerator):
    """专业供应商管理模板生成器"""
    
    def generate_supplier_list_excel(self, suppliers, user_area):
        """生成供应商信息清单Excel"""
        # 准备数据
        data = []
        for supplier in suppliers:
            data.append([
                supplier.name,
                supplier.contact_person or "",
                supplier.phone or "",
                supplier.email or "",
                supplier.address or "",
                supplier.category.name if supplier.category else "",
                '启用' if supplier.is_active else '禁用',
                supplier.credit_rating if hasattr(supplier, 'credit_rating') else "",
                self.format_date(supplier.created_at),
                supplier.notes or ""
            ])
        
        headers = ["供应商名称", "联系人", "联系电话", "邮箱", "地址", "分类", "状态", "信用等级", "创建日期", "备注"]
        
        # 创建Excel文件
        temp_file, filename = self.create_professional_excel(
            data=data,
            headers=headers,
            title="供应商信息清单",
            subtitle="供应商基础信息统计表",
            company_name=user_area.name,
            report_date=datetime.now().date()
        )
        
        return send_file(
            temp_file,
            as_attachment=True,
            download_name=filename,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
    
    def generate_supplier_statistics_excel(self, supplier_stats, user_area):
        """生成供应商采购统计Excel"""
        # 准备数据
        data = []
        total_orders = 0
        total_amount = Decimal('0')
        
        for stat in supplier_stats:
            order_count = stat.order_count or 0
            amount = stat.total_amount or Decimal('0')
            
            total_orders += order_count
            total_amount += amount
            
            data.append([
                stat.name,
                order_count,
                self.format_currency(amount),
                self.format_currency(amount / order_count) if order_count > 0 else "0.00",
                self.format_date(stat.last_order_date) if stat.last_order_date else "",
                self.format_date(stat.first_order_date) if hasattr(stat, 'first_order_date') and stat.first_order_date else "",
                f"{order_count / total_orders * 100:.1f}%" if total_orders > 0 else "0.0%"
            ])
        
        # 添加合计行
        data.append([
            "合计",
            total_orders,
            self.format_currency(total_amount),
            self.format_currency(total_amount / total_orders) if total_orders > 0 else "0.00",
            "",
            "",
            "100.0%"
        ])
        
        headers = ["供应商名称", "订单数量", "采购总额", "平均订单金额", "最后订单日期", "首次订单日期", "订单占比"]
        
        # 创建Excel文件
        temp_file, filename = self.create_professional_excel(
            data=data,
            headers=headers,
            title="供应商采购统计报表",
            subtitle="供应商业务合作统计分析",
            company_name=user_area.name,
            report_date=datetime.now().date()
        )
        
        return send_file(
            temp_file,
            as_attachment=True,
            download_name=filename,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
    
    def generate_supplier_evaluation_pdf(self, supplier, evaluation_data, user_area):
        """生成供应商评估报告PDF"""
        # 创建PDF保存目录
        pdf_dir = self.create_pdf_dir('supplier_reports')
        
        # 生成PDF文件名
        filename = f"供应商评估报告_{supplier.name}_{datetime.now().strftime('%Y%m%d%H%M%S')}.pdf"
        pdf_path = os.path.join(pdf_dir, filename)
        
        # 创建PDF文档
        doc = SimpleDocTemplate(
            pdf_path,
            pagesize=A4,
            rightMargin=20*mm,
            leftMargin=20*mm,
            topMargin=20*mm,
            bottomMargin=20*mm
        )
        
        content = []
        
        # 创建页眉
        self.create_pdf_header(
            content,
            title="供应商评估报告",
            subtitle=f"供应商：{supplier.name}",
            company_name=user_area.name,
            report_date=datetime.now().date()
        )
        
        # 供应商基本信息
        content.append(Paragraph("一、供应商基本信息", self.pdf_styles['section_title']))
        
        supplier_info = [
            [
                Paragraph("供应商名称:", self.pdf_styles['body_text']),
                Paragraph(supplier.name, self.pdf_styles['body_text']),
                Paragraph("联系人:", self.pdf_styles['body_text']),
                Paragraph(supplier.contact_person or "-", self.pdf_styles['body_text'])
            ],
            [
                Paragraph("联系电话:", self.pdf_styles['body_text']),
                Paragraph(supplier.phone or "-", self.pdf_styles['body_text']),
                Paragraph("邮箱:", self.pdf_styles['body_text']),
                Paragraph(supplier.email or "-", self.pdf_styles['body_text'])
            ],
            [
                Paragraph("地址:", self.pdf_styles['body_text']),
                Paragraph(supplier.address or "-", self.pdf_styles['body_text']),
                Paragraph("分类:", self.pdf_styles['body_text']),
                Paragraph(supplier.category.name if supplier.category else "-", self.pdf_styles['body_text'])
            ],
            [
                Paragraph("合作开始:", self.pdf_styles['body_text']),
                Paragraph(self.format_date(supplier.created_at), self.pdf_styles['body_text']),
                Paragraph("当前状态:", self.pdf_styles['body_text']),
                Paragraph('启用' if supplier.is_active else '禁用', self.pdf_styles['body_text'])
            ]
        ]
        
        info_table = self.create_info_table(supplier_info)
        content.append(info_table)
        content.append(Spacer(1, 10*mm))
        
        # 评估指标
        content.append(Paragraph("二、评估指标", self.pdf_styles['section_title']))
        
        # 评估数据表格
        evaluation_headers = ["评估项目", "评分", "权重", "加权得分", "评价"]
        evaluation_table_data = []
        
        total_score = 0
        total_weight = 0
        
        for item in evaluation_data:
            score = item.get('score', 0)
            weight = item.get('weight', 0)
            weighted_score = score * weight / 100
            total_score += weighted_score
            total_weight += weight
            
            evaluation_table_data.append([
                item.get('name', ''),
                f"{score}分",
                f"{weight}%",
                f"{weighted_score:.1f}分",
                item.get('comment', '')
            ])
        
        # 添加总分行
        evaluation_table_data.append([
            "综合评分", "", f"{total_weight}%", f"{total_score:.1f}分", 
            "优秀" if total_score >= 90 else "良好" if total_score >= 80 else "一般" if total_score >= 70 else "需改进"
        ])
        
        evaluation_table = self.create_professional_table(
            evaluation_table_data,
            evaluation_headers,
            col_widths=[40*mm, 20*mm, 20*mm, 25*mm, 65*mm],
            has_total_row=True
        )
        
        content.append(evaluation_table)
        content.append(Spacer(1, 10*mm))
        
        # 合作统计
        content.append(Paragraph("三、合作统计", self.pdf_styles['section_title']))
        
        # 这里可以添加具体的统计数据
        stats_info = [
            [
                Paragraph("订单总数:", self.pdf_styles['body_text']),
                Paragraph("0", self.pdf_styles['body_text']),  # 实际应从数据库获取
                Paragraph("采购总额:", self.pdf_styles['body_text']),
                Paragraph("¥0.00", self.pdf_styles['body_text'])
            ],
            [
                Paragraph("平均订单金额:", self.pdf_styles['body_text']),
                Paragraph("¥0.00", self.pdf_styles['body_text']),
                Paragraph("最后订单日期:", self.pdf_styles['body_text']),
                Paragraph("-", self.pdf_styles['body_text'])
            ]
        ]
        
        stats_table = self.create_info_table(stats_info)
        content.append(stats_table)
        content.append(Spacer(1, 10*mm))
        
        # 评估结论
        content.append(Paragraph("四、评估结论", self.pdf_styles['section_title']))
        
        conclusion_text = f"""
        根据本次评估，{supplier.name}的综合得分为{total_score:.1f}分。
        
        主要优势：
        • 供应商基本信息完整
        • 合作关系稳定
        
        改进建议：
        • 建议定期更新供应商信息
        • 加强质量管控
        • 优化配送时效
        
        综合评价：{'优秀' if total_score >= 90 else '良好' if total_score >= 80 else '一般' if total_score >= 70 else '需改进'}
        """
        
        content.append(Paragraph(conclusion_text, self.pdf_styles['body_text']))
        
        # 创建页脚
        signatures = [
            {'label': '评估人', 'value': ""},
            {'label': '审核人', 'value': ""},
            {'label': '批准人', 'value': ""}
        ]
        
        self.create_pdf_footer(content, signatures=signatures)
        
        # 生成PDF
        doc.build(content)
        
        # 返回相对路径
        return os.path.join('pdf', 'supplier_reports', filename)
    
    def generate_supplier_contract_pdf(self, supplier, contract_data, user_area):
        """生成供应商合同PDF"""
        # 创建PDF保存目录
        pdf_dir = self.create_pdf_dir('contracts')
        
        # 生成PDF文件名
        filename = f"供应商合同_{supplier.name}_{datetime.now().strftime('%Y%m%d%H%M%S')}.pdf"
        pdf_path = os.path.join(pdf_dir, filename)
        
        # 创建PDF文档
        doc = SimpleDocTemplate(
            pdf_path,
            pagesize=A4,
            rightMargin=25*mm,
            leftMargin=25*mm,
            topMargin=25*mm,
            bottomMargin=25*mm
        )
        
        content = []
        
        # 创建页眉
        self.create_pdf_header(
            content,
            title="食材供应合同",
            company_name=user_area.name,
            report_date=datetime.now().date()
        )
        
        # 合同基本信息
        content.append(Paragraph("甲方（采购方）", self.pdf_styles['section_title']))
        content.append(Paragraph(f"单位名称：{user_area.name}", self.pdf_styles['body_text']))
        content.append(Spacer(1, 5*mm))
        
        content.append(Paragraph("乙方（供应方）", self.pdf_styles['section_title']))
        content.append(Paragraph(f"单位名称：{supplier.name}", self.pdf_styles['body_text']))
        content.append(Paragraph(f"联系人：{supplier.contact_person or ''}", self.pdf_styles['body_text']))
        content.append(Paragraph(f"联系电话：{supplier.phone or ''}", self.pdf_styles['body_text']))
        content.append(Paragraph(f"地址：{supplier.address or ''}", self.pdf_styles['body_text']))
        content.append(Spacer(1, 10*mm))
        
        # 合同条款
        content.append(Paragraph("合同条款", self.pdf_styles['section_title']))
        
        contract_terms = [
            "一、供应范围：乙方向甲方供应食堂所需的各类食材。",
            "二、质量标准：所供应的食材必须符合国家食品安全标准。",
            "三、配送要求：乙方应按照甲方要求的时间和地点进行配送。",
            "四、价格条款：价格以双方确认的采购订单为准。",
            "五、付款方式：货到验收合格后，按约定期限付款。",
            "六、违约责任：任何一方违约，应承担相应的违约责任。",
            "七、合同期限：本合同有效期为一年，到期可续签。",
            "八、其他约定：双方应诚信履行合同义务。"
        ]
        
        for term in contract_terms:
            content.append(Paragraph(term, self.pdf_styles['body_text']))
            content.append(Spacer(1, 3*mm))
        
        content.append(Spacer(1, 15*mm))
        
        # 创建页脚（签名栏）
        signatures = [
            {'label': '甲方代表', 'value': ""},
            {'label': '乙方代表', 'value': ""},
            {'label': '签约日期', 'value': ""}
        ]
        
        self.create_pdf_footer(content, signatures=signatures)
        
        # 生成PDF
        doc.build(content)
        
        # 返回相对路径
        return os.path.join('pdf', 'contracts', filename)
