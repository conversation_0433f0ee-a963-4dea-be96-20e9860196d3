2025-06-20 20:17:40,515 INFO: 应用启动 - PID: 9656 [in C:\StudentsCMSSP\app\__init__.py:831]
2025-06-22 22:14:40,876 INFO: 应用启动 - PID: 9656 [in C:\StudentsCMSSP\app\__init__.py:868]
2025-06-22 22:16:11,618 ERROR: Exception on /employee/ [GET] [in C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py:838]
Traceback (most recent call last):
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 1473, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 882, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_cors\extension.py", line 194, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 880, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 865, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
  File "C:\StudentsCMSSP\app\employee\routes.py", line 139, in index
    return render_template(
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 1301, in render
    self.environment.handle_exception()
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 936, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "C:\StudentsCMSSP\app\templates\employee\index.html", line 1, in top-level template code
    {% extends 'base.html' %}
  File "C:\StudentsCMSSP\app\templates\base.html", line 481, in top-level template code
    {% block content %}{% endblock %}
  File "C:\StudentsCMSSP\app\templates\employee\index.html", line 262, in block 'content'
    <i class="fas fa-calendar-alt mr-2"></i>{{ moment().format('YYYY年MM月DD日 dddd') }}
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\utils.py", line 83, in from_obj
    if hasattr(obj, "jinja_pass_arg"):
jinja2.exceptions.UndefinedError: 'moment' is undefined
2025-06-22 22:16:25,733 ERROR: Exception on /employee/ [GET] [in C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py:838]
Traceback (most recent call last):
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 1473, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 882, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_cors\extension.py", line 194, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 880, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 865, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
  File "C:\StudentsCMSSP\app\employee\routes.py", line 139, in index
    return render_template(
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 1301, in render
    self.environment.handle_exception()
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 936, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "C:\StudentsCMSSP\app\templates\employee\index.html", line 1, in top-level template code
    {% extends 'base.html' %}
  File "C:\StudentsCMSSP\app\templates\base.html", line 481, in top-level template code
    {% block content %}{% endblock %}
  File "C:\StudentsCMSSP\app\templates\employee\index.html", line 262, in block 'content'
    <i class="fas fa-calendar-alt mr-2"></i>{{ moment().format('YYYY年MM月DD日 dddd') }}
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\utils.py", line 83, in from_obj
    if hasattr(obj, "jinja_pass_arg"):
jinja2.exceptions.UndefinedError: 'moment' is undefined
2025-06-22 22:16:54,535 ERROR: Exception on /employee/ [GET] [in C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py:838]
Traceback (most recent call last):
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 1473, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 882, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_cors\extension.py", line 194, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 880, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 865, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
  File "C:\StudentsCMSSP\app\employee\routes.py", line 139, in index
    return render_template(
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 1301, in render
    self.environment.handle_exception()
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 936, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "C:\StudentsCMSSP\app\templates\employee\index.html", line 1, in top-level template code
    {% extends 'base.html' %}
  File "C:\StudentsCMSSP\app\templates\base.html", line 481, in top-level template code
    {% block content %}{% endblock %}
  File "C:\StudentsCMSSP\app\templates\employee\index.html", line 262, in block 'content'
    <i class="fas fa-calendar-alt mr-2"></i>{{ moment().format('YYYY年MM月DD日 dddd') }}
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\utils.py", line 83, in from_obj
    if hasattr(obj, "jinja_pass_arg"):
jinja2.exceptions.UndefinedError: 'moment' is undefined
2025-06-22 22:17:05,773 ERROR: Exception on /employee/ [GET] [in C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py:838]
Traceback (most recent call last):
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 1473, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 882, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_cors\extension.py", line 194, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 880, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 865, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
  File "C:\StudentsCMSSP\app\employee\routes.py", line 139, in index
    return render_template(
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 1301, in render
    self.environment.handle_exception()
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 936, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "C:\StudentsCMSSP\app\templates\employee\index.html", line 1, in top-level template code
    {% extends 'base.html' %}
  File "C:\StudentsCMSSP\app\templates\base.html", line 481, in top-level template code
    {% block content %}{% endblock %}
  File "C:\StudentsCMSSP\app\templates\employee\index.html", line 262, in block 'content'
    <i class="fas fa-calendar-alt mr-2"></i>{{ moment().format('YYYY年MM月DD日 dddd') }}
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\utils.py", line 83, in from_obj
    if hasattr(obj, "jinja_pass_arg"):
jinja2.exceptions.UndefinedError: 'moment' is undefined
