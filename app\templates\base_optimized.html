<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}{{ project_name|default('智慧食堂平台') }}{% endblock %}</title>
    
    <!-- 基础CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='bootstrap/css/bootstrap.min.css') }}?v=1.0.0">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/all.min.css') }}?v=1.0.0">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/theme-colors.css') }}?v=1.0.0">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/left-right-layout.css') }}?v=1.0.0">
    
    <!-- 页面特定CSS -->
    {% block styles %}{% endblock %}
    
    <!-- CSP和安全脚本 -->
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/critical-handler-simple.js') }}"></script>
</head>
<body data-theme="{{ theme_color|default('primary') }}">
    <div class="layout-container">
        <!-- 左侧导航栏 -->
        <div class="sidebar">
            <div class="sidebar-header">
                <a class="sidebar-brand" href="{{ url_for('main.index') }}">
                    {% if system_logo %}
                    <img src="{{ system_logo }}" alt="{{ project_name }}" style="height: 24px; width: auto;">
                    {% endif %}
                    <div>
                        <div style="font-size: 0.95rem; font-weight: 600;">{{ project_name|default('智慧食堂平台') }}</div>
                        {% if current_user.is_authenticated and current_user.get_current_area() %}
                        <div style="font-size: 0.75rem; opacity: 0.8;">{{ current_user.get_current_area().name }}</div>
                        {% endif %}
                    </div>
                </a>
            </div>

            <div class="sidebar-nav">
                {% if current_user.is_authenticated %}
                    {% for menu_item in user_menu %}
                        {% if menu_item.children %}
                            <div class="nav-item">
                                <a class="nav-link" href="#" data-toggle="collapse" data-target="#{{ menu_item.id }}Submenu" aria-expanded="false">
                                    {% if menu_item.icon %}<i class="{{ menu_item.icon }}"></i>{% endif %}
                                    {{ menu_item.name }}
                                    <i class="fas fa-chevron-down ms-auto" style="font-size: 0.8rem;"></i>
                                </a>
                                <div class="collapse" id="{{ menu_item.id }}Submenu">
                                    <div style="padding-left: 1rem;">
                                        {% for child in menu_item.children %}
                                            {% if child.get('is_header') %}
                                                <div style="padding: 0.5rem 1rem; font-size: 0.8rem; opacity: 0.7; font-weight: 600;">{{ child.name }}</div>
                                            {% else %}
                                                <a class="nav-link" href="{{ url_for(child.url) if not child.get('url_params') else get_url(child) }}" style="padding: 0.5rem 1rem; font-size: 0.85rem;">
                                                    {% if child.icon %}<i class="{{ child.icon }}"></i>{% endif %}
                                                    {{ child.name }}
                                                </a>
                                            {% endif %}
                                        {% endfor %}
                                    </div>
                                </div>
                            </div>
                        {% else %}
                            <div class="nav-item">
                                <a class="nav-link" href="{{ url_for(menu_item.url) if not menu_item.get('url_params') else get_url(menu_item) }}">
                                    {% if menu_item.icon %}<i class="{{ menu_item.icon }}"></i>{% endif %}
                                    {{ menu_item.name }}
                                </a>
                            </div>
                        {% endif %}
                    {% endfor %}
                {% endif %}
            </div>
        </div>

        <!-- 右侧主内容区域 -->
        <div class="main-content">
            <!-- 顶部工具栏 -->
            <div class="top-toolbar">
                <div class="toolbar-left">
                    <button class="btn btn-outline-secondary mobile-toggle" type="button" onclick="toggleSidebar()">
                        <i class="fas fa-bars"></i>
                    </button>
                </div>

                <div class="toolbar-right">
                    <!-- 主题切换器 -->
                    <div class="dropdown">
                        <a class="btn btn-outline-light" href="#" id="themeDropdown" role="button" data-toggle="dropdown" title="切换主题">
                            <i class="fas fa-palette"></i>
                        </a>
                        <div class="dropdown-menu dropdown-menu-right theme-switcher-panel" style="min-width: 200px;">
                            <h6 class="dropdown-header">主题选择</h6>
                            <a class="dropdown-item theme-option" href="#" data-theme="primary">
                                <span class="theme-preview primary"></span>海洋蓝
                            </a>
                            <a class="dropdown-item theme-option" href="#" data-theme="success">
                                <span class="theme-preview success"></span>自然绿
                            </a>
                            <a class="dropdown-item theme-option" href="#" data-theme="warning">
                                <span class="theme-preview warning"></span>活力橙
                            </a>
                            <a class="dropdown-item theme-option" href="#" data-theme="info">
                                <span class="theme-preview info"></span>优雅紫
                            </a>
                            <a class="dropdown-item theme-option" href="#" data-theme="danger">
                                <span class="theme-preview danger"></span>深邃红
                            </a>
                        </div>
                    </div>

                    {% if current_user.is_authenticated %}
                    <!-- 通知图标 -->
                    <div class="dropdown">
                        <a class="btn btn-outline-light" href="#" id="notificationDropdown" role="button" data-toggle="dropdown">
                            <i class="fas fa-bell"></i>
                            {% if current_user.unread_notifications_count > 0 %}
                            <span class="badge badge-danger notification-badge" style="position: absolute; top: -5px; right: -5px; font-size: 0.7rem;">{{ current_user.unread_notifications_count }}</span>
                            {% endif %}
                        </a>
                        <div class="dropdown-menu dropdown-menu-right notification-dropdown" style="min-width: 300px;">
                            <h6 class="dropdown-header">通知中心</h6>
                            <div id="notification-list" style="max-height: 300px; overflow-y: auto;">
                                {% if current_user.recent_notifications %}
                                    {% for notification in current_user.recent_notifications %}
                                    <a class="dropdown-item notification-item {% if not notification['is_read'] %}unread{% endif %}" href="{{ url_for('notification.view', id=notification['id']) }}">
                                        <div class="notification-title">
                                            {% if notification['level'] == 2 %}
                                            <span class="badge badge-danger">紧急</span>
                                            {% elif notification['level'] == 1 %}
                                            <span class="badge badge-warning">重要</span>
                                            {% endif %}
                                            {{ notification['title'] }}
                                        </div>
                                        <div class="notification-content">{{ notification['content']|truncate(50) }}</div>
                                        <div class="notification-time">
                                            {% if notification['created_at'] is string %}
                                                {{ notification['created_at'] }}
                                            {% else %}
                                                {{ notification['created_at']|format_datetime }}
                                            {% endif %}
                                        </div>
                                    </a>
                                    {% endfor %}
                                {% else %}
                                <div class="dropdown-item text-center">暂无通知</div>
                                {% endif %}
                            </div>
                            <div class="dropdown-divider"></div>
                            <a class="dropdown-item text-center" href="{{ url_for('notification.index') }}">查看全部通知</a>
                            {% if current_user.unread_notifications_count > 0 %}
                            <a class="dropdown-item text-center" href="{{ url_for('notification.mark_all_read') }}">全部标为已读</a>
                            {% endif %}
                        </div>
                    </div>

                    <!-- 用户菜单 -->
                    <div class="dropdown">
                        <a class="btn btn-outline-light" href="#" id="userDropdown" role="button" data-toggle="dropdown">
                            <i class="fas fa-user"></i>
                            <span>{{ current_user.username }}</span>
                        </a>
                        <div class="dropdown-menu dropdown-menu-right">
                            <a class="dropdown-item" href="#">
                                <i class="fas fa-user"></i> 个人资料
                            </a>
                            <a class="dropdown-item" href="{{ url_for('help.index') }}">
                                <i class="fas fa-question-circle"></i> 帮助中心
                            </a>
                            <div class="dropdown-divider"></div>
                            <a class="dropdown-item" href="{{ url_for('auth.logout') }}">
                                <i class="fas fa-sign-out-alt"></i> 退出登录
                            </a>
                        </div>
                    </div>
                    {% else %}
                    <a class="btn btn-outline-primary" href="{{ url_for('auth.login') }}">登录</a>
                    <a class="btn btn-primary" href="{{ url_for('auth.register') }}">注册</a>
                    {% endif %}
                </div>
            </div>

            <!-- 内容区域 -->
            <div class="content-area">
                {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                {% for category, message in messages %}
                <div class="alert alert-{{ category }} alert-dismissible fade show">
                    {{ message }}
                    <button type="button" class="close" data-dismiss="alert">
                        <span>&times;</span>
                    </button>
                </div>
                {% endfor %}
                {% endif %}
                {% endwith %}

                {% block content %}{% endblock %}
            </div>
        </div>
    </div>

    <!-- 按需加载资源 -->
    {% from 'components/resource_loader.html' import load_page_resources %}
    {{ load_page_resources(page_type|default('basic')) }}
    
    <!-- 页面特定脚本 -->
    {% block scripts %}{% endblock %}

    <!-- 基础布局脚本 -->
    <script nonce="{{ csp_nonce }}">
        function toggleSidebar() {
            document.querySelector('.sidebar').classList.toggle('show');
        }

        document.addEventListener('click', function(e) {
            if (window.innerWidth <= 768) {
                const sidebar = document.querySelector('.sidebar');
                const mainContent = document.querySelector('.main-content');
                if (mainContent.contains(e.target) && !e.target.closest('.mobile-toggle')) {
                    sidebar.classList.remove('show');
                }
            }
        });

        window.addEventListener('resize', function() {
            const sidebar = document.querySelector('.sidebar');
            if (window.innerWidth > 768) {
                sidebar.classList.remove('show');
            }
        });

        $(document).ready(function() {
            // 侧边栏子菜单切换 - 手风琴效果
            $('.sidebar-nav [data-toggle="collapse"]').on('click', function(e) {
                e.preventDefault();
                const target = $(this).attr('data-target');
                const $target = $(target);
                const $icon = $(this).find('.fa-chevron-down');
                const isCurrentlyShown = $target.hasClass('show');

                // 如果当前菜单是关闭状态，先关闭所有其他菜单
                if (!isCurrentlyShown) {
                    // 关闭所有其他展开的菜单
                    $('.sidebar-nav .collapse.show').each(function() {
                        if (this !== $target[0]) {
                            $(this).collapse('hide');
                            // 重置对应的箭头图标
                            const $otherIcon = $(this).prev().find('.fa-chevron-down');
                            $otherIcon.css('transform', 'rotate(0deg)');
                        }
                    });
                }

                // 切换当前子菜单
                $target.collapse('toggle');

                $target.on('show.bs.collapse', function() {
                    $icon.css('transform', 'rotate(180deg)');
                });

                $target.on('hide.bs.collapse', function() {
                    $icon.css('transform', 'rotate(0deg)');
                });
            });

            // 高亮当前页面的导航项
            const currentPath = window.location.pathname;
            $('.sidebar-nav .nav-link').each(function() {
                const href = $(this).attr('href');
                if (href && currentPath.includes(href) && href !== '/') {
                    $(this).addClass('active');
                    const $collapse = $(this).closest('.collapse');
                    if ($collapse.length) {
                        $collapse.addClass('show');
                        const $parentLink = $collapse.prev().find('[data-toggle="collapse"]');
                        $parentLink.find('.fa-chevron-down').css('transform', 'rotate(180deg)');
                    }
                }
            });
        });
    </script>
</body>
</html>
