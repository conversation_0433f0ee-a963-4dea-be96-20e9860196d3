"""
专业日常管理模板生成器
为日常管理模块提供专业、优雅的打印和导出模板
"""

from flask import current_app, send_file
from .professional_template_generator import ProfessionalTemplateGenerator
from reportlab.lib.pagesizes import A4, landscape
from reportlab.lib import colors
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, KeepTogether
from reportlab.lib.units import mm
from decimal import Decimal
import os
from datetime import datetime, timedelta

class ProfessionalDailyTemplates(ProfessionalTemplateGenerator):
    """专业日常管理模板生成器"""
    
    def generate_inspection_records_excel(self, records, user_area, start_date, end_date):
        """生成检查记录Excel"""
        # 准备数据
        data = []
        for record in records:
            data.append([
                self.format_date(record.get('inspection_date')),
                record.get('inspection_type', ''),
                record.get('inspection_item', ''),
                record.get('result', ''),
                record.get('issue_description', ''),
                record.get('corrective_action', ''),
                record.get('inspector', ''),
                record.get('status', ''),
                self.format_datetime(record.get('created_at'))
            ])
        
        headers = ["检查日期", "检查类型", "检查项目", "检查结果", "问题描述", "整改措施", "检查人", "状态", "记录时间"]
        
        # 创建Excel文件
        temp_file, filename = self.create_professional_excel(
            data=data,
            headers=headers,
            title="日常检查记录",
            subtitle=f"统计期间：{self.format_date(start_date)} 至 {self.format_date(end_date)}",
            company_name=user_area.name,
            report_date=datetime.now().date()
        )
        
        return send_file(
            temp_file,
            as_attachment=True,
            download_name=filename,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
    
    def generate_companion_dining_excel(self, records, user_area, start_date, end_date):
        """生成陪餐记录Excel"""
        # 准备数据
        data = []
        for record in records:
            data.append([
                self.format_date(record.get('dining_date')),
                record.get('companion_name', ''),
                record.get('meal_type', ''),
                record.get('food_rating', ''),
                record.get('feedback', ''),
                record.get('suggestions', ''),
                record.get('dining_location', ''),
                self.format_datetime(record.get('created_at'))
            ])
        
        headers = ["陪餐日期", "陪餐人", "餐次", "菜品评价", "问题反馈", "改进建议", "用餐地点", "记录时间"]
        
        # 创建Excel文件
        temp_file, filename = self.create_professional_excel(
            data=data,
            headers=headers,
            title="陪餐记录",
            subtitle=f"统计期间：{self.format_date(start_date)} 至 {self.format_date(end_date)}",
            company_name=user_area.name,
            report_date=datetime.now().date()
        )
        
        return send_file(
            temp_file,
            as_attachment=True,
            download_name=filename,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
    
    def generate_training_records_excel(self, records, user_area, start_date, end_date):
        """生成培训记录Excel"""
        # 准备数据
        data = []
        for record in records:
            data.append([
                self.format_date(record.get('training_date')),
                record.get('training_topic', ''),
                record.get('training_type', ''),
                record.get('participants', ''),
                f"{record.get('duration', 0)}小时" if record.get('duration') else "",
                record.get('trainer', ''),
                record.get('effectiveness', ''),
                record.get('notes', ''),
                self.format_datetime(record.get('created_at'))
            ])
        
        headers = ["培训日期", "培训主题", "培训类型", "参与人员", "培训时长", "培训师", "培训效果", "备注", "记录时间"]
        
        # 创建Excel文件
        temp_file, filename = self.create_professional_excel(
            data=data,
            headers=headers,
            title="培训记录",
            subtitle=f"统计期间：{self.format_date(start_date)} 至 {self.format_date(end_date)}",
            company_name=user_area.name,
            report_date=datetime.now().date()
        )
        
        return send_file(
            temp_file,
            as_attachment=True,
            download_name=filename,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
    
    def generate_daily_summary_pdf(self, daily_log, user_area):
        """生成日常管理汇总PDF"""
        # 创建PDF保存目录
        pdf_dir = self.create_pdf_dir('daily_reports')
        
        # 生成PDF文件名
        filename = f"日常管理汇总_{daily_log.log_date.strftime('%Y%m%d')}_{datetime.now().strftime('%H%M%S')}.pdf"
        pdf_path = os.path.join(pdf_dir, filename)
        
        # 创建PDF文档
        doc = SimpleDocTemplate(
            pdf_path,
            pagesize=A4,
            rightMargin=20*mm,
            leftMargin=20*mm,
            topMargin=20*mm,
            bottomMargin=20*mm
        )
        
        content = []
        
        # 创建页眉
        self.create_pdf_header(
            content,
            title="日常管理工作汇总",
            company_name=user_area.name,
            report_date=daily_log.log_date
        )
        
        # 基本信息
        basic_info = [
            [
                Paragraph("汇总日期:", self.pdf_styles['body_text']),
                Paragraph(self.format_date(daily_log.log_date), self.pdf_styles['body_text']),
                Paragraph("记录人:", self.pdf_styles['body_text']),
                Paragraph(daily_log.recorder.real_name or daily_log.recorder.username if daily_log.recorder else "", self.pdf_styles['body_text'])
            ],
            [
                Paragraph("天气情况:", self.pdf_styles['body_text']),
                Paragraph(daily_log.weather or "-", self.pdf_styles['body_text']),
                Paragraph("就餐人数:", self.pdf_styles['body_text']),
                Paragraph(str(daily_log.dining_count) if daily_log.dining_count else "-", self.pdf_styles['body_text'])
            ]
        ]
        
        info_table = self.create_info_table(basic_info)
        content.append(info_table)
        content.append(Spacer(1, 10*mm))
        
        # 检查记录
        content.append(Paragraph("一、检查记录", self.pdf_styles['section_title']))
        
        # 这里应该从数据库获取实际的检查记录
        inspection_headers = ["检查项目", "检查结果", "问题描述", "整改措施", "检查人"]
        inspection_data = [
            ["食材验收", "合格", "", "", "张三"],
            ["环境卫生", "良好", "", "", "李四"],
            ["设备运行", "正常", "", "", "王五"]
        ]
        
        inspection_table = self.create_professional_table(
            inspection_data,
            inspection_headers,
            col_widths=[35*mm, 25*mm, 40*mm, 40*mm, 25*mm]
        )
        
        content.append(inspection_table)
        content.append(Spacer(1, 8*mm))
        
        # 陪餐记录
        content.append(Paragraph("二、陪餐记录", self.pdf_styles['section_title']))
        
        companion_headers = ["陪餐人", "餐次", "菜品评价", "问题反馈", "改进建议"]
        companion_data = [
            ["校长", "午餐", "良好", "", "增加蔬菜品种"],
            ["副校长", "晚餐", "优秀", "", ""]
        ]
        
        companion_table = self.create_professional_table(
            companion_data,
            companion_headers,
            col_widths=[25*mm, 20*mm, 30*mm, 50*mm, 40*mm]
        )
        
        content.append(companion_table)
        content.append(Spacer(1, 8*mm))
        
        # 培训记录
        content.append(Paragraph("三、培训记录", self.pdf_styles['section_title']))
        
        if hasattr(daily_log, 'training_records') and daily_log.training_records:
            training_headers = ["培训主题", "培训类型", "参与人员", "培训时长", "培训效果"]
            training_data = []
            
            for training in daily_log.training_records:
                training_data.append([
                    training.training_topic or "",
                    training.training_type or "",
                    training.participants or "",
                    f"{training.duration}小时" if training.duration else "",
                    training.effectiveness or ""
                ])
            
            training_table = self.create_professional_table(
                training_data,
                training_headers,
                col_widths=[40*mm, 25*mm, 40*mm, 20*mm, 30*mm]
            )
            
            content.append(training_table)
        else:
            content.append(Paragraph("今日无培训记录", self.pdf_styles['body_text']))
        
        content.append(Spacer(1, 8*mm))
        
        # 问题记录
        content.append(Paragraph("四、问题记录", self.pdf_styles['section_title']))
        
        if hasattr(daily_log, 'issues') and daily_log.issues:
            issue_headers = ["问题类型", "问题描述", "严重程度", "处理状态", "解决方案"]
            issue_data = []
            
            for issue in daily_log.issues:
                issue_data.append([
                    issue.issue_type or "",
                    issue.description or "",
                    issue.severity or "",
                    issue.status or "",
                    issue.solution or ""
                ])
            
            issue_table = self.create_professional_table(
                issue_data,
                issue_headers,
                col_widths=[25*mm, 50*mm, 20*mm, 25*mm, 45*mm]
            )
            
            content.append(issue_table)
        else:
            content.append(Paragraph("今日无问题记录", self.pdf_styles['body_text']))
        
        content.append(Spacer(1, 10*mm))
        
        # 工作总结
        content.append(Paragraph("五、工作总结", self.pdf_styles['section_title']))
        
        summary_text = daily_log.summary if hasattr(daily_log, 'summary') and daily_log.summary else "今日食堂运营正常，各项工作按计划进行。"
        content.append(Paragraph(summary_text, self.pdf_styles['body_text']))
        
        # 创建页脚
        signatures = [
            {'label': '记录人', 'value': daily_log.recorder.real_name or daily_log.recorder.username if daily_log.recorder else ""},
            {'label': '审核人', 'value': ""},
            {'label': '主管', 'value': ""}
        ]
        
        self.create_pdf_footer(content, signatures=signatures)
        
        # 生成PDF
        doc.build(content)
        
        # 返回相对路径
        return os.path.join('pdf', 'daily_reports', filename)
    
    def generate_monthly_summary_pdf(self, monthly_data, user_area, year, month):
        """生成月度工作总结PDF"""
        # 创建PDF保存目录
        pdf_dir = self.create_pdf_dir('monthly_reports')
        
        # 生成PDF文件名
        filename = f"月度工作总结_{year}年{month:02d}月_{datetime.now().strftime('%Y%m%d%H%M%S')}.pdf"
        pdf_path = os.path.join(pdf_dir, filename)
        
        # 创建PDF文档
        doc = SimpleDocTemplate(
            pdf_path,
            pagesize=A4,
            rightMargin=20*mm,
            leftMargin=20*mm,
            topMargin=20*mm,
            bottomMargin=20*mm
        )
        
        content = []
        
        # 创建页眉
        self.create_pdf_header(
            content,
            title="食堂月度工作总结",
            subtitle=f"{year}年{month}月",
            company_name=user_area.name,
            report_date=datetime.now().date()
        )
        
        # 月度统计数据
        content.append(Paragraph("一、月度统计数据", self.pdf_styles['section_title']))
        
        stats_headers = ["统计项目", "数量", "单位", "备注"]
        stats_data = [
            ["工作日天数", str(monthly_data.get('work_days', 0)), "天", ""],
            ["检查次数", str(monthly_data.get('inspection_count', 0)), "次", ""],
            ["陪餐次数", str(monthly_data.get('companion_count', 0)), "次", ""],
            ["培训次数", str(monthly_data.get('training_count', 0)), "次", ""],
            ["问题记录", str(monthly_data.get('issue_count', 0)), "项", ""],
            ["总就餐人次", str(monthly_data.get('total_dining', 0)), "人次", ""]
        ]
        
        stats_table = self.create_professional_table(
            stats_data,
            stats_headers,
            col_widths=[50*mm, 30*mm, 20*mm, 65*mm]
        )
        
        content.append(stats_table)
        content.append(Spacer(1, 10*mm))
        
        # 工作亮点
        content.append(Paragraph("二、工作亮点", self.pdf_styles['section_title']))
        highlights = [
            "• 食品安全零事故，确保师生用餐安全",
            "• 陪餐制度执行良好，及时收集反馈意见",
            "• 员工培训覆盖率100%，提升服务质量",
            "• 设备维护及时，保障正常运营"
        ]
        
        for highlight in highlights:
            content.append(Paragraph(highlight, self.pdf_styles['body_text']))
        
        content.append(Spacer(1, 8*mm))
        
        # 存在问题
        content.append(Paragraph("三、存在问题及改进措施", self.pdf_styles['section_title']))
        issues_text = """
        1. 部分员工食品安全意识有待加强
           改进措施：加强培训，定期考核
        
        2. 菜品种类相对单一
           改进措施：增加菜品种类，丰富营养搭配
        
        3. 设备老化问题逐渐显现
           改进措施：制定设备更新计划，确保正常运营
        """
        
        content.append(Paragraph(issues_text, self.pdf_styles['body_text']))
        content.append(Spacer(1, 8*mm))
        
        # 下月计划
        content.append(Paragraph("四、下月工作计划", self.pdf_styles['section_title']))
        plans_text = """
        1. 继续严格执行食品安全管理制度
        2. 加强员工培训，提升服务水平
        3. 优化菜品结构，提高师生满意度
        4. 做好设备维护保养工作
        5. 完善各项管理制度和流程
        """
        
        content.append(Paragraph(plans_text, self.pdf_styles['body_text']))
        
        # 创建页脚
        signatures = [
            {'label': '制表人', 'value': ""},
            {'label': '审核人', 'value': ""},
            {'label': '主管领导', 'value': ""}
        ]
        
        self.create_pdf_footer(content, signatures=signatures)
        
        # 生成PDF
        doc.build(content)
        
        # 返回相对路径
        return os.path.join('pdf', 'monthly_reports', filename)
