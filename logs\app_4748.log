2025-06-22 21:46:46,109 INFO: 应用启动 - PID: 4748 [in C:\StudentsCMSSP\app\__init__.py:868]
2025-06-22 21:47:20,236 INFO: 生成固定二维码 - 用户: 18373062333, 学校: 朝阳区实验中学 (ID: 42) [in C:\StudentsCMSSP\app\routes\daily_management\inspection_qrcode.py:31]
2025-06-22 21:47:20,237 INFO: 员工上传URL: http://xiaoyuanst.com/daily-management/public/inspections/select-date/42/upload [in C:\StudentsCMSSP\app\routes\daily_management\inspection_qrcode.py:38]
2025-06-22 21:47:20,256 INFO: 成功生成二维码base64，数据长度: 1268 [in C:\StudentsCMSSP\app\utils\qrcode_helper.py:78]
2025-06-22 21:47:20,258 INFO: 管理员评分URL: http://xiaoyuanst.com/daily-management/public/inspections/select-date/42/rate [in C:\StudentsCMSSP\app\routes\daily_management\inspection_qrcode.py:46]
2025-06-22 21:47:20,273 INFO: 成功生成二维码base64，数据长度: 1108 [in C:\StudentsCMSSP\app\utils\qrcode_helper.py:78]
2025-06-22 21:47:51,720 INFO: 生成固定二维码 - 用户: 18373062333, 学校: 朝阳区实验中学 (ID: 42) [in C:\StudentsCMSSP\app\routes\daily_management\inspection_qrcode.py:31]
2025-06-22 21:47:51,721 INFO: 员工上传URL: http://xiaoyuanst.com/daily-management/public/inspections/select-date/42/upload [in C:\StudentsCMSSP\app\routes\daily_management\inspection_qrcode.py:38]
2025-06-22 21:47:51,741 INFO: 成功生成二维码base64，数据长度: 1268 [in C:\StudentsCMSSP\app\utils\qrcode_helper.py:78]
2025-06-22 21:47:51,741 INFO: 管理员评分URL: http://xiaoyuanst.com/daily-management/public/inspections/select-date/42/rate [in C:\StudentsCMSSP\app\routes\daily_management\inspection_qrcode.py:46]
2025-06-22 21:47:51,757 INFO: 成功生成二维码base64，数据长度: 1108 [in C:\StudentsCMSSP\app\utils\qrcode_helper.py:78]
2025-06-22 21:48:30,944 INFO: 收到创建周菜单请求: b'{"area_id":"42","week_start":"2025-06-16"}' [in C:\StudentsCMSSP\app\routes\api_weekly_menu.py:76]
2025-06-22 21:48:30,945 INFO: 创建周菜单参数: area_id=42, week_start=2025-06-16 [in C:\StudentsCMSSP\app\routes\api_weekly_menu.py:87]
2025-06-22 21:48:30,945 INFO: 检查用户权限: user_id=34, area_id=42 [in C:\StudentsCMSSP\app\routes\api_weekly_menu.py:98]
2025-06-22 21:48:30,947 INFO: 用户角色: ['学校管理员'] [in C:\StudentsCMSSP\app\routes\api_weekly_menu.py:99]
2025-06-22 21:48:30,954 INFO: 权限检查结果: is_admin=0, is_school_admin=1, can_access_area=0, has_edit_permission=1 [in C:\StudentsCMSSP\app\routes\api_weekly_menu.py:106]
2025-06-22 21:48:30,955 INFO: 开始创建周菜单: area_id=42, week_start=2025-06-16, created_by=34 [in C:\StudentsCMSSP\app\routes\api_weekly_menu.py:123]
2025-06-22 21:48:30,955 INFO: 开始创建周菜单: area_id=42, week_start=2025-06-16, created_by=34 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:184]
2025-06-22 21:48:30,956 INFO: 转换后的日期对象: 2025-06-16, 类型=<class 'datetime.date'> [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:190]
2025-06-22 21:48:30,956 INFO: 计算的周结束日期: 2025-06-22 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:202]
2025-06-22 21:48:30,956 INFO: 检查是否已存在该周的菜单: area_id=42, week_start=2025-06-16 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:205]
2025-06-22 21:48:30,956 INFO: 获取周菜单: area_id=42, week_start=2025-06-16, 类型=<class 'datetime.date'> [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:42]
2025-06-22 21:48:30,956 INFO: 使用优化后的查询: area_id=42, week_start=2025-06-16 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:65]
2025-06-22 21:48:30,957 INFO: 执行主SQL查询: area_id=42, week_start_str=2025-06-16 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:77]
2025-06-22 21:48:30,961 INFO: 主查询未找到菜单: area_id=42, week_start=2025-06-16 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:102]
2025-06-22 21:48:30,961 INFO: 使用日期字符串: week_start_str=2025-06-16, week_end_str=2025-06-22 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:214]
2025-06-22 21:48:30,961 INFO: 准备执行SQL创建菜单 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:217]
2025-06-22 21:48:30,962 INFO: SQL参数: {'area_id': '42', 'week_start_str': '2025-06-16', 'week_end_str': '2025-06-22', 'status': '计划中', 'created_by': 34} [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:232]
2025-06-22 21:48:30,962 INFO: 执行SQL: 
            INSERT INTO weekly_menus (area_id, week_start, week_end, status, created_by)
            OUTPUT inserted.id
            VALUES (:area_id, CONVERT(DATETIME2(0), :week_start_str), CONVERT(DATETIME2(0), :week_end_str), :status, :created_by)
             [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:236]
2025-06-22 21:48:30,975 INFO: SQL执行成功，获取到ID: 44 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:243]
2025-06-22 21:48:30,976 INFO: 检查数据库连接状态 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:248]
2025-06-22 21:48:30,977 INFO: 数据库连接正常 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:251]
2025-06-22 21:48:30,979 INFO: 事务提交成功 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:258]
2025-06-22 21:48:30,979 INFO: 菜单缓存已清理 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:20]
2025-06-22 21:48:30,984 INFO: 验证成功: 菜单已创建 ID=44 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:266]
2025-06-22 21:48:30,984 INFO: 周菜单创建成功: id=44 [in C:\StudentsCMSSP\app\routes\api_weekly_menu.py:125]
2025-06-22 21:48:30,986 INFO: 返回创建周菜单响应: {'success': True, 'message': '周菜单创建成功', 'weekly_menu_id': 44, 'status': '计划中'} [in C:\StudentsCMSSP\app\routes\api_weekly_menu.py:142]
2025-06-22 21:49:10,988 WARNING: [安全监控] 2025-06-22 21:49:10 - 可疑请求 | IP: ************** | 路径: /example.com:443, 指标: 可疑HTTP方法 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-22 21:49:12,227 WARNING: [安全监控] 2025-06-22 21:49:12 - 可疑请求 | IP: ************** | 路径: /example.com:443, 指标: 可疑HTTP方法 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-22 21:49:12,632 INFO: 获取副表数据用于补全主表: weekly_menu_id=44 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:342]
2025-06-22 21:49:12,638 INFO: 副表数据映射构建完成: 0 天, 0 个菜品 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:370]
2025-06-22 21:49:12,638 INFO: 主表数据补全完成，准备保存: 总菜品数=3, 已补全=3, 未补全=0 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:419]
2025-06-22 21:49:12,640 INFO: 删除现有菜单食谱(主表): weekly_menu_id=44 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:428]
2025-06-22 21:49:12,642 INFO: 删除现有菜单食谱(副表): weekly_menu_id=44 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:437]
2025-06-22 21:49:12,658 INFO: 保存周菜单成功(主表和副表): id=44 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:500]
2025-06-22 21:49:12,658 INFO: 菜单缓存已清理 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:20]
2025-06-22 21:52:36,728 ERROR: [安全监控] 2025-06-22 21:52:36 - 频率限制触发 | IP: 127.0.0.1 | IP 127.0.0.1 在1分钟内请求 60 次 [in C:\StudentsCMSSP\app\security_monitor.py:126]
2025-06-22 21:52:36,730 WARNING: SECURITY EVENT - RATE_LIMIT: Rate limit exceeded from 127.0.0.1 [in C:\StudentsCMSSP\app\security_config.py:155]
2025-06-22 21:52:36,731 WARNING: [安全监控] 2025-06-22 21:52:36 - 阻止访问 | IP: 127.0.0.1 | IP 127.0.0.1 尝试访问 /static/js/file-upload-fix.js [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-22 21:52:36,733 WARNING: SECURITY EVENT - RATE_LIMIT: Rate limit exceeded from 127.0.0.1 [in C:\StudentsCMSSP\app\security_config.py:155]
2025-06-22 21:52:36,851 WARNING: [安全监控] 2025-06-22 21:52:36 - 阻止访问 | IP: 127.0.0.1 | IP 127.0.0.1 尝试访问 /static/js/mobile-table-cards.js [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-22 21:52:36,852 WARNING: SECURITY EVENT - RATE_LIMIT: Rate limit exceeded from 127.0.0.1 [in C:\StudentsCMSSP\app\security_config.py:155]
2025-06-22 21:52:36,954 WARNING: [安全监控] 2025-06-22 21:52:36 - 阻止访问 | IP: 127.0.0.1 | IP 127.0.0.1 尝试访问 /static/js/mobile-enhancements.js [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-22 21:52:36,954 WARNING: SECURITY EVENT - RATE_LIMIT: Rate limit exceeded from 127.0.0.1 [in C:\StudentsCMSSP\app\security_config.py:155]
2025-06-22 21:52:37,274 WARNING: [安全监控] 2025-06-22 21:52:37 - 阻止访问 | IP: 127.0.0.1 | IP 127.0.0.1 尝试访问 /notifications/check [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-22 21:52:37,276 WARNING: [安全监控] 2025-06-22 21:52:37 - 阻止访问 | IP: 127.0.0.1 | IP 127.0.0.1 尝试访问 /api/check-login-status [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-22 21:52:37,276 WARNING: SECURITY EVENT - RATE_LIMIT: Rate limit exceeded from 127.0.0.1 [in C:\StudentsCMSSP\app\security_config.py:155]
2025-06-22 21:52:37,276 WARNING: SECURITY EVENT - RATE_LIMIT: Rate limit exceeded from 127.0.0.1 [in C:\StudentsCMSSP\app\security_config.py:155]
2025-06-22 21:52:41,470 WARNING: [安全监控] 2025-06-22 21:52:41 - 阻止访问 | IP: 127.0.0.1 | IP 127.0.0.1 尝试访问 /login [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-22 21:52:41,472 WARNING: SECURITY EVENT - RATE_LIMIT: Rate limit exceeded from 127.0.0.1 [in C:\StudentsCMSSP\app\security_config.py:155]
2025-06-22 21:52:54,371 WARNING: [安全监控] 2025-06-22 21:52:54 - 阻止访问 | IP: 127.0.0.1 | IP 127.0.0.1 尝试访问 /login [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-22 21:52:54,372 WARNING: SECURITY EVENT - RATE_LIMIT: Rate limit exceeded from 127.0.0.1 [in C:\StudentsCMSSP\app\security_config.py:155]
2025-06-22 21:53:10,910 WARNING: [安全监控] 2025-06-22 21:53:10 - 阻止访问 | IP: 127.0.0.1 | IP 127.0.0.1 尝试访问 / [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-22 21:53:10,911 WARNING: SECURITY EVENT - RATE_LIMIT: Rate limit exceeded from 127.0.0.1 [in C:\StudentsCMSSP\app\security_config.py:155]
