"""
专业财务模板生成器
为财务模块提供专业、优雅的打印和导出模板
"""

from flask import current_app, send_file
from .professional_template_generator import ProfessionalTemplateGenerator
from reportlab.lib.pagesizes import A4, landscape
from reportlab.lib import colors
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, KeepTogether
from reportlab.lib.units import mm
from decimal import Decimal
import os
from datetime import datetime

class ProfessionalFinancialTemplates(ProfessionalTemplateGenerator):
    """专业财务模板生成器"""
    
    def generate_voucher_pdf(self, voucher, details, user_area, landscape_mode=True):
        """生成专业财务凭证PDF"""
        # 创建PDF保存目录
        pdf_dir = self.create_pdf_dir('vouchers')
        
        # 生成PDF文件名
        filename = f"财务凭证_{voucher.voucher_number}_{datetime.now().strftime('%Y%m%d%H%M%S')}.pdf"
        pdf_path = os.path.join(pdf_dir, filename)
        
        # 设置页面大小
        pagesize = landscape(A4) if landscape_mode else A4
        
        # 创建PDF文档
        doc = SimpleDocTemplate(
            pdf_path,
            pagesize=pagesize,
            rightMargin=15*mm,
            leftMargin=15*mm,
            topMargin=15*mm,
            bottomMargin=15*mm
        )
        
        content = []
        
        # 创建页眉
        self.create_pdf_header(
            content,
            title="记账凭证",
            company_name=user_area.name,
            report_date=voucher.voucher_date
        )
        
        # 凭证基本信息
        voucher_info = [
            [
                Paragraph("凭证字:", self.pdf_styles['body_text']),
                Paragraph(voucher.voucher_type, self.pdf_styles['body_text']),
                Paragraph("号:", self.pdf_styles['body_text']),
                Paragraph(str(voucher.voucher_number), self.pdf_styles['body_text']),
                Paragraph("日期:", self.pdf_styles['body_text']),
                Paragraph(self.format_date(voucher.voucher_date), self.pdf_styles['body_text']),
                Paragraph("附件:", self.pdf_styles['body_text']),
                Paragraph(f"{voucher.attachment_count or 0}张", self.pdf_styles['body_text'])
            ]
        ]
        
        info_table = self.create_info_table(voucher_info, [20*mm, 25*mm, 15*mm, 25*mm, 20*mm, 25*mm, 20*mm, 20*mm])
        content.append(info_table)
        content.append(Spacer(1, 8*mm))
        
        # 凭证明细表
        headers = ["序号", "摘要", "会计科目", "借方金额", "贷方金额"]
        
        # 准备明细数据
        detail_data = []
        total_debit = Decimal('0')
        total_credit = Decimal('0')
        
        for i, detail in enumerate(details, 1):
            # 格式化金额
            debit_amount = self.format_currency(detail.debit_amount) if detail.debit_amount > 0 else ""
            credit_amount = self.format_currency(detail.credit_amount) if detail.credit_amount > 0 else ""
            
            # 处理摘要和科目文本
            summary_text = detail.summary or voucher.summary or ''
            subject_text = f"{detail.subject.code} {detail.subject.name}" if detail.subject else ""
            
            detail_data.append([
                str(i),
                summary_text,
                subject_text,
                debit_amount,
                credit_amount
            ])
            
            total_debit += detail.debit_amount or Decimal('0')
            total_credit += detail.credit_amount or Decimal('0')
        
        # 添加合计行
        detail_data.append([
            "合计", "", "",
            self.format_currency(total_debit),
            self.format_currency(total_credit)
        ])
        
        # 创建明细表格
        detail_table = self.create_professional_table(
            detail_data, 
            headers, 
            col_widths=[15*mm, 50*mm, 50*mm, 30*mm, 30*mm],
            has_total_row=True
        )
        
        content.append(detail_table)
        content.append(Spacer(1, 10*mm))
        
        # 大写金额
        chinese_amount = self.convert_amount_to_chinese(float(total_debit))
        amount_info = [
            [Paragraph(f"金额大写：{chinese_amount}", self.pdf_styles['body_text'])]
        ]
        
        amount_table = self.create_info_table(amount_info, [200*mm])
        content.append(amount_table)
        content.append(Spacer(1, 10*mm))
        
        # 创建页脚（签名栏）
        signatures = [
            {'label': '制单', 'value': voucher.creator.username if voucher.creator else ""},
            {'label': '审核', 'value': voucher.reviewer.username if voucher.reviewer else ""},
            {'label': '记账', 'value': voucher.poster.username if voucher.poster else ""},
            {'label': '出纳', 'value': ""}
        ]
        
        self.create_pdf_footer(content, signatures=signatures)
        
        # 生成PDF
        doc.build(content)
        
        # 返回相对路径
        return os.path.join('pdf', 'vouchers', filename)
    
    def generate_voucher_excel(self, vouchers, user_area):
        """生成财务凭证Excel导出"""
        # 准备数据
        data = []
        for voucher in vouchers:
            data.append([
                voucher.voucher_number,
                voucher.voucher_type,
                self.format_date(voucher.voucher_date),
                voucher.summary or "",
                self.format_currency(voucher.total_amount),
                voucher.status,
                voucher.creator.username if voucher.creator else "",
                self.format_datetime(voucher.created_at)
            ])
        
        headers = ["凭证号", "凭证类型", "凭证日期", "摘要", "金额", "状态", "制单人", "创建时间"]
        
        # 创建Excel文件
        temp_file, filename = self.create_professional_excel(
            data=data,
            headers=headers,
            title="财务凭证清单",
            company_name=user_area.name,
            report_date=datetime.now().date()
        )
        
        return send_file(
            temp_file,
            as_attachment=True,
            download_name=filename,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
    
    def generate_balance_sheet_pdf(self, balance_data, balance_date, user_area):
        """生成资产负债表PDF"""
        # 创建PDF保存目录
        pdf_dir = self.create_pdf_dir('reports')
        
        # 生成PDF文件名
        filename = f"资产负债表_{balance_date.strftime('%Y%m%d')}_{datetime.now().strftime('%H%M%S')}.pdf"
        pdf_path = os.path.join(pdf_dir, filename)
        
        # 创建PDF文档
        doc = SimpleDocTemplate(
            pdf_path,
            pagesize=A4,
            rightMargin=20*mm,
            leftMargin=20*mm,
            topMargin=20*mm,
            bottomMargin=20*mm
        )
        
        content = []
        
        # 创建页眉
        self.create_pdf_header(
            content,
            title="资产负债表",
            company_name=user_area.name,
            report_date=balance_date
        )
        
        # 资产负债表数据
        headers = ["资产", "金额", "负债和所有者权益", "金额"]
        
        # 处理资产负债表数据
        table_data = []
        
        # 资产部分
        assets = balance_data.get('assets', {})
        liabilities = balance_data.get('liabilities', {})
        equity = balance_data.get('equity', {})
        
        max_rows = max(len(assets), len(liabilities) + len(equity))
        
        asset_items = list(assets.items())
        liability_equity_items = list(liabilities.items()) + list(equity.items())
        
        for i in range(max_rows):
            row = ["", "", "", ""]
            
            # 资产项目
            if i < len(asset_items):
                asset_name, asset_amount = asset_items[i]
                row[0] = asset_name
                row[1] = self.format_currency(asset_amount)
            
            # 负债和权益项目
            if i < len(liability_equity_items):
                item_name, item_amount = liability_equity_items[i]
                row[2] = item_name
                row[3] = self.format_currency(item_amount)
            
            table_data.append(row)
        
        # 添加合计行
        total_assets = sum(assets.values()) if assets else 0
        total_liabilities_equity = sum(liabilities.values()) + sum(equity.values()) if liabilities or equity else 0
        
        table_data.append([
            "资产总计", self.format_currency(total_assets),
            "负债和所有者权益总计", self.format_currency(total_liabilities_equity)
        ])
        
        # 创建表格
        balance_table = self.create_professional_table(
            table_data,
            headers,
            col_widths=[60*mm, 30*mm, 60*mm, 30*mm],
            has_total_row=True
        )
        
        content.append(balance_table)
        
        # 创建页脚
        signatures = [
            {'label': '制表', 'value': ""},
            {'label': '审核', 'value': ""},
            {'label': '主管', 'value': ""}
        ]
        
        self.create_pdf_footer(content, signatures=signatures)
        
        # 生成PDF
        doc.build(content)
        
        # 返回相对路径
        return os.path.join('pdf', 'reports', filename)
    
    def convert_amount_to_chinese(self, amount):
        """转换金额为中文大写"""
        if amount == 0:
            return "零元整"
        
        digits = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖']
        units = ['', '拾', '佰', '仟']
        big_units = ['', '万', '亿']
        
        # 简化的中文大写转换
        yuan = int(amount)
        jiao = int((amount - yuan) * 10)
        fen = int((amount - yuan - jiao * 0.1) * 100)
        
        result = ""
        
        # 处理元
        if yuan > 0:
            if yuan < 10:
                result = digits[yuan] + "元"
            elif yuan < 100:
                shi = yuan // 10
                ge = yuan % 10
                result = digits[shi] + "拾"
                if ge > 0:
                    result += digits[ge]
                result += "元"
            else:
                # 简化处理，实际应用中可以更完善
                result = f"{yuan}元"
        else:
            result = "零元"
        
        # 处理角分
        if jiao > 0:
            result += digits[jiao] + "角"
        if fen > 0:
            result += digits[fen] + "分"
        
        if jiao == 0 and fen == 0:
            result += "整"
        
        return f"人民币{result}"
