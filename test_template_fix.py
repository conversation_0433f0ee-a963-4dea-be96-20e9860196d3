#!/usr/bin/env python3
"""
测试模板格式化修复
验证Jinja2模板中的格式化问题是否已解决
"""

def test_format_with_none():
    """测试None值的格式化"""
    try:
        # 模拟原来的错误情况
        value = None
        # 这会导致错误：TypeError: not all arguments converted during string formatting
        # result = "{:,.2f}".format(value)
        
        # 修复后的方式
        result = "{:,.2f}".format(value or 0)
        print(f"✅ None值格式化成功: {result}")
        return True
    except Exception as e:
        print(f"❌ None值格式化失败: {str(e)}")
        return False

def test_format_with_numbers():
    """测试数字格式化"""
    test_cases = [
        (123.456, "123.46"),
        (1234.567, "1,234.57"),
        (0, "0.00"),
        (-123.45, "-123.45"),
        (None, "0.00"),
        ("", "0.00"),
        (False, "0.00")
    ]
    
    passed = 0
    failed = 0
    
    for value, expected in test_cases:
        try:
            result = "{:,.2f}".format(value or 0)
            if result == expected:
                print(f"✅ 格式化测试通过: {value} -> {result}")
                passed += 1
            else:
                print(f"❌ 格式化测试失败: {value} -> {result} (期望: {expected})")
                failed += 1
        except Exception as e:
            print(f"❌ 格式化异常: {value} -> {str(e)}")
            failed += 1
    
    print(f"\n📊 数字格式化测试结果: 通过 {passed} 个，失败 {failed} 个")
    return failed == 0

def test_jinja2_template():
    """测试Jinja2模板格式化"""
    try:
        from jinja2 import Template
        
        # 测试模板
        template_str = '''
        <span>{{ "{:,.2f}".format(balance or 0) }}</span>
        <span>{{ "{:,.2f}".format(debit_amount or 0) }}</span>
        <span>{{ "{:,.2f}".format(credit_amount or 0) }}</span>
        '''
        
        template = Template(template_str)
        
        # 测试数据
        test_data = {
            'balance': None,
            'debit_amount': 123.45,
            'credit_amount': 0
        }
        
        result = template.render(**test_data)
        print("✅ Jinja2模板渲染成功:")
        print(result.strip())
        return True
        
    except Exception as e:
        print(f"❌ Jinja2模板测试失败: {str(e)}")
        return False

def test_financial_template_patterns():
    """测试财务模板中的常见模式"""
    try:
        from jinja2 import Template
        
        # 模拟财务模板中的条件格式化
        template_str = '''
        {% if record.balance and record.balance > 0 %}
            <span class="positive">{{ "{:,.2f}".format(record.balance or 0) }}</span>
        {% elif record.balance and record.balance < 0 %}
            <span class="negative">{{ "{:,.2f}".format(record.balance or 0) }}</span>
        {% else %}
            <span class="zero">{{ "{:,.2f}".format(record.balance or 0) }}</span>
        {% endif %}
        '''
        
        template = Template(template_str)
        
        # 测试不同的余额情况
        test_cases = [
            {'balance': 100.50},
            {'balance': -50.25},
            {'balance': 0},
            {'balance': None},
            {}  # 没有balance字段
        ]
        
        for i, data in enumerate(test_cases):
            record = type('Record', (), data)()
            result = template.render(record=record)
            print(f"✅ 测试用例 {i+1} 渲染成功: {data} -> {result.strip()}")
        
        return True
        
    except Exception as e:
        print(f"❌ 财务模板模式测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试模板格式化修复...")
    print("=" * 50)
    
    tests = [
        ("None值格式化", test_format_with_none),
        ("数字格式化", test_format_with_numbers),
        ("Jinja2模板", test_jinja2_template),
        ("财务模板模式", test_financial_template_patterns)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        print(f"\n🧪 测试: {test_name}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                failed += 1
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            failed += 1
            print(f"❌ {test_name} 测试异常: {str(e)}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: 通过 {passed} 个，失败 {failed} 个")
    
    if failed == 0:
        print("🎉 所有测试通过！模板格式化问题已修复")
        print("\n💡 修复说明:")
        print("• 将 '{:,.2f}'|format(value) 改为 '{:,.2f}'.format(value or 0)")
        print("• 在条件判断中增加了 None 值检查")
        print("• 确保所有数值格式化都有默认值 0")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关功能")
        return False

if __name__ == '__main__':
    import sys
    success = main()
    sys.exit(0 if success else 1)
