"""
专业库存管理模板生成器
为库存管理模块提供专业、优雅的打印和导出模板
"""

from flask import current_app, send_file
from .professional_template_generator import ProfessionalTemplateGenerator
from reportlab.lib.pagesizes import A4, landscape
from reportlab.lib import colors
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, KeepTogether
from reportlab.lib.units import mm
from decimal import Decimal
import os
from datetime import datetime, timedelta
import qrcode
from io import BytesIO
from reportlab.platypus import Image

class ProfessionalInventoryTemplates(ProfessionalTemplateGenerator):
    """专业库存管理模板生成器"""
    
    def generate_stock_in_pdf(self, stock_in, stock_in_items, user_area):
        """生成专业入库单PDF"""
        # 创建PDF保存目录
        pdf_dir = self.create_pdf_dir('stock_ins')
        
        # 生成PDF文件名
        filename = f"入库单_{stock_in.stock_in_number}_{datetime.now().strftime('%Y%m%d%H%M%S')}.pdf"
        pdf_path = os.path.join(pdf_dir, filename)
        
        # 创建PDF文档
        doc = SimpleDocTemplate(
            pdf_path,
            pagesize=A4,
            rightMargin=15*mm,
            leftMargin=15*mm,
            topMargin=15*mm,
            bottomMargin=15*mm
        )
        
        content = []
        
        # 创建页眉
        self.create_pdf_header(
            content,
            title="食材入库单",
            company_name=user_area.name,
            report_date=stock_in.stock_in_date
        )
        
        # 入库单基本信息
        stock_in_info = [
            [
                Paragraph("入库单号:", self.pdf_styles['body_text']),
                Paragraph(stock_in.stock_in_number, self.pdf_styles['body_text']),
                Paragraph("仓库:", self.pdf_styles['body_text']),
                Paragraph(stock_in.warehouse.name, self.pdf_styles['body_text'])
            ],
            [
                Paragraph("入库日期:", self.pdf_styles['body_text']),
                Paragraph(self.format_date(stock_in.stock_in_date), self.pdf_styles['body_text']),
                Paragraph("入库类型:", self.pdf_styles['body_text']),
                Paragraph(stock_in.stock_in_type, self.pdf_styles['body_text'])
            ],
            [
                Paragraph("供应商:", self.pdf_styles['body_text']),
                Paragraph(stock_in.supplier.name if stock_in.supplier else "-", self.pdf_styles['body_text']),
                Paragraph("操作人:", self.pdf_styles['body_text']),
                Paragraph(stock_in.operator.real_name or stock_in.operator.username, self.pdf_styles['body_text'])
            ],
            [
                Paragraph("审批人:", self.pdf_styles['body_text']),
                Paragraph(stock_in.approver.real_name or stock_in.approver.username if stock_in.approver else "-", self.pdf_styles['body_text']),
                Paragraph("状态:", self.pdf_styles['body_text']),
                Paragraph(stock_in.status, self.pdf_styles['body_text'])
            ]
        ]
        
        info_table = self.create_info_table(stock_in_info)
        content.append(info_table)
        content.append(Spacer(1, 8*mm))
        
        # 入库明细表
        headers = ["序号", "食材名称", "存储位置", "批次号", "数量", "单位", "生产日期", "过期日期", "单价", "金额"]
        
        # 准备明细数据
        detail_data = []
        total_amount = Decimal('0')
        
        for i, item in enumerate(stock_in_items, 1):
            unit_price = item.unit_price or Decimal('0')
            quantity = item.quantity or Decimal('0')
            item_amount = unit_price * quantity
            total_amount += item_amount
            
            detail_data.append([
                str(i),
                item.ingredient.name,
                f"{item.storage_location.name} ({item.storage_location.location_code})" if item.storage_location else "-",
                item.batch_number or "-",
                f"{quantity:.2f}",
                item.unit,
                self.format_date(item.production_date),
                self.format_date(item.expiry_date),
                self.format_currency(unit_price),
                self.format_currency(item_amount)
            ])
        
        # 添加合计行
        detail_data.append([
            "合计", "", "", "", "", "", "", "", "",
            self.format_currency(total_amount)
        ])
        
        # 创建明细表格
        detail_table = self.create_professional_table(
            detail_data,
            headers,
            col_widths=[12*mm, 35*mm, 25*mm, 20*mm, 15*mm, 12*mm, 20*mm, 20*mm, 18*mm, 20*mm],
            has_total_row=True
        )
        
        content.append(detail_table)
        content.append(Spacer(1, 10*mm))
        
        # 质检信息（如果有）
        if hasattr(stock_in, 'inspection_result') and stock_in.inspection_result:
            content.append(Paragraph("质检信息", self.pdf_styles['section_title']))
            content.append(Paragraph(stock_in.inspection_result, self.pdf_styles['body_text']))
            content.append(Spacer(1, 5*mm))
        
        # 创建页脚（签名栏）
        signatures = [
            {'label': '入库员', 'value': stock_in.operator.real_name or stock_in.operator.username},
            {'label': '质检员', 'value': ""},
            {'label': '仓管员', 'value': ""},
            {'label': '审批人', 'value': stock_in.approver.real_name or stock_in.approver.username if stock_in.approver else ""}
        ]
        
        notes = stock_in.notes if hasattr(stock_in, 'notes') and stock_in.notes else None
        self.create_pdf_footer(content, signatures=signatures, notes=notes)
        
        # 生成PDF
        doc.build(content)
        
        # 返回相对路径
        return os.path.join('pdf', 'stock_ins', filename)
    
    def generate_inventory_list_excel(self, inventories, user_area):
        """生成库存清单Excel"""
        # 准备数据
        data = []
        for inventory in inventories:
            data.append([
                inventory.ingredient.name if hasattr(inventory, 'ingredient') and inventory.ingredient else "",
                f"{inventory.quantity:.2f}" if hasattr(inventory, 'quantity') else f"{inventory.current_stock:.2f}" if hasattr(inventory, 'current_stock') else "0.00",
                inventory.unit if hasattr(inventory, 'unit') else "",
                inventory.storage_location.name if hasattr(inventory, 'storage_location') and inventory.storage_location else "",
                inventory.batch_number if hasattr(inventory, 'batch_number') else "",
                self.format_date(inventory.production_date) if hasattr(inventory, 'production_date') else "",
                self.format_date(inventory.expiry_date) if hasattr(inventory, 'expiry_date') else "",
                inventory.supplier.name if hasattr(inventory, 'supplier') and inventory.supplier else "",
                self.format_currency(inventory.unit_price) if hasattr(inventory, 'unit_price') and inventory.unit_price else "",
                self.format_currency(inventory.quantity * inventory.unit_price) if hasattr(inventory, 'quantity') and hasattr(inventory, 'unit_price') and inventory.unit_price else ""
            ])
        
        headers = ["食材名称", "库存数量", "单位", "存储位置", "批次号", "生产日期", "过期日期", "供应商", "单价", "库存金额"]
        
        # 创建Excel文件
        temp_file, filename = self.create_professional_excel(
            data=data,
            headers=headers,
            title="库存清单",
            subtitle="详细库存信息统计表",
            company_name=user_area.name,
            report_date=datetime.now().date()
        )
        
        return send_file(
            temp_file,
            as_attachment=True,
            download_name=filename,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
    
    def generate_expiry_warning_excel(self, inventories, user_area, warning_days=7):
        """生成过期预警Excel"""
        # 准备数据
        data = []
        warning_date = datetime.now().date() + timedelta(days=warning_days)
        
        for inventory in inventories:
            if hasattr(inventory, 'expiry_date') and inventory.expiry_date:
                days_to_expire = (inventory.expiry_date - datetime.now().date()).days
                
                if days_to_expire <= warning_days:
                    status = '已过期' if days_to_expire < 0 else f'{days_to_expire}天后过期'
                    urgency = '紧急' if days_to_expire <= 3 else '警告' if days_to_expire <= 7 else '提醒'
                    
                    data.append([
                        inventory.ingredient.name if hasattr(inventory, 'ingredient') and inventory.ingredient else "",
                        inventory.batch_number if hasattr(inventory, 'batch_number') else "",
                        f"{inventory.quantity:.2f}" if hasattr(inventory, 'quantity') else f"{inventory.current_stock:.2f}" if hasattr(inventory, 'current_stock') else "0.00",
                        inventory.unit if hasattr(inventory, 'unit') else "",
                        self.format_date(inventory.expiry_date),
                        status,
                        urgency,
                        inventory.storage_location.name if hasattr(inventory, 'storage_location') and inventory.storage_location else "",
                        inventory.supplier.name if hasattr(inventory, 'supplier') and inventory.supplier else "",
                        self.format_currency(inventory.quantity * inventory.unit_price) if hasattr(inventory, 'quantity') and hasattr(inventory, 'unit_price') and inventory.unit_price else ""
                    ])
        
        headers = ["食材名称", "批次号", "库存数量", "单位", "过期日期", "状态", "紧急程度", "存储位置", "供应商", "库存金额"]
        
        # 创建Excel文件
        temp_file, filename = self.create_professional_excel(
            data=data,
            headers=headers,
            title="库存过期预警报表",
            subtitle=f"预警期限：{warning_days}天",
            company_name=user_area.name,
            report_date=datetime.now().date()
        )
        
        return send_file(
            temp_file,
            as_attachment=True,
            download_name=filename,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
    
    def generate_purchase_order_pdf(self, order, order_items, user_area):
        """生成专业采购订单PDF"""
        # 创建PDF保存目录
        pdf_dir = self.create_pdf_dir('purchase_orders')
        
        # 生成PDF文件名
        filename = f"采购订单_{order.order_number}_{datetime.now().strftime('%Y%m%d%H%M%S')}.pdf"
        pdf_path = os.path.join(pdf_dir, filename)
        
        # 创建PDF文档
        doc = SimpleDocTemplate(
            pdf_path,
            pagesize=A4,
            rightMargin=15*mm,
            leftMargin=15*mm,
            topMargin=15*mm,
            bottomMargin=15*mm
        )
        
        content = []
        
        # 创建页眉
        self.create_pdf_header(
            content,
            title="食材采购订单",
            company_name=user_area.name,
            report_date=order.order_date
        )
        
        # 订单基本信息
        order_info = [
            [
                Paragraph("订单编号:", self.pdf_styles['body_text']),
                Paragraph(order.order_number, self.pdf_styles['body_text']),
                Paragraph("供应商:", self.pdf_styles['body_text']),
                Paragraph(order.supplier.name, self.pdf_styles['body_text'])
            ],
            [
                Paragraph("订单日期:", self.pdf_styles['body_text']),
                Paragraph(self.format_date(order.order_date), self.pdf_styles['body_text']),
                Paragraph("预计送达:", self.pdf_styles['body_text']),
                Paragraph(self.format_date(order.expected_delivery_date) if order.expected_delivery_date else "-", self.pdf_styles['body_text'])
            ],
            [
                Paragraph("联系人:", self.pdf_styles['body_text']),
                Paragraph(order.supplier.contact_person if order.supplier.contact_person else "-", self.pdf_styles['body_text']),
                Paragraph("联系电话:", self.pdf_styles['body_text']),
                Paragraph(order.supplier.phone if order.supplier.phone else "-", self.pdf_styles['body_text'])
            ],
            [
                Paragraph("采购员:", self.pdf_styles['body_text']),
                Paragraph(order.creator.real_name or order.creator.username, self.pdf_styles['body_text']),
                Paragraph("订单状态:", self.pdf_styles['body_text']),
                Paragraph(order.status, self.pdf_styles['body_text'])
            ]
        ]
        
        info_table = self.create_info_table(order_info)
        content.append(info_table)
        content.append(Spacer(1, 8*mm))
        
        # 订单明细表
        headers = ["序号", "食材名称", "规格", "数量", "单位", "单价", "金额", "备注"]
        
        # 准备明细数据
        detail_data = []
        total_amount = Decimal('0')
        
        for i, item in enumerate(order_items, 1):
            item_total = item.total_price or (item.quantity * item.unit_price)
            total_amount += item_total
            
            detail_data.append([
                str(i),
                item.ingredient.name,
                item.specification if hasattr(item, 'specification') else "-",
                f"{item.quantity:.2f}",
                item.unit,
                self.format_currency(item.unit_price),
                self.format_currency(item_total),
                item.notes or "-"
            ])
        
        # 添加合计行
        detail_data.append([
            "合计", "", "", "", "", "",
            self.format_currency(total_amount), ""
        ])
        
        # 创建明细表格
        detail_table = self.create_professional_table(
            detail_data,
            headers,
            col_widths=[12*mm, 40*mm, 25*mm, 18*mm, 15*mm, 20*mm, 20*mm, 30*mm],
            has_total_row=True
        )
        
        content.append(detail_table)
        content.append(Spacer(1, 10*mm))
        
        # 创建页脚（签名栏）
        signatures = [
            {'label': '采购员', 'value': order.creator.real_name or order.creator.username},
            {'label': '审批人', 'value': ""},
            {'label': '供应商', 'value': ""},
            {'label': '日期', 'value': ""}
        ]
        
        notes = order.notes if hasattr(order, 'notes') and order.notes else None
        self.create_pdf_footer(content, signatures=signatures, notes=notes)
        
        # 生成PDF
        doc.build(content)
        
        # 返回相对路径
        return os.path.join('pdf', 'purchase_orders', filename)
