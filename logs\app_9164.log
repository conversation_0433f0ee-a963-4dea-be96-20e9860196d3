2025-06-22 23:15:29,364 INFO: 应用启动 - PID: 9164 [in C:\StudentsCMSSP\app\__init__.py:868]
2025-06-23 08:55:18,829 INFO: 查询菜谱：日期=2025-06-23, 星期=0(0=周一), day_of_week=1, 餐次=午餐, 区域ID=44 [in C:\StudentsCMSSP\app\routes\food_trace.py:324]
2025-06-23 08:55:18,829 INFO: 找到 1 个周菜单 [in C:\StudentsCMSSP\app\routes\food_trace.py:334]
2025-06-23 08:55:18,829 INFO: 匹配条件的食谱有 7 个 [in C:\StudentsCMSSP\app\routes\food_trace.py:346]
2025-06-23 08:55:18,845 INFO:   - 食谱: 剁椒笋片（海淀区中关村第一小学版） [in C:\StudentsCMSSP\app\routes\food_trace.py:361]
2025-06-23 08:55:18,845 INFO:   - 食谱: 西红柿炒蛋（海淀区中关村第一小学版） [in C:\StudentsCMSSP\app\routes\food_trace.py:361]
2025-06-23 08:55:18,860 INFO:   - 食谱: 韭菜炒豆芽（海淀区中关村第一小学版） [in C:\StudentsCMSSP\app\routes\food_trace.py:361]
2025-06-23 08:55:18,860 INFO:   - 食谱: 刨花青笋（海淀区中关村第一小学版） [in C:\StudentsCMSSP\app\routes\food_trace.py:361]
2025-06-23 08:55:18,876 INFO:   - 食谱: 大蒜辣椒炒油渣（海淀区中关村第一小学版） [in C:\StudentsCMSSP\app\routes\food_trace.py:361]
2025-06-23 08:55:18,876 INFO:   - 食谱: 大碗长豆角（海淀区中关村第一小学版） [in C:\StudentsCMSSP\app\routes\food_trace.py:361]
2025-06-23 08:55:18,886 INFO:   - 食谱: 蒸蛋羹（海淀区中关村第一小学版） [in C:\StudentsCMSSP\app\routes\food_trace.py:361]
2025-06-23 08:55:18,892 INFO: 食材一致性分析完成: 匹配率=0.0%, 缺失=11, 多余=0 [in C:\StudentsCMSSP\app\routes\food_trace.py:531]
2025-06-23 09:39:10,688 WARNING: [安全监控] 2025-06-23 09:39:10 - 可疑请求 | IP: ************** | 路径: /example.com:443, 指标: 可疑HTTP方法 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 09:39:12,016 WARNING: [安全监控] 2025-06-23 09:39:12 - 可疑请求 | IP: ************** | 路径: /example.com:443, 指标: 可疑HTTP方法 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 09:54:59,840 WARNING: [安全监控] 2025-06-23 09:54:59 - 可疑请求 | IP: **************:44900 | 路径: /, 指标: 无User-Agent [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 10:39:54,268 WARNING: [安全监控] 2025-06-23 10:39:54 - 可疑请求 | IP: *************:44520 | 路径: /wordpress/wp-admin/setup-config.php, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 10:42:30,358 WARNING: [安全监控] 2025-06-23 10:42:30 - 可疑请求 | IP: **************:60124 | 路径: /wp-admin/setup-config.php, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 12:47:31,894 WARNING: [安全监控] 2025-06-23 12:47:31 - 可疑请求 | IP: *************:34694 | 路径: /, 指标: 无User-Agent [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 13:23:14,221 WARNING: [安全监控] 2025-06-23 13:23:14 - 可疑请求 | IP: ************:45170 | 路径: /.env, 指标: 可疑机器人User-Agent, 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 13:23:14,911 WARNING: [安全监控] 2025-06-23 13:23:14 - 可疑请求 | IP: ************:45182 | 路径: /.git/config, 指标: 可疑机器人User-Agent, 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 13:23:21,476 WARNING: [安全监控] 2025-06-23 13:23:21 - 可疑请求 | IP: ************** | 路径: /, 指标: 无User-Agent [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 13:23:50,528 WARNING: [安全监控] 2025-06-23 13:23:50 - 可疑请求 | IP: *************:56106 | 路径: /sites/all/libraries/mailchimp/.env, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 13:26:14,058 WARNING: [安全监控] 2025-06-23 13:26:14 - 可疑请求 | IP: ***********:62645 | 路径: /xmlrpc.php, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 13:43:46,672 WARNING: [安全监控] 2025-06-23 13:43:46 - 可疑请求 | IP: *************:52678 | 路径: /, 指标: 无User-Agent [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 13:47:46,027 WARNING: [安全监控] 2025-06-23 13:47:46 - 可疑请求 | IP: ***************:60790 | 路径: /, 指标: 可疑机器人User-Agent [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 14:15:04,790 WARNING: [安全监控] 2025-06-23 14:15:04 - 可疑请求 | IP: *************:33388 | 路径: /.git/objects/, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 14:16:38,756 WARNING: [安全监控] 2025-06-23 14:16:38 - 可疑请求 | IP: **************:36466 | 路径: /, 指标: 无User-Agent [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 14:32:18,018 WARNING: [安全监控] 2025-06-23 14:32:18 - 可疑请求 | IP: ************:61306 | 路径: /xmlrpc.php, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 14:46:16,348 WARNING: [安全监控] 2025-06-23 14:46:16 - 可疑请求 | IP: ************:44976 | 路径: /.git/HEAD, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 15:54:28,395 WARNING: [安全监控] 2025-06-23 15:54:28 - 可疑请求 | IP: ************* | 路径: /, 指标: 无User-Agent [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 16:41:48,632 ERROR: Exception on /daily-management/ [GET] [in C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py:838]
Traceback (most recent call last):
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 1473, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 882, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_cors\extension.py", line 194, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 880, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 865, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
  File "C:\StudentsCMSSP\app\routes\daily_management\routes.py", line 102, in index
    return render_template('daily_management/simplified_dashboard.html',
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 1301, in render
    self.environment.handle_exception()
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 936, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "C:\StudentsCMSSP\app\templates\daily_management\simplified_dashboard.html", line 5, in top-level template code
    {% from 'daily_management/components/qrcode_card_widget.html' import qrcode_card_widget %}
  File "C:\StudentsCMSSP\app\templates\base.html", line 481, in top-level template code
    {% block content %}{% endblock %}
  File "C:\StudentsCMSSP\app\templates\daily_management\simplified_dashboard.html", line 312, in block 'content'
    value="{{ (today - timedelta(days=30)).strftime('%Y-%m-%d') }}">
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\utils.py", line 83, in from_obj
    if hasattr(obj, "jinja_pass_arg"):
jinja2.exceptions.UndefinedError: 'timedelta' is undefined
2025-06-23 16:41:52,910 INFO: 生成固定二维码 - 用户: guest_demo, 学校: 海淀区中关村第一小学 (ID: 44) [in C:\StudentsCMSSP\app\routes\daily_management\inspection_qrcode.py:31]
2025-06-23 16:41:52,910 INFO: 员工上传URL: http://xiaoyuanst.com/daily-management/public/inspections/select-date/44/upload [in C:\StudentsCMSSP\app\routes\daily_management\inspection_qrcode.py:38]
2025-06-23 16:41:52,942 INFO: 成功生成二维码base64，数据长度: 1236 [in C:\StudentsCMSSP\app\utils\qrcode_helper.py:78]
2025-06-23 16:41:52,942 INFO: 管理员评分URL: http://xiaoyuanst.com/daily-management/public/inspections/select-date/44/rate [in C:\StudentsCMSSP\app\routes\daily_management\inspection_qrcode.py:46]
2025-06-23 16:41:52,957 INFO: 成功生成二维码base64，数据长度: 1084 [in C:\StudentsCMSSP\app\utils\qrcode_helper.py:78]
2025-06-23 16:42:52,440 INFO: 解除了 1 个入库单对凭证 54 的引用 [in C:\StudentsCMSSP\app\routes\financial\vouchers.py:693]
2025-06-23 16:42:52,503 INFO: 成功删除财务凭证 54 [in C:\StudentsCMSSP\app\routes\financial\vouchers.py:715]
2025-06-23 16:43:04,629 INFO: 食材分类映射: 其他 -> 1201(原材料), 金额: 1948.4, 明细: 面粉(100公斤)、大米(108.3公斤)、面条(47.0公斤) [in C:\StudentsCMSSP\app\routes\financial\vouchers.py:1945]
2025-06-23 16:43:04,633 INFO: 食材分类映射: 肉类 -> 120102(肉类), 金额: 8300.0, 明细: 猪肉(100公斤)、鸡蛋(100公斤) [in C:\StudentsCMSSP\app\routes\financial\vouchers.py:1945]
2025-06-23 16:43:04,661 INFO: 财务凭证生成成功: 凭证号=8, 入库单=RK20250614230014, 供应商=绿色农场有限公司, 总金额=10248.40 [in C:\StudentsCMSSP\app\routes\financial\vouchers.py:1998]
2025-06-23 16:43:45,346 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\StudentsCMSSP\app\routes\inventory.py:151]
2025-06-23 16:43:50,107 INFO: 使用消耗计划的区域ID: 44 [in C:\StudentsCMSSP\app\routes\stock_out.py:199]
2025-06-23 16:43:50,123 INFO: 通过消耗计划信息读取菜谱：日期=2025-06-09, 餐次=早餐+午餐+晚餐, 区域ID=44 [in C:\StudentsCMSSP\app\routes\stock_out.py:206]
2025-06-23 16:43:50,123 INFO: 查询周菜单：日期=2025-06-09, 星期=0(0=周一), day_of_week=1, 餐次=早餐+午餐+晚餐, 区域ID=44 [in C:\StudentsCMSSP\app\routes\stock_out.py:217]
2025-06-23 16:43:50,123 INFO: 找到 1 个周菜单 [in C:\StudentsCMSSP\app\routes\stock_out.py:227]
2025-06-23 16:43:50,123 INFO:   - 周菜单ID: 41, 开始日期: 2025-06-09, 结束日期: 2025-06-15, 状态: 已发布 [in C:\StudentsCMSSP\app\routes\stock_out.py:229]
2025-06-23 16:43:50,123 INFO: 周菜单 41 总共有 57 条食谱记录 [in C:\StudentsCMSSP\app\routes\stock_out.py:235]
2025-06-23 16:43:50,123 INFO:   - 食谱记录: day_of_week=1, meal_type='早餐', recipe_id=404 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-23 16:43:50,123 INFO:   - 食谱记录: day_of_week=1, meal_type='早餐', recipe_id=402 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-23 16:43:50,123 INFO:   - 食谱记录: day_of_week=1, meal_type='早餐', recipe_id=396 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-23 16:43:50,123 INFO:   - 食谱记录: day_of_week=1, meal_type='午餐', recipe_id=398 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-23 16:43:50,123 INFO:   - 食谱记录: day_of_week=1, meal_type='午餐', recipe_id=401 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-23 16:43:50,123 INFO:   - 食谱记录: day_of_week=1, meal_type='午餐', recipe_id=397 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-23 16:43:50,123 INFO:   - 食谱记录: day_of_week=1, meal_type='午餐', recipe_id=400 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-23 16:43:50,123 INFO:   - 食谱记录: day_of_week=1, meal_type='晚餐', recipe_id=386 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-23 16:43:50,123 INFO:   - 食谱记录: day_of_week=1, meal_type='晚餐', recipe_id=385 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-23 16:43:50,123 INFO:   - 食谱记录: day_of_week=1, meal_type='晚餐', recipe_id=381 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-23 16:43:50,123 INFO:   - 食谱记录: day_of_week=1, meal_type='晚餐', recipe_id=394 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-23 16:43:50,123 INFO:   - 食谱记录: day_of_week=5, meal_type='早餐', recipe_id=404 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-23 16:43:50,123 INFO:   - 食谱记录: day_of_week=5, meal_type='早餐', recipe_id=402 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-23 16:43:50,123 INFO:   - 食谱记录: day_of_week=5, meal_type='早餐', recipe_id=392 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-23 16:43:50,123 INFO:   - 食谱记录: day_of_week=5, meal_type='早餐', recipe_id=396 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-23 16:43:50,123 INFO:   - 食谱记录: day_of_week=5, meal_type='午餐', recipe_id=398 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-23 16:43:50,123 INFO:   - 食谱记录: day_of_week=5, meal_type='午餐', recipe_id=395 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-23 16:43:50,123 INFO:   - 食谱记录: day_of_week=5, meal_type='午餐', recipe_id=391 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-23 16:43:50,123 INFO:   - 食谱记录: day_of_week=5, meal_type='午餐', recipe_id=384 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-23 16:43:50,123 INFO:   - 食谱记录: day_of_week=5, meal_type='午餐', recipe_id=385 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-23 16:43:50,123 INFO:   - 食谱记录: day_of_week=5, meal_type='午餐', recipe_id=399 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-23 16:43:50,123 INFO:   - 食谱记录: day_of_week=5, meal_type='晚餐', recipe_id=394 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-23 16:43:50,123 INFO:   - 食谱记录: day_of_week=5, meal_type='晚餐', recipe_id=393 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-23 16:43:50,123 INFO:   - 食谱记录: day_of_week=5, meal_type='晚餐', recipe_id=378 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-23 16:43:50,123 INFO:   - 食谱记录: day_of_week=5, meal_type='晚餐', recipe_id=381 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-23 16:43:50,123 INFO:   - 食谱记录: day_of_week=5, meal_type='晚餐', recipe_id=401 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-23 16:43:50,138 INFO:   - 食谱记录: day_of_week=6, meal_type='早餐', recipe_id=404 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-23 16:43:50,138 INFO:   - 食谱记录: day_of_week=6, meal_type='早餐', recipe_id=396 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-23 16:43:50,138 INFO:   - 食谱记录: day_of_week=6, meal_type='早餐', recipe_id=392 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-23 16:43:50,138 INFO:   - 食谱记录: day_of_week=6, meal_type='早餐', recipe_id=402 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-23 16:43:50,138 INFO:   - 食谱记录: day_of_week=6, meal_type='午餐', recipe_id=392 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-23 16:43:50,138 INFO:   - 食谱记录: day_of_week=6, meal_type='午餐', recipe_id=395 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-23 16:43:50,138 INFO:   - 食谱记录: day_of_week=6, meal_type='午餐', recipe_id=397 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-23 16:43:50,138 INFO:   - 食谱记录: day_of_week=6, meal_type='午餐', recipe_id=382 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-23 16:43:50,138 INFO:   - 食谱记录: day_of_week=6, meal_type='午餐', recipe_id=394 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-23 16:43:50,138 INFO:   - 食谱记录: day_of_week=6, meal_type='晚餐', recipe_id=388 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-23 16:43:50,138 INFO:   - 食谱记录: day_of_week=6, meal_type='晚餐', recipe_id=385 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-23 16:43:50,138 INFO:   - 食谱记录: day_of_week=6, meal_type='晚餐', recipe_id=390 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-23 16:43:50,138 INFO:   - 食谱记录: day_of_week=6, meal_type='晚餐', recipe_id=386 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-23 16:43:50,138 INFO:   - 食谱记录: day_of_week=6, meal_type='晚餐', recipe_id=394 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-23 16:43:50,138 INFO:   - 食谱记录: day_of_week=7, meal_type='早餐', recipe_id=404 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-23 16:43:50,138 INFO:   - 食谱记录: day_of_week=7, meal_type='早餐', recipe_id=402 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-23 16:43:50,138 INFO:   - 食谱记录: day_of_week=7, meal_type='早餐', recipe_id=392 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-23 16:43:50,138 INFO:   - 食谱记录: day_of_week=7, meal_type='早餐', recipe_id=396 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-23 16:43:50,138 INFO:   - 食谱记录: day_of_week=7, meal_type='午餐', recipe_id=398 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-23 16:43:50,138 INFO:   - 食谱记录: day_of_week=7, meal_type='午餐', recipe_id=401 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-23 16:43:50,138 INFO:   - 食谱记录: day_of_week=7, meal_type='午餐', recipe_id=395 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-23 16:43:50,138 INFO:   - 食谱记录: day_of_week=7, meal_type='午餐', recipe_id=391 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-23 16:43:50,138 INFO:   - 食谱记录: day_of_week=7, meal_type='午餐', recipe_id=394 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-23 16:43:50,138 INFO:   - 食谱记录: day_of_week=7, meal_type='午餐', recipe_id=400 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-23 16:43:50,138 INFO:   - 食谱记录: day_of_week=7, meal_type='午餐', recipe_id=392 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-23 16:43:50,138 INFO:   - 食谱记录: day_of_week=7, meal_type='晚餐', recipe_id=398 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-23 16:43:50,138 INFO:   - 食谱记录: day_of_week=7, meal_type='晚餐', recipe_id=399 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-23 16:43:50,138 INFO:   - 食谱记录: day_of_week=7, meal_type='晚餐', recipe_id=397 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-23 16:43:50,138 INFO:   - 食谱记录: day_of_week=7, meal_type='晚餐', recipe_id=393 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-23 16:43:50,138 INFO:   - 食谱记录: day_of_week=7, meal_type='晚餐', recipe_id=400 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-23 16:43:50,138 INFO:   - 食谱记录: day_of_week=7, meal_type='晚餐', recipe_id=392 [in C:\StudentsCMSSP\app\routes\stock_out.py:238]
2025-06-23 16:43:50,138 INFO: 匹配条件 day_of_week=1, meal_type='早餐+午餐+晚餐' 的食谱有 0 个 [in C:\StudentsCMSSP\app\routes\stock_out.py:247]
2025-06-23 16:43:50,148 INFO: 从周菜单读取到 0 个食谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:250]
2025-06-23 16:43:50,148 INFO: 最终获取到 0 个食谱用于关联显示 [in C:\StudentsCMSSP\app\routes\stock_out.py:255]
2025-06-23 16:43:50,148 INFO: 步骤1: 读取消耗日期: 2025-06-09, 餐次: 早餐+午餐+晚餐 [in C:\StudentsCMSSP\app\routes\stock_out.py:27]
2025-06-23 16:43:50,155 INFO: 步骤1完成: 找到 0 个菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:58]
2025-06-23 16:43:50,155 INFO: 步骤2: 为出库食材 '猪肉' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-23 16:43:50,155 INFO: 步骤2: 为出库食材 '鸡蛋' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-23 16:43:50,155 INFO: 步骤2: 为出库食材 '鸡蛋' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-23 16:43:50,155 INFO: 步骤2: 为出库食材 '五花肉' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-23 16:43:50,155 INFO: 步骤2: 为出库食材 '猪肉' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-23 16:43:50,155 INFO: 步骤2: 为出库食材 '胡萝卜' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-23 16:43:50,155 INFO: 步骤2: 为出库食材 '西红柿' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-23 16:43:50,155 INFO: 步骤2: 为出库食材 '白菜' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-23 16:43:50,170 INFO: 步骤2: 为出库食材 '洋葱' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-23 16:43:50,170 INFO: 步骤2: 为出库食材 '白菜' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-23 16:43:50,170 INFO: 步骤2: 为出库食材 '姜片' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-23 16:43:50,170 INFO: 步骤2: 为出库食材 '葱段' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-23 16:43:50,170 INFO: 步骤2: 为出库食材 '包菜' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-23 16:43:50,170 INFO: 步骤2: 为出库食材 '彩椒' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-23 16:43:50,170 INFO: 步骤2: 为出库食材 '面粉' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-23 16:43:50,170 INFO: 步骤2: 为出库食材 '面条' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-23 16:43:50,170 INFO: 步骤2: 为出库食材 '豌豆' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-23 16:43:50,170 INFO: 步骤2: 为出库食材 '豆腐' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-23 16:43:50,170 INFO: 步骤2: 为出库食材 '豆角' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-23 16:43:50,170 INFO: 步骤2: 为出库食材 '豇豆' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-23 16:43:50,170 INFO: 步骤2: 为出库食材 '土豆' 查找对应菜谱 [in C:\StudentsCMSSP\app\routes\stock_out.py:65]
2025-06-23 16:43:50,170 INFO: 溯源完成: 找到 0 条溯源记录 [in C:\StudentsCMSSP\app\routes\stock_out.py:122]
2025-06-23 16:44:30,999 WARNING: [安全监控] 2025-06-23 16:44:30 - 可疑请求 | IP: ************** | 路径: /, 指标: 无User-Agent [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 16:44:40,145 INFO: 成功生成二维码，数据: http://xiaoyuanst.com/food-trace/recipe/401/2025-06-17/%E5%8D%88%E9%A4%90/44... [in C:\StudentsCMSSP\app\routes\stock_in.py:2986]
2025-06-23 16:45:57,880 INFO: 当前用户: guest_demo [in C:\StudentsCMSSP\app\routes\consumption_plan.py:94]
2025-06-23 16:45:57,880 INFO: 用户区域ID: 44 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:95]
2025-06-23 16:45:57,880 INFO: 用户区域名称: 海淀区中关村第一小学 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:96]
2025-06-23 16:45:57,880 INFO: 是否管理员: 0 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:97]
2025-06-23 16:46:01,893 WARNING: [安全监控] 2025-06-23 16:46:01 - 可疑请求 | IP: *************:45016 | 路径: /, 指标: 无User-Agent [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 16:46:06,563 INFO: 收到食谱分析请求: {'area_id': 44, 'consumption_date': '2025-06-23', 'meal_types': ['午餐']} [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:322]
2025-06-23 16:46:06,579 INFO: 查询日期: 2025-06-23, 区域: 44, 餐次: ['午餐'] [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:340]
2025-06-23 16:46:06,579 INFO: 查询餐次: 午餐 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:345]
2025-06-23 16:46:06,579 INFO: 日菜单功能已移除，从周菜单获取食谱 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:348]
2025-06-23 16:46:32,119 INFO: 收到食谱分析请求: {'area_id': 44, 'consumption_date': '2025-06-23', 'meal_types': ['晚餐']} [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:322]
2025-06-23 16:46:32,119 INFO: 查询日期: 2025-06-23, 区域: 44, 餐次: ['晚餐'] [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:340]
2025-06-23 16:46:32,119 INFO: 查询餐次: 晚餐 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:345]
2025-06-23 16:46:32,119 INFO: 日菜单功能已移除，从周菜单获取食谱 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:348]
2025-06-23 16:46:59,024 ERROR: 获取食材供应商失败: 'SupplierProduct' object has no attribute 'unit' [in C:\StudentsCMSSP\app\routes\purchase_order.py:2165]
2025-06-23 16:46:59,024 ERROR: 获取食材供应商失败: 'SupplierProduct' object has no attribute 'unit' [in C:\StudentsCMSSP\app\routes\purchase_order.py:2165]
2025-06-23 16:46:59,024 ERROR: 获取食材供应商失败: 'SupplierProduct' object has no attribute 'unit' [in C:\StudentsCMSSP\app\routes\purchase_order.py:2165]
2025-06-23 16:46:59,050 ERROR: 获取食材供应商失败: 'SupplierProduct' object has no attribute 'unit' [in C:\StudentsCMSSP\app\routes\purchase_order.py:2165]
2025-06-23 16:46:59,056 ERROR: 获取食材供应商失败: 'SupplierProduct' object has no attribute 'unit' [in C:\StudentsCMSSP\app\routes\purchase_order.py:2165]
2025-06-23 16:46:59,056 ERROR: 获取食材供应商失败: 'SupplierProduct' object has no attribute 'unit' [in C:\StudentsCMSSP\app\routes\purchase_order.py:2165]
2025-06-23 16:47:15,675 INFO: 收到食谱分析请求: {'area_id': 44, 'consumption_date': '2025-06-23', 'meal_types': ['晚餐']} [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:322]
2025-06-23 16:47:15,690 INFO: 查询日期: 2025-06-23, 区域: 44, 餐次: ['晚餐'] [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:340]
2025-06-23 16:47:15,690 INFO: 查询餐次: 晚餐 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:345]
2025-06-23 16:47:15,690 INFO: 日菜单功能已移除，从周菜单获取食谱 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:348]
2025-06-23 16:47:24,407 ERROR: 获取食材供应商失败: 'SupplierProduct' object has no attribute 'unit' [in C:\StudentsCMSSP\app\routes\purchase_order.py:2165]
2025-06-23 16:47:24,422 ERROR: 获取食材供应商失败: 'SupplierProduct' object has no attribute 'unit' [in C:\StudentsCMSSP\app\routes\purchase_order.py:2165]
2025-06-23 16:47:24,438 ERROR: 获取食材供应商失败: 'SupplierProduct' object has no attribute 'unit' [in C:\StudentsCMSSP\app\routes\purchase_order.py:2165]
2025-06-23 16:47:24,438 ERROR: 获取食材供应商失败: 'SupplierProduct' object has no attribute 'unit' [in C:\StudentsCMSSP\app\routes\purchase_order.py:2165]
2025-06-23 16:47:24,454 ERROR: 获取食材供应商失败: 'SupplierProduct' object has no attribute 'unit' [in C:\StudentsCMSSP\app\routes\purchase_order.py:2165]
2025-06-23 16:47:24,464 ERROR: 获取食材供应商失败: 'SupplierProduct' object has no attribute 'unit' [in C:\StudentsCMSSP\app\routes\purchase_order.py:2165]
2025-06-23 16:48:23,619 ERROR: 获取食材供应商失败: 'SupplierProduct' object has no attribute 'unit' [in C:\StudentsCMSSP\app\routes\purchase_order.py:2165]
2025-06-23 16:48:23,619 ERROR: 获取食材供应商失败: 'SupplierProduct' object has no attribute 'unit' [in C:\StudentsCMSSP\app\routes\purchase_order.py:2165]
2025-06-23 16:48:23,619 ERROR: 获取食材供应商失败: 'SupplierProduct' object has no attribute 'unit' [in C:\StudentsCMSSP\app\routes\purchase_order.py:2165]
2025-06-23 16:48:23,619 ERROR: 获取食材供应商失败: 'SupplierProduct' object has no attribute 'unit' [in C:\StudentsCMSSP\app\routes\purchase_order.py:2165]
2025-06-23 16:48:23,635 ERROR: 获取食材供应商失败: 'SupplierProduct' object has no attribute 'unit' [in C:\StudentsCMSSP\app\routes\purchase_order.py:2165]
2025-06-23 16:48:23,635 ERROR: 获取食材供应商失败: 'SupplierProduct' object has no attribute 'unit' [in C:\StudentsCMSSP\app\routes\purchase_order.py:2165]
2025-06-23 16:48:23,666 ERROR: 获取食材供应商失败: 'SupplierProduct' object has no attribute 'unit' [in C:\StudentsCMSSP\app\routes\purchase_order.py:2165]
2025-06-23 16:48:23,666 ERROR: 获取食材供应商失败: 'SupplierProduct' object has no attribute 'unit' [in C:\StudentsCMSSP\app\routes\purchase_order.py:2165]
2025-06-23 16:48:49,281 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\StudentsCMSSP\app\routes\inventory.py:151]
2025-06-23 16:49:05,017 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\StudentsCMSSP\app\routes\inventory.py:151]
2025-06-23 16:49:11,003 INFO: 库存统计页面：为区域 [44] 找到 13 个关联供应商 [in C:\StudentsCMSSP\app\routes\inventory.py:1021]
2025-06-23 16:49:20,939 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\StudentsCMSSP\app\routes\inventory.py:151]
2025-06-23 16:49:26,268 INFO: 库存汇总查询条件: 状态=正常, 最小数量=0.001 [in C:\StudentsCMSSP\app\routes\inventory.py:317]
2025-06-23 16:49:45,860 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\StudentsCMSSP\app\routes\inventory.py:151]
2025-06-23 16:54:10,319 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\StudentsCMSSP\app\routes\inventory.py:151]
2025-06-23 16:55:43,260 INFO: 库存统计页面：为区域 [44] 找到 13 个关联供应商 [in C:\StudentsCMSSP\app\routes\inventory.py:1021]
2025-06-23 16:56:21,137 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\StudentsCMSSP\app\routes\inventory.py:151]
