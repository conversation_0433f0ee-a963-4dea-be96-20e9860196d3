# StudentsCMSSP导出功能代码改进方案

"""
统一导出服务实现
整合MCP库功能，提供标准化的导出接口
"""

from flask import current_app, send_file, jsonify
from datetime import datetime
import os
import tempfile
import pandas as pd
from openpyxl import Workbook
from openpyxl.styles import Font, Alignment, Border, Side, PatternFill
from reportlab.lib.pagesizes import A4
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph
from reportlab.lib import colors

class UnifiedExportService:
    """统一导出服务"""
    
    def __init__(self):
        self.setup_export_directories()
        self.setup_templates()
    
    def setup_export_directories(self):
        """设置导出目录"""
        self.export_dirs = {
            'excel': os.path.join(current_app.root_path, 'static', 'excel'),
            'pdf': os.path.join(current_app.root_path, 'static', 'pdf'),
            'templates': os.path.join(current_app.root_path, 'static', 'export_templates')
        }
        
        for dir_path in self.export_dirs.values():
            if not os.path.exists(dir_path):
                os.makedirs(dir_path)
    
    def setup_templates(self):
        """设置导出模板"""
        self.templates = {
            'supplier_list': {
                'headers': ['供应商名称', '联系人', '电话', '地址', '邮箱', '状态', '创建时间'],
                'columns': ['name', 'contact_person', 'phone', 'address', 'email', 'status', 'created_at'],
                'title': '供应商信息列表'
            },
            'inventory_list': {
                'headers': ['食材名称', '当前库存', '单位', '存储位置', '批次号', '生产日期', '过期日期', '供应商'],
                'columns': ['ingredient_name', 'current_stock', 'unit', 'storage_location', 'batch_number', 'production_date', 'expiry_date', 'supplier_name'],
                'title': '库存清单'
            },
            'daily_inspection': {
                'headers': ['检查日期', '检查类型', '检查项目', '检查结果', '问题描述', '整改措施', '检查人'],
                'columns': ['inspection_date', 'inspection_type', 'inspection_item', 'result', 'issue_description', 'corrective_action', 'inspector'],
                'title': '日常检查记录'
            },
            'companion_dining': {
                'headers': ['陪餐日期', '陪餐人', '餐次', '菜品评价', '问题反馈', '建议'],
                'columns': ['dining_date', 'companion_name', 'meal_type', 'food_rating', 'feedback', 'suggestions'],
                'title': '陪餐记录'
            },
            'training_records': {
                'headers': ['培训日期', '培训主题', '培训类型', '参与人员', '培训时长', '培训效果', '备注'],
                'columns': ['training_date', 'training_topic', 'training_type', 'participants', 'duration', 'effectiveness', 'notes'],
                'title': '培训记录'
            }
        }
    
    def export_to_excel(self, data, template_name, filename=None, user_area=None):
        """导出数据到Excel"""
        try:
            template = self.templates.get(template_name)
            if not template:
                raise ValueError(f"未找到模板: {template_name}")
            
            # 生成文件名
            if not filename:
                timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
                filename = f"{template['title']}_{timestamp}.xlsx"
            
            # 创建临时文件
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.xlsx')
            temp_file.close()
            
            # 创建工作簿
            wb = Workbook()
            ws = wb.active
            ws.title = template['title']
            
            # 设置标题
            if user_area:
                title = f"{user_area.name} - {template['title']}"
                ws.merge_cells('A1:' + chr(65 + len(template['headers']) - 1) + '1')
                ws['A1'] = title
                ws['A1'].font = Font(name='宋体', size=16, bold=True)
                ws['A1'].alignment = Alignment(horizontal='center', vertical='center')
                start_row = 3
            else:
                start_row = 1
            
            # 设置表头
            for col, header in enumerate(template['headers'], 1):
                cell = ws.cell(row=start_row, column=col, value=header)
                cell.font = Font(name='宋体', size=12, bold=True)
                cell.alignment = Alignment(horizontal='center', vertical='center')
                cell.fill = PatternFill(start_color='D9D9D9', end_color='D9D9D9', fill_type='solid')
                cell.border = Border(
                    left=Side(style='thin'),
                    right=Side(style='thin'),
                    top=Side(style='thin'),
                    bottom=Side(style='thin')
                )
            
            # 填充数据
            for row_idx, item in enumerate(data, start_row + 1):
                for col_idx, column in enumerate(template['columns'], 1):
                    value = self._get_nested_value(item, column)
                    cell = ws.cell(row=row_idx, column=col_idx, value=value)
                    cell.font = Font(name='宋体', size=10)
                    cell.alignment = Alignment(horizontal='left', vertical='center')
                    cell.border = Border(
                        left=Side(style='thin'),
                        right=Side(style='thin'),
                        top=Side(style='thin'),
                        bottom=Side(style='thin')
                    )
            
            # 自动调整列宽
            for column in ws.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)
                ws.column_dimensions[column_letter].width = adjusted_width
            
            # 保存文件
            wb.save(temp_file.name)
            
            return send_file(
                temp_file.name,
                as_attachment=True,
                download_name=filename,
                mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            )
            
        except Exception as e:
            current_app.logger.error(f"Excel导出失败: {str(e)}")
            return jsonify({'success': False, 'message': f'导出失败: {str(e)}'}), 500
    
    def _get_nested_value(self, obj, key):
        """获取嵌套对象的值"""
        try:
            if hasattr(obj, key):
                value = getattr(obj, key)
            elif isinstance(obj, dict):
                value = obj.get(key)
            else:
                return ""
            
            # 格式化特殊类型
            if hasattr(value, 'strftime'):  # 日期时间类型
                return value.strftime('%Y-%m-%d %H:%M:%S')
            elif value is None:
                return ""
            else:
                return str(value)
        except:
            return ""

# 供应商导出功能实现
def export_suppliers_excel(suppliers, user_area):
    """导出供应商Excel"""
    export_service = UnifiedExportService()
    
    # 准备数据
    data = []
    for supplier in suppliers:
        data.append({
            'name': supplier.name,
            'contact_person': supplier.contact_person,
            'phone': supplier.phone,
            'address': supplier.address,
            'email': supplier.email or '',
            'status': '启用' if supplier.is_active else '禁用',
            'created_at': supplier.created_at.strftime('%Y-%m-%d') if supplier.created_at else ''
        })
    
    return export_service.export_to_excel(data, 'supplier_list', user_area=user_area)

# 库存导出功能实现
def export_inventory_excel(inventories, user_area):
    """导出库存Excel"""
    export_service = UnifiedExportService()
    
    # 准备数据
    data = []
    for inventory in inventories:
        data.append({
            'ingredient_name': inventory.ingredient.name,
            'current_stock': f"{inventory.current_stock:.2f}",
            'unit': inventory.unit,
            'storage_location': inventory.storage_location.name if inventory.storage_location else '',
            'batch_number': inventory.batch_number or '',
            'production_date': inventory.production_date.strftime('%Y-%m-%d') if inventory.production_date else '',
            'expiry_date': inventory.expiry_date.strftime('%Y-%m-%d') if inventory.expiry_date else '',
            'supplier_name': inventory.supplier.name if inventory.supplier else ''
        })
    
    return export_service.export_to_excel(data, 'inventory_list', user_area=user_area)

# 日常管理导出功能实现
def export_inspection_records_excel(records, user_area):
    """导出检查记录Excel"""
    export_service = UnifiedExportService()
    
    # 准备数据
    data = []
    for record in records:
        data.append({
            'inspection_date': record.inspection_date.strftime('%Y-%m-%d'),
            'inspection_type': record.inspection_type,
            'inspection_item': record.inspection_item,
            'result': record.result,
            'issue_description': record.issue_description or '',
            'corrective_action': record.corrective_action or '',
            'inspector': record.inspector.real_name if record.inspector else ''
        })
    
    return export_service.export_to_excel(data, 'daily_inspection', user_area=user_area)

def export_companion_records_excel(records, user_area):
    """导出陪餐记录Excel"""
    export_service = UnifiedExportService()
    
    # 准备数据
    data = []
    for record in records:
        data.append({
            'dining_date': record.dining_date.strftime('%Y-%m-%d'),
            'companion_name': record.companion_name,
            'meal_type': record.meal_type,
            'food_rating': record.food_rating or '',
            'feedback': record.feedback or '',
            'suggestions': record.suggestions or ''
        })
    
    return export_service.export_to_excel(data, 'companion_dining', user_area=user_area)

# 路由实现示例
"""
# 在 app/routes/supplier.py 中添加
@supplier_bp.route('/export')
@login_required
@school_required
def export_suppliers(user_area):
    suppliers = Supplier.query.filter_by(area_id=user_area.id).all()
    return export_suppliers_excel(suppliers, user_area)

# 在 app/routes/inventory.py 中添加
@inventory_bp.route('/export')
@login_required
@school_required
def export_inventory(user_area):
    inventories = Inventory.query.join(Warehouse).filter(
        Warehouse.area_id == user_area.id
    ).all()
    return export_inventory_excel(inventories, user_area)

# 在 app/routes/daily_management/routes.py 中添加
@daily_management_bp.route('/export/inspection')
@login_required
@school_required
def export_inspection_records(user_area):
    records = InspectionRecord.query.filter_by(area_id=user_area.id).all()
    return export_inspection_records_excel(records, user_area)
"""

# MCP库集成示例
class MCPEnhancedExportService(UnifiedExportService):
    """使用MCP库增强的导出服务"""
    
    def create_financial_template(self, report_type):
        """创建财务报表模板"""
        try:
            # 使用MCP Excel服务创建财务报表模板
            template_path = f"app/static/export_templates/财务_{report_type}_模板.xlsx"
            
            # 这里可以调用MCP Excel服务
            # mcp_excel_service.create_financial_report(template_path, report_type)
            
            return template_path
        except Exception as e:
            current_app.logger.error(f"创建财务模板失败: {str(e)}")
            raise
    
    def export_with_template(self, data, template_path, output_path):
        """使用模板导出数据"""
        try:
            # 使用MCP Excel服务进行模板导出
            # mcp_excel_service.write_excel(output_path, data)
            
            return output_path
        except Exception as e:
            current_app.logger.error(f"模板导出失败: {str(e)}")
            raise

# 全局导出服务实例
export_service = UnifiedExportService()
mcp_export_service = MCPEnhancedExportService()
