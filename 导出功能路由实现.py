# 导出功能路由实现
# 为各个模块添加完整的导出功能

"""
供应商管理导出功能
在 app/routes/supplier.py 中添加以下代码
"""

from flask import request, jsonify, current_app
from flask_login import login_required
from app.utils.school_required import school_required
from app.models import Supplier
from datetime import datetime, timedelta

# 供应商导出路由
@supplier_bp.route('/export')
@login_required
@school_required
def export_suppliers(user_area):
    """导出供应商信息"""
    try:
        # 获取查询参数
        export_format = request.args.get('format', 'excel')
        status_filter = request.args.get('status', 'all')
        
        # 构建查询
        query = Supplier.query.filter_by(area_id=user_area.id)
        
        if status_filter == 'active':
            query = query.filter_by(is_active=True)
        elif status_filter == 'inactive':
            query = query.filter_by(is_active=False)
        
        suppliers = query.order_by(Supplier.created_at.desc()).all()
        
        if not suppliers:
            return jsonify({'success': False, 'message': '没有找到供应商数据'}), 400
        
        # 导出数据
        from app.utils.export_service import export_suppliers_excel
        return export_suppliers_excel(suppliers, user_area)
        
    except Exception as e:
        current_app.logger.error(f"导出供应商失败: {str(e)}")
        return jsonify({'success': False, 'message': f'导出失败: {str(e)}'}), 500

@supplier_bp.route('/export/statistics')
@login_required
@school_required
def export_supplier_statistics(user_area):
    """导出供应商统计报表"""
    try:
        # 获取统计数据
        from sqlalchemy import func
        from app.models import PurchaseOrder, PurchaseOrderItem
        
        # 供应商采购统计
        supplier_stats = db.session.query(
            Supplier.name,
            func.count(PurchaseOrder.id).label('order_count'),
            func.sum(PurchaseOrder.total_amount).label('total_amount'),
            func.max(PurchaseOrder.order_date).label('last_order_date')
        ).join(PurchaseOrder).filter(
            Supplier.area_id == user_area.id
        ).group_by(Supplier.id).all()
        
        # 准备导出数据
        data = []
        for stat in supplier_stats:
            data.append({
                'supplier_name': stat.name,
                'order_count': stat.order_count,
                'total_amount': f"{stat.total_amount:.2f}" if stat.total_amount else "0.00",
                'last_order_date': stat.last_order_date.strftime('%Y-%m-%d') if stat.last_order_date else ''
            })
        
        from app.utils.export_service import UnifiedExportService
        export_service = UnifiedExportService()
        
        # 使用自定义模板
        template = {
            'headers': ['供应商名称', '订单数量', '采购总额', '最后订单日期'],
            'columns': ['supplier_name', 'order_count', 'total_amount', 'last_order_date'],
            'title': '供应商采购统计'
        }
        
        return export_service.export_to_excel(data, 'supplier_statistics', user_area=user_area)
        
    except Exception as e:
        current_app.logger.error(f"导出供应商统计失败: {str(e)}")
        return jsonify({'success': False, 'message': f'导出失败: {str(e)}'}), 500

"""
库存管理导出功能
在 app/routes/inventory.py 中添加以下代码
"""

@inventory_bp.route('/export')
@login_required
@school_required
def export_inventory(user_area):
    """导出库存清单"""
    try:
        # 获取查询参数
        export_format = request.args.get('format', 'excel')
        warehouse_id = request.args.get('warehouse_id', type=int)
        low_stock_only = request.args.get('low_stock_only', 'false') == 'true'
        expired_only = request.args.get('expired_only', 'false') == 'true'
        
        # 构建查询
        from app.models import Inventory, Warehouse
        query = Inventory.query.join(Warehouse).filter(Warehouse.area_id == user_area.id)
        
        if warehouse_id:
            query = query.filter(Inventory.warehouse_id == warehouse_id)
        
        if low_stock_only:
            query = query.filter(Inventory.current_stock <= Inventory.min_stock_level)
        
        if expired_only:
            query = query.filter(Inventory.expiry_date <= datetime.now().date())
        
        inventories = query.order_by(Inventory.ingredient_id).all()
        
        if not inventories:
            return jsonify({'success': False, 'message': '没有找到库存数据'}), 400
        
        # 导出数据
        from app.utils.export_service import export_inventory_excel
        return export_inventory_excel(inventories, user_area)
        
    except Exception as e:
        current_app.logger.error(f"导出库存失败: {str(e)}")
        return jsonify({'success': False, 'message': f'导出失败: {str(e)}'}), 500

@inventory_bp.route('/export/expiry_report')
@login_required
@school_required
def export_expiry_report(user_area):
    """导出过期预警报表"""
    try:
        # 获取即将过期的库存（7天内）
        warning_date = datetime.now().date() + timedelta(days=7)
        
        from app.models import Inventory, Warehouse
        inventories = Inventory.query.join(Warehouse).filter(
            Warehouse.area_id == user_area.id,
            Inventory.expiry_date <= warning_date,
            Inventory.current_stock > 0
        ).order_by(Inventory.expiry_date).all()
        
        # 准备导出数据
        data = []
        for inventory in inventories:
            days_to_expire = (inventory.expiry_date - datetime.now().date()).days
            status = '已过期' if days_to_expire < 0 else f'{days_to_expire}天后过期'
            
            data.append({
                'ingredient_name': inventory.ingredient.name,
                'batch_number': inventory.batch_number or '',
                'current_stock': f"{inventory.current_stock:.2f}",
                'unit': inventory.unit,
                'expiry_date': inventory.expiry_date.strftime('%Y-%m-%d'),
                'status': status,
                'storage_location': inventory.storage_location.name if inventory.storage_location else '',
                'supplier_name': inventory.supplier.name if inventory.supplier else ''
            })
        
        from app.utils.export_service import UnifiedExportService
        export_service = UnifiedExportService()
        
        # 使用自定义模板
        template = {
            'headers': ['食材名称', '批次号', '库存数量', '单位', '过期日期', '状态', '存储位置', '供应商'],
            'columns': ['ingredient_name', 'batch_number', 'current_stock', 'unit', 'expiry_date', 'status', 'storage_location', 'supplier_name'],
            'title': '库存过期预警报表'
        }
        
        return export_service.export_to_excel(data, 'expiry_report', user_area=user_area)
        
    except Exception as e:
        current_app.logger.error(f"导出过期预警报表失败: {str(e)}")
        return jsonify({'success': False, 'message': f'导出失败: {str(e)}'}), 500

"""
日常管理导出功能
在 app/routes/daily_management/routes.py 中添加以下代码
"""

@daily_management_bp.route('/export/<export_type>')
@login_required
@school_required
def export_daily_data(export_type, user_area):
    """导出日常管理数据"""
    try:
        # 获取日期范围
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        
        if start_date:
            start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
        else:
            start_date = datetime.now().date() - timedelta(days=30)
        
        if end_date:
            end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
        else:
            end_date = datetime.now().date()
        
        if export_type == 'inspection':
            return export_inspection_records(user_area, start_date, end_date)
        elif export_type == 'companion':
            return export_companion_records(user_area, start_date, end_date)
        elif export_type == 'training':
            return export_training_records(user_area, start_date, end_date)
        elif export_type == 'issues':
            return export_issue_records(user_area, start_date, end_date)
        else:
            return jsonify({'success': False, 'message': '不支持的导出类型'}), 400
            
    except Exception as e:
        current_app.logger.error(f"导出日常管理数据失败: {str(e)}")
        return jsonify({'success': False, 'message': f'导出失败: {str(e)}'}), 500

def export_inspection_records(user_area, start_date, end_date):
    """导出检查记录"""
    from app.models_daily_management import InspectionRecord
    
    records = InspectionRecord.query.filter(
        InspectionRecord.area_id == user_area.id,
        InspectionRecord.inspection_date >= start_date,
        InspectionRecord.inspection_date <= end_date
    ).order_by(InspectionRecord.inspection_date.desc()).all()
    
    from app.utils.export_service import export_inspection_records_excel
    return export_inspection_records_excel(records, user_area)

def export_companion_records(user_area, start_date, end_date):
    """导出陪餐记录"""
    from app.models_daily_management import DiningCompanion
    
    records = DiningCompanion.query.filter(
        DiningCompanion.area_id == user_area.id,
        DiningCompanion.dining_date >= start_date,
        DiningCompanion.dining_date <= end_date
    ).order_by(DiningCompanion.dining_date.desc()).all()
    
    from app.utils.export_service import export_companion_records_excel
    return export_companion_records_excel(records, user_area)

def export_training_records(user_area, start_date, end_date):
    """导出培训记录"""
    from app.models_daily_management import CanteenTrainingRecord
    
    records = CanteenTrainingRecord.query.filter(
        CanteenTrainingRecord.area_id == user_area.id,
        CanteenTrainingRecord.training_date >= start_date,
        CanteenTrainingRecord.training_date <= end_date
    ).order_by(CanteenTrainingRecord.training_date.desc()).all()
    
    # 准备数据
    data = []
    for record in records:
        data.append({
            'training_date': record.training_date.strftime('%Y-%m-%d'),
            'training_topic': record.training_topic,
            'training_type': record.training_type,
            'participants': record.participants,
            'duration': f"{record.duration}小时" if record.duration else '',
            'effectiveness': record.effectiveness or '',
            'notes': record.notes or ''
        })
    
    from app.utils.export_service import UnifiedExportService
    export_service = UnifiedExportService()
    return export_service.export_to_excel(data, 'training_records', user_area=user_area)

"""
员工管理导出功能
在 app/routes/employee.py 中添加以下代码
"""

@employee_bp.route('/export')
@login_required
@school_required
def export_employees(user_area):
    """导出员工信息"""
    try:
        from app.models import Employee
        
        employees = Employee.query.filter_by(area_id=user_area.id).order_by(Employee.created_at.desc()).all()
        
        # 准备数据
        data = []
        for employee in employees:
            data.append({
                'name': employee.name,
                'employee_id': employee.employee_id,
                'position': employee.position,
                'department': employee.department,
                'phone': employee.phone or '',
                'email': employee.email or '',
                'hire_date': employee.hire_date.strftime('%Y-%m-%d') if employee.hire_date else '',
                'status': '在职' if employee.is_active else '离职',
                'health_certificate_expiry': employee.health_certificate_expiry.strftime('%Y-%m-%d') if employee.health_certificate_expiry else ''
            })
        
        from app.utils.export_service import UnifiedExportService
        export_service = UnifiedExportService()
        
        # 使用自定义模板
        template = {
            'headers': ['姓名', '工号', '职位', '部门', '电话', '邮箱', '入职日期', '状态', '健康证到期日期'],
            'columns': ['name', 'employee_id', 'position', 'department', 'phone', 'email', 'hire_date', 'status', 'health_certificate_expiry'],
            'title': '员工信息列表'
        }
        
        return export_service.export_to_excel(data, 'employee_list', user_area=user_area)
        
    except Exception as e:
        current_app.logger.error(f"导出员工信息失败: {str(e)}")
        return jsonify({'success': False, 'message': f'导出失败: {str(e)}'}), 500

# 前端JavaScript代码示例
"""
// 在相应的模板文件中添加导出按钮和JavaScript代码

// 供应商导出
function exportSuppliers(format = 'excel') {
    const params = new URLSearchParams({
        format: format,
        status: document.getElementById('statusFilter')?.value || 'all'
    });
    
    window.location.href = `/supplier/export?${params.toString()}`;
}

// 库存导出
function exportInventory(format = 'excel') {
    const params = new URLSearchParams({
        format: format,
        warehouse_id: document.getElementById('warehouseFilter')?.value || '',
        low_stock_only: document.getElementById('lowStockOnly')?.checked || false,
        expired_only: document.getElementById('expiredOnly')?.checked || false
    });
    
    window.location.href = `/inventory/export?${params.toString()}`;
}

// 日常管理导出
function exportDailyData(exportType) {
    const startDate = document.getElementById('startDate')?.value || '';
    const endDate = document.getElementById('endDate')?.value || '';
    
    const params = new URLSearchParams({
        start_date: startDate,
        end_date: endDate
    });
    
    window.location.href = `/daily-management/export/${exportType}?${params.toString()}`;
}
"""
