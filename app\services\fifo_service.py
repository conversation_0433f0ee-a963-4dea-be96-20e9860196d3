"""
先进先出(FIFO)智能提醒服务
"""
from datetime import datetime, date, timedelta
from app import db
from app.models import Inventory, Ingredient, ConsumptionPlan, ConsumptionDetail
from sqlalchemy import and_, or_, func
import logging

logger = logging.getLogger(__name__)

class FIFOService:
    """先进先出智能提醒服务"""
    
    def __init__(self):
        self.warning_days = {
            'urgent': 3,    # 紧急：3天内过期
            'high': 7,      # 高优先级：7天内过期
            'medium': 15,   # 中优先级：15天内过期
            'low': 30       # 低优先级：30天内过期
        }
    
    def get_fifo_recommendations(self, area_id=None, ingredient_id=None):
        """获取先进先出推荐"""
        try:
            recommendations = []
            
            # 构建基础查询
            query = Inventory.query.filter(
                and_(
                    Inventory.quantity > 0,
                    Inventory.status.in_(['正常', '优先使用'])
                )
            )
            
            # 区域筛选
            if area_id:
                query = query.join(Inventory.warehouse_info).filter(
                    Inventory.warehouse_info.has(area_id=area_id)
                )
            
            # 食材筛选
            if ingredient_id:
                query = query.filter(Inventory.ingredient_id == ingredient_id)
            
            # 按食材分组获取库存
            inventories = query.order_by(
                Inventory.ingredient_id,
                Inventory.expiry_date,
                Inventory.production_date
            ).all()
            
            # 按食材分组处理
            ingredient_groups = {}
            for inventory in inventories:
                if inventory.ingredient_id not in ingredient_groups:
                    ingredient_groups[inventory.ingredient_id] = []
                ingredient_groups[inventory.ingredient_id].append(inventory)
            
            # 为每个食材生成FIFO推荐
            for ingredient_id, ingredient_inventories in ingredient_groups.items():
                recommendation = self._analyze_ingredient_fifo(ingredient_inventories)
                if recommendation:
                    recommendations.append(recommendation)
            
            # 按优先级排序
            recommendations.sort(key=lambda x: self._get_priority_score(x['priority']), reverse=True)
            
            return recommendations
            
        except Exception as e:
            logger.error(f"获取FIFO推荐失败: {str(e)}")
            return []
    
    def _analyze_ingredient_fifo(self, inventories):
        """分析单个食材的FIFO情况"""
        try:
            if not inventories:
                return None
            
            ingredient = inventories[0].ingredient
            today = date.today()
            
            # 计算总库存
            total_quantity = sum(inv.quantity for inv in inventories)
            
            # 找出最早过期的库存
            earliest_expiry = min(inv.expiry_date for inv in inventories)
            days_to_expire = (earliest_expiry - today).days
            
            # 确定优先级
            priority = self._determine_priority(days_to_expire)
            
            # 计算建议使用数量
            suggested_quantity = self._calculate_suggested_quantity(inventories, days_to_expire)
            
            # 生成推荐信息
            recommendation = {
                'ingredient_id': ingredient.id,
                'ingredient_name': ingredient.name,
                'total_quantity': total_quantity,
                'earliest_expiry': earliest_expiry,
                'days_to_expire': days_to_expire,
                'priority': priority,
                'suggested_quantity': suggested_quantity,
                'inventories': [self._format_inventory_info(inv) for inv in inventories[:3]],  # 只显示前3个
                'message': self._generate_message(ingredient.name, days_to_expire, suggested_quantity),
                'actions': self._generate_actions(inventories, days_to_expire)
            }
            
            return recommendation
            
        except Exception as e:
            logger.error(f"分析食材FIFO失败: {str(e)}")
            return None
    
    def _determine_priority(self, days_to_expire):
        """确定优先级"""
        if days_to_expire < 0:
            return 'expired'
        elif days_to_expire <= self.warning_days['urgent']:
            return 'urgent'
        elif days_to_expire <= self.warning_days['high']:
            return 'high'
        elif days_to_expire <= self.warning_days['medium']:
            return 'medium'
        elif days_to_expire <= self.warning_days['low']:
            return 'low'
        else:
            return 'normal'
    
    def _calculate_suggested_quantity(self, inventories, days_to_expire):
        """计算建议使用数量"""
        try:
            # 如果已过期或即将过期，建议使用全部
            if days_to_expire <= 3:
                return sum(inv.quantity for inv in inventories if (inv.expiry_date - date.today()).days <= 3)
            
            # 否则根据历史消耗量计算
            ingredient_id = inventories[0].ingredient_id
            daily_consumption = self._get_daily_consumption(ingredient_id)
            
            if daily_consumption > 0:
                # 建议使用未来3-7天的消耗量
                suggested = daily_consumption * min(days_to_expire, 7)
                total_available = sum(inv.quantity for inv in inventories)
                return min(suggested, total_available)
            
            # 如果没有历史数据，建议使用最早过期批次的一半
            earliest_batch = min(inventories, key=lambda x: x.expiry_date)
            return earliest_batch.quantity * 0.5
            
        except Exception as e:
            logger.error(f"计算建议使用数量失败: {str(e)}")
            return 0
    
    def _get_daily_consumption(self, ingredient_id, days=30):
        """获取日均消耗量"""
        try:
            # 获取最近30天的消耗记录
            start_date = date.today() - timedelta(days=days)
            
            total_consumption = db.session.query(
                func.sum(ConsumptionDetail.actual_quantity)
            ).join(ConsumptionPlan).filter(
                and_(
                    ConsumptionDetail.ingredient_id == ingredient_id,
                    ConsumptionPlan.plan_date >= start_date,
                    ConsumptionDetail.actual_quantity.isnot(None)
                )
            ).scalar()
            
            if total_consumption:
                return float(total_consumption) / days
            
            return 0
            
        except Exception as e:
            logger.error(f"获取日均消耗量失败: {str(e)}")
            return 0
    
    def _format_inventory_info(self, inventory):
        """格式化库存信息"""
        return {
            'id': inventory.id,
            'batch_number': inventory.batch_number,
            'quantity': inventory.quantity,
            'unit': inventory.unit,
            'production_date': inventory.production_date,
            'expiry_date': inventory.expiry_date,
            'days_to_expire': (inventory.expiry_date - date.today()).days,
            'storage_location': inventory.storage_location.name if inventory.storage_location else None,
            'supplier': inventory.supplier.name if inventory.supplier else None
        }
    
    def _generate_message(self, ingredient_name, days_to_expire, suggested_quantity):
        """生成提醒消息"""
        if days_to_expire < 0:
            return f"{ingredient_name} 已过期 {abs(days_to_expire)} 天，请立即处理"
        elif days_to_expire <= 3:
            return f"{ingredient_name} 将在 {days_to_expire} 天后过期，建议优先使用 {suggested_quantity:.1f} 单位"
        elif days_to_expire <= 7:
            return f"{ingredient_name} 将在 {days_to_expire} 天后过期，建议安排使用 {suggested_quantity:.1f} 单位"
        else:
            return f"{ingredient_name} 将在 {days_to_expire} 天后过期，可考虑优先安排使用"
    
    def _generate_actions(self, inventories, days_to_expire):
        """生成推荐操作"""
        actions = []
        
        if days_to_expire < 0:
            actions.extend(['立即报废', '申请退货', '转移处理'])
        elif days_to_expire <= 3:
            actions.extend(['优先使用', '加入消耗计划', '标记报废'])
        elif days_to_expire <= 7:
            actions.extend(['加入消耗计划', '优先使用', '调整采购'])
        else:
            actions.extend(['加入消耗计划', '调整采购计划'])
        
        return actions
    
    def _get_priority_score(self, priority):
        """获取优先级分数"""
        scores = {
            'expired': 100,
            'urgent': 90,
            'high': 70,
            'medium': 50,
            'low': 30,
            'normal': 10
        }
        return scores.get(priority, 0)
    
    def mark_inventory_priority(self, inventory_id, priority_level='优先使用'):
        """标记库存优先级"""
        try:
            inventory = Inventory.query.get(inventory_id)
            if not inventory:
                return {'success': False, 'message': '库存不存在'}
            
            old_status = inventory.status
            inventory.status = priority_level
            
            db.session.commit()
            
            logger.info(f"库存ID {inventory_id} 状态从 '{old_status}' 更新为 '{priority_level}'")
            
            return {
                'success': True,
                'message': f'已将 {inventory.ingredient.name} 标记为 {priority_level}'
            }
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"标记库存优先级失败: {str(e)}")
            return {'success': False, 'message': f'标记失败: {str(e)}'}
    
    def get_priority_inventory_summary(self, area_id=None):
        """获取优先使用库存汇总"""
        try:
            query = Inventory.query.filter(
                and_(
                    Inventory.status == '优先使用',
                    Inventory.quantity > 0
                )
            )
            
            if area_id:
                query = query.join(Inventory.warehouse_info).filter(
                    Inventory.warehouse_info.has(area_id=area_id)
                )
            
            priority_inventories = query.order_by(Inventory.expiry_date).all()
            
            summary = {
                'total_items': len(priority_inventories),
                'total_value': 0,
                'by_category': {},
                'urgent_items': []
            }
            
            today = date.today()
            
            for inventory in priority_inventories:
                # 计算价值（如果有成本信息）
                # 这里简化处理，实际应该从入库记录获取成本
                
                # 按分类统计
                category = inventory.ingredient.category.name if inventory.ingredient.category else '其他'
                if category not in summary['by_category']:
                    summary['by_category'][category] = {
                        'count': 0,
                        'quantity': 0
                    }
                summary['by_category'][category]['count'] += 1
                summary['by_category'][category]['quantity'] += inventory.quantity
                
                # 紧急项目（3天内过期）
                days_to_expire = (inventory.expiry_date - today).days
                if days_to_expire <= 3:
                    summary['urgent_items'].append({
                        'ingredient_name': inventory.ingredient.name,
                        'quantity': inventory.quantity,
                        'unit': inventory.unit,
                        'days_to_expire': days_to_expire,
                        'batch_number': inventory.batch_number
                    })
            
            return summary
            
        except Exception as e:
            logger.error(f"获取优先库存汇总失败: {str(e)}")
            return {
                'total_items': 0,
                'total_value': 0,
                'by_category': {},
                'urgent_items': []
            }

# 创建服务实例
fifo_service = FIFOService()
