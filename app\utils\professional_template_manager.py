"""
专业模板管理器
统一管理所有专业打印和导出模板
提供统一的接口和样式管理
"""

from flask import current_app
from .professional_financial_templates import ProfessionalFinancialTemplates
from .professional_inventory_templates import ProfessionalInventoryTemplates
from .professional_supplier_templates import ProfessionalSupplierTemplates
from .professional_daily_templates import ProfessionalDailyTemplates
from .professional_employee_templates import ProfessionalEmployeeTemplates
from datetime import datetime
import os

class ProfessionalTemplateManager:
    """专业模板管理器"""
    
    def __init__(self):
        self.financial_templates = ProfessionalFinancialTemplates()
        self.inventory_templates = ProfessionalInventoryTemplates()
        self.supplier_templates = ProfessionalSupplierTemplates()
        self.daily_templates = ProfessionalDailyTemplates()
        self.employee_templates = ProfessionalEmployeeTemplates()
        
        # 确保所有必要的目录存在
        self.ensure_directories()
    
    def ensure_directories(self):
        """确保所有导出目录存在"""
        directories = [
            'pdf/vouchers',
            'pdf/reports', 
            'pdf/stock_ins',
            'pdf/purchase_orders',
            'pdf/supplier_reports',
            'pdf/contracts',
            'pdf/daily_reports',
            'pdf/monthly_reports',
            'pdf/employee_profiles',
            'pdf/attendance_reports',
            'excel/exports',
            'export_templates'
        ]
        
        for directory in directories:
            full_path = os.path.join(current_app.root_path, 'static', directory)
            if not os.path.exists(full_path):
                os.makedirs(full_path)
                current_app.logger.info(f"创建目录: {full_path}")
    
    # 财务模块模板
    def generate_financial_voucher_pdf(self, voucher, details, user_area, landscape_mode=True):
        """生成财务凭证PDF"""
        return self.financial_templates.generate_voucher_pdf(voucher, details, user_area, landscape_mode)
    
    def generate_financial_voucher_excel(self, vouchers, user_area):
        """生成财务凭证Excel"""
        return self.financial_templates.generate_voucher_excel(vouchers, user_area)
    
    def generate_balance_sheet_pdf(self, balance_data, balance_date, user_area):
        """生成资产负债表PDF"""
        return self.financial_templates.generate_balance_sheet_pdf(balance_data, balance_date, user_area)
    
    # 库存管理模板
    def generate_stock_in_pdf(self, stock_in, stock_in_items, user_area):
        """生成入库单PDF"""
        return self.inventory_templates.generate_stock_in_pdf(stock_in, stock_in_items, user_area)
    
    def generate_inventory_list_excel(self, inventories, user_area):
        """生成库存清单Excel"""
        return self.inventory_templates.generate_inventory_list_excel(inventories, user_area)
    
    def generate_expiry_warning_excel(self, inventories, user_area, warning_days=7):
        """生成过期预警Excel"""
        return self.inventory_templates.generate_expiry_warning_excel(inventories, user_area, warning_days)
    
    def generate_purchase_order_pdf(self, order, order_items, user_area):
        """生成采购订单PDF"""
        return self.inventory_templates.generate_purchase_order_pdf(order, order_items, user_area)
    
    # 供应商管理模板
    def generate_supplier_list_excel(self, suppliers, user_area):
        """生成供应商清单Excel"""
        return self.supplier_templates.generate_supplier_list_excel(suppliers, user_area)
    
    def generate_supplier_statistics_excel(self, supplier_stats, user_area):
        """生成供应商统计Excel"""
        return self.supplier_templates.generate_supplier_statistics_excel(supplier_stats, user_area)
    
    def generate_supplier_evaluation_pdf(self, supplier, evaluation_data, user_area):
        """生成供应商评估报告PDF"""
        return self.supplier_templates.generate_supplier_evaluation_pdf(supplier, evaluation_data, user_area)
    
    def generate_supplier_contract_pdf(self, supplier, contract_data, user_area):
        """生成供应商合同PDF"""
        return self.supplier_templates.generate_supplier_contract_pdf(supplier, contract_data, user_area)
    
    # 日常管理模板
    def generate_inspection_records_excel(self, records, user_area, start_date, end_date):
        """生成检查记录Excel"""
        return self.daily_templates.generate_inspection_records_excel(records, user_area, start_date, end_date)
    
    def generate_companion_dining_excel(self, records, user_area, start_date, end_date):
        """生成陪餐记录Excel"""
        return self.daily_templates.generate_companion_dining_excel(records, user_area, start_date, end_date)
    
    def generate_training_records_excel(self, records, user_area, start_date, end_date):
        """生成培训记录Excel"""
        return self.daily_templates.generate_training_records_excel(records, user_area, start_date, end_date)
    
    def generate_daily_summary_pdf(self, daily_log, user_area):
        """生成日常管理汇总PDF"""
        return self.daily_templates.generate_daily_summary_pdf(daily_log, user_area)
    
    def generate_monthly_summary_pdf(self, monthly_data, user_area, year, month):
        """生成月度工作总结PDF"""
        return self.daily_templates.generate_monthly_summary_pdf(monthly_data, user_area, year, month)
    
    # 员工管理模板
    def generate_employee_list_excel(self, employees, user_area):
        """生成员工清单Excel"""
        return self.employee_templates.generate_employee_list_excel(employees, user_area)
    
    def generate_health_certificate_warning_excel(self, employees, user_area, warning_days=30):
        """生成健康证预警Excel"""
        return self.employee_templates.generate_health_certificate_warning_excel(employees, user_area, warning_days)
    
    def generate_employee_profile_pdf(self, employee, user_area):
        """生成员工档案PDF"""
        return self.employee_templates.generate_employee_profile_pdf(employee, user_area)
    
    def generate_attendance_summary_pdf(self, attendance_data, user_area, year, month):
        """生成考勤汇总PDF"""
        return self.employee_templates.generate_attendance_summary_pdf(attendance_data, user_area, year, month)
    
    # 通用工具方法
    def get_template_info(self):
        """获取所有可用模板信息"""
        return {
            'financial': {
                'voucher_pdf': '财务凭证PDF',
                'voucher_excel': '财务凭证Excel',
                'balance_sheet_pdf': '资产负债表PDF'
            },
            'inventory': {
                'stock_in_pdf': '入库单PDF',
                'inventory_list_excel': '库存清单Excel',
                'expiry_warning_excel': '过期预警Excel',
                'purchase_order_pdf': '采购订单PDF'
            },
            'supplier': {
                'supplier_list_excel': '供应商清单Excel',
                'supplier_statistics_excel': '供应商统计Excel',
                'supplier_evaluation_pdf': '供应商评估PDF',
                'supplier_contract_pdf': '供应商合同PDF'
            },
            'daily': {
                'inspection_records_excel': '检查记录Excel',
                'companion_dining_excel': '陪餐记录Excel',
                'training_records_excel': '培训记录Excel',
                'daily_summary_pdf': '日常汇总PDF',
                'monthly_summary_pdf': '月度总结PDF'
            },
            'employee': {
                'employee_list_excel': '员工清单Excel',
                'health_certificate_warning_excel': '健康证预警Excel',
                'employee_profile_pdf': '员工档案PDF',
                'attendance_summary_pdf': '考勤汇总PDF'
            }
        }
    
    def get_template_statistics(self):
        """获取模板使用统计"""
        stats = {
            'total_templates': 0,
            'pdf_templates': 0,
            'excel_templates': 0,
            'modules': 0
        }
        
        template_info = self.get_template_info()
        stats['modules'] = len(template_info)
        
        for module, templates in template_info.items():
            stats['total_templates'] += len(templates)
            for template_name in templates.keys():
                if 'pdf' in template_name:
                    stats['pdf_templates'] += 1
                elif 'excel' in template_name:
                    stats['excel_templates'] += 1
        
        return stats
    
    def validate_template_requirements(self):
        """验证模板系统要求"""
        requirements = {
            'fonts_available': False,
            'directories_created': False,
            'dependencies_installed': False
        }
        
        # 检查字体
        try:
            font_path = os.path.join(current_app.root_path, 'static', 'fonts', 'simsun.ttf')
            requirements['fonts_available'] = os.path.exists(font_path)
        except:
            requirements['fonts_available'] = False
        
        # 检查目录
        try:
            pdf_dir = os.path.join(current_app.root_path, 'static', 'pdf')
            excel_dir = os.path.join(current_app.root_path, 'static', 'excel')
            requirements['directories_created'] = os.path.exists(pdf_dir) and os.path.exists(excel_dir)
        except:
            requirements['directories_created'] = False
        
        # 检查依赖
        try:
            import reportlab
            import openpyxl
            requirements['dependencies_installed'] = True
        except ImportError:
            requirements['dependencies_installed'] = False
        
        return requirements
    
    def get_system_status(self):
        """获取模板系统状态"""
        requirements = self.validate_template_requirements()
        statistics = self.get_template_statistics()
        
        status = {
            'system_ready': all(requirements.values()),
            'requirements': requirements,
            'statistics': statistics,
            'last_check': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        
        return status

# 全局模板管理器实例
template_manager = None

def get_template_manager():
    """获取模板管理器实例"""
    global template_manager
    if template_manager is None:
        template_manager = ProfessionalTemplateManager()
    return template_manager
