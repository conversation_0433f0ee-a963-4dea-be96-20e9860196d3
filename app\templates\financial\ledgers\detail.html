{% extends "financial/base.html" %}

{% block page_title %}明细账管理{% endblock %}

{% block breadcrumb %}
<span class="uf-breadcrumb-item"><a href="{{ url_for('financial.reports_index') }}">财务报表</a></span>
<span class="uf-breadcrumb-item active">明细账管理</span>
{% endblock %}

{% block page_actions %}
<div class="uf-toolbar-group">
    <button type="button" class="uf-btn uf-btn-primary" onclick="refreshLedger()">
        <i class="fas fa-sync-alt uf-icon"></i> 刷新
    </button>
    <button type="button" class="uf-btn uf-btn-success" onclick="batchGenerateLedgers()">
        <i class="fas fa-cogs uf-icon"></i> 批量生成
    </button>
    <div class="uf-toolbar-separator"></div>
    <button type="button" class="uf-btn uf-btn-info" onclick="exportDetailLedger()" {% if not selected_subject %}disabled{% endif %}>
        <i class="fas fa-file-excel uf-icon"></i> 导出
    </button>
    <button type="button" class="uf-btn uf-btn-secondary" onclick="printDetailLedger()" {% if not selected_subject %}disabled{% endif %}>
        <i class="fas fa-print uf-icon"></i> 打印
    </button>
</div>
{% endblock %}

{% block financial_content %}
<!-- 用友风格查询条件 - 紧凑布局 -->
<div class="uf-card uf-query-card">
    <div class="uf-card-body" style="padding: 12px 16px;">
        <form method="GET" class="uf-query-form">
            <div class="uf-query-row">
                <!-- 查询条件组 -->
                <div class="uf-query-conditions">
                    <div class="uf-form-group">
                        <label class="uf-form-label">会计科目：</label>
                        <select class="uf-form-control uf-subject-select" id="subject_id" name="subject_id" required>
                            <option value="">请选择科目</option>
                            {% for subject in subjects %}
                            <option value="{{ subject.id }}" {% if subject.id == subject_id %}selected{% endif %}>
                                {{ subject.code }} - {{ subject.name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="uf-form-group">
                        <label class="uf-form-label">年份：</label>
                        <select class="uf-form-control" id="year" name="year" required>
                            {% for y in range(2020, 2030) %}
                            <option value="{{ y }}" {% if y == year %}selected{% endif %}>{{ y }}年</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="uf-form-group">
                        <label class="uf-form-label">月份：</label>
                        <select class="uf-form-control" id="month" name="month" required>
                            {% for m in range(1, 13) %}
                            <option value="{{ m }}" {% if m == month %}selected{% endif %}>{{ m }}月</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>

                <!-- 操作按钮组 -->
                <div class="uf-query-actions">
                    <button type="submit" class="uf-btn uf-btn-primary">
                        <i class="fas fa-search uf-icon"></i> 查询
                    </button>
                    <button type="button" class="uf-btn uf-btn-success" onclick="generateLedger()">
                        <i class="fas fa-cogs uf-icon"></i> 生成
                    </button>
                    <button type="button" class="uf-btn uf-btn-warning" onclick="regenerateLedger()" {% if not selected_subject %}disabled{% endif %}>
                        <i class="fas fa-redo uf-icon"></i> 重生成
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- 已生成明细账列表 -->
<div class="uf-card uf-generated-ledgers-card">
    <div class="uf-card-header">
        <div class="uf-card-header-title">
            <i class="fas fa-history uf-card-header-icon"></i>
            已生成明细账
        </div>
        <div class="uf-card-header-actions">
            <span class="uf-record-count">
                {% if generated_ledgers %}
                    共 {{ generated_ledgers|length }} 条记录
                {% else %}
                    暂无记录
                {% endif %}
            </span>
            <div class="uf-header-tools">
                <button type="button" class="uf-btn uf-btn-sm uf-btn-outline" onclick="refreshLedgerList()" title="刷新">
                    <i class="fas fa-sync-alt uf-icon"></i>
                </button>
                <button type="button" class="uf-btn uf-btn-sm uf-btn-outline" onclick="toggleLedgerList()" title="展开/收起">
                    <i class="fas fa-chevron-up uf-icon" id="toggleIcon"></i>
                </button>
            </div>
        </div>
    </div>
    <div class="uf-card-body" style="padding: 0;" id="ledgerListBody">
        {% if generated_ledgers %}
        <div class="uf-table-container" style="max-height: 300px;">
            <table class="uf-table uf-generated-ledgers-table">
                <thead>
                    <tr>
                        <th class="uf-col-period">期间</th>
                        <th class="uf-col-subject">会计科目</th>
                        <th class="uf-col-type">科目类型</th>
                        <th class="uf-col-opening">期初余额</th>
                        <th class="uf-col-debit">借方发生额</th>
                        <th class="uf-col-credit">贷方发生额</th>
                        <th class="uf-col-ending">期末余额</th>
                        <th class="uf-col-records">记录数</th>
                        <th class="uf-col-generated">生成时间</th>
                        <th class="uf-col-operations">操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for ledger in generated_ledgers %}
                    <tr class="uf-ledger-row" data-ledger-id="{{ ledger.id }}">
                        <td class="uf-period-cell">
                            <span class="uf-period-text">{{ ledger.year }}年{{ ledger.month }}月</span>
                        </td>
                        <td class="uf-subject-cell">
                            <div class="uf-subject-info">
                                <span class="uf-subject-code">{{ ledger.subject_code }}</span>
                                <span class="uf-subject-name">{{ ledger.subject_name }}</span>
                            </div>
                        </td>
                        <td class="uf-type-cell">
                            <span class="uf-status uf-status-{{ ledger.subject_type|lower }}">{{ ledger.subject_type }}</span>
                        </td>
                        <td class="uf-amount-cell">
                            {% if ledger.opening_balance != 0 %}
                                <span class="uf-amount {% if ledger.opening_balance > 0 %}uf-positive{% else %}uf-negative{% endif %}">
                                    {{ "{:,.2f}".format(ledger.opening_balance or 0) }}
                                </span>
                            {% else %}
                                <span class="uf-amount-zero">-</span>
                            {% endif %}
                        </td>
                        <td class="uf-amount-cell">
                            {% if ledger.period_debit > 0 %}
                                <span class="uf-amount uf-debit-amount">{{ "{:,.2f}".format(ledger.period_debit or 0) }}</span>
                            {% else %}
                                <span class="uf-amount-zero">-</span>
                            {% endif %}
                        </td>
                        <td class="uf-amount-cell">
                            {% if ledger.period_credit > 0 %}
                                <span class="uf-amount uf-credit-amount">{{ "{:,.2f}".format(ledger.period_credit or 0) }}</span>
                            {% else %}
                                <span class="uf-amount-zero">-</span>
                            {% endif %}
                        </td>
                        <td class="uf-amount-cell uf-ending-cell">
                            {% if ledger.closing_balance != 0 %}
                                <span class="uf-amount uf-ending-amount {% if ledger.closing_balance > 0 %}uf-positive{% else %}uf-negative{% endif %}">
                                    {{ "{:,.2f}".format(ledger.closing_balance or 0) }}
                                </span>
                            {% else %}
                                <span class="uf-amount-zero">-</span>
                            {% endif %}
                        </td>
                        <td class="uf-records-cell">
                            <span class="uf-record-count-text">{{ ledger.record_count }}</span>
                        </td>
                        <td class="uf-generated-cell">
                            <div class="uf-generated-info">
                                <span class="uf-generated-time">{{ ledger.generated_at }}</span>
                                <small class="uf-generated-by">{{ ledger.generator_name }}</small>
                            </div>
                        </td>
                        <td class="uf-operations-cell">
                            <div class="uf-btn-group-sm">
                                <a href="?subject_id={{ ledger.subject_id }}&year={{ ledger.year }}&month={{ ledger.month }}"
                                   class="uf-btn uf-btn-xs uf-btn-info" title="查看明细">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <button type="button" class="uf-btn uf-btn-xs uf-btn-warning"
                                        onclick="regenerateLedger({{ ledger.subject_id }}, {{ ledger.year }}, {{ ledger.month }})" title="重新生成">
                                    <i class="fas fa-redo"></i>
                                </button>
                                <button type="button" class="uf-btn uf-btn-xs uf-btn-success"
                                        onclick="exportLedger({{ ledger.subject_id }}, {{ ledger.year }}, {{ ledger.month }})" title="导出">
                                    <i class="fas fa-download"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="uf-empty-content" style="padding: 40px; text-align: center;">
            <i class="fas fa-inbox uf-empty-icon" style="font-size: 48px; color: #ccc; margin-bottom: 16px;"></i>
            <p class="uf-empty-text" style="color: #666; margin: 0;">暂无已生成的明细账</p>
            <p class="uf-empty-hint" style="color: #999; font-size: 12px; margin: 8px 0 0 0;">请先选择科目并生成明细账</p>
        </div>
        {% endif %}
    </div>
</div>

{% if generation_status %}
<!-- 用友风格生成状态提示 -->
<div style="margin-bottom: 10px;">
    {% if generation_status.success %}
    <div style="background: #d4edda; border: 1px solid #c3e6cb; border-radius: 2px; padding: 12px; color: #155724;">
        <i class="fas fa-check-circle uf-icon"></i> {{ generation_status.message }}
        <br><small style="font-size: 11px;">记录数：{{ generation_status.records_count }}，期初余额：¥{{ "%.2f".format(generation_status.opening_balance or 0) }}，期末余额：¥{{ "%.2f".format(generation_status.closing_balance or 0) }}</small>
    </div>
    {% else %}
    <div style="background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 2px; padding: 12px; color: #721c24;">
        <i class="fas fa-exclamation-circle uf-icon"></i> {{ generation_status.message }}
    </div>
    {% endif %}
</div>
{% endif %}

{% if selected_subject and ledger_data %}
<!-- 用友风格科目信息 -->
<div class="uf-card" style="margin-bottom: 10px;">
    <div class="uf-card-header">
        <i class="fas fa-list-alt uf-icon"></i> {{ ledger_data.subject.code }} - {{ ledger_data.subject.name }}
    </div>
    <div class="uf-card-body">
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px; font-size: 12px;">
            <div>
                <strong>科目类型：</strong>{{ ledger_data.subject.subject_type }}
            </div>
            <div>
                <strong>余额方向：</strong>{{ ledger_data.subject.balance_direction }}
            </div>
            <div>
                <strong>期初余额：</strong><span class="uf-amount">¥{{ "%.2f".format(ledger_data.opening_balance or 0) }}</span>
            </div>
            <div>
                <strong>期末余额：</strong><span class="uf-amount">¥{{ "%.2f".format(ledger_data.closing_balance or 0) }}</span>
            </div>
            <div>
                <strong>本期借方：</strong><span class="uf-amount">¥{{ "%.2f".format(ledger_data.total_debit or 0) }}</span>
            </div>
            <div>
                <strong>本期贷方：</strong><span class="uf-amount">¥{{ "%.2f".format(ledger_data.total_credit or 0) }}</span>
            </div>
            <div>
                <strong>发生笔数：</strong>{{ ledger_data.transaction_count }}
            </div>
            <div>
                <strong>账页期间：</strong>{{ year }}年{{ month }}月
            </div>
        </div>
    </div>
</div>

<!-- 用友财务软件专业明细账表格 -->
<div class="uf-card uf-ledger-card">
    <div class="uf-card-header">
        <div class="uf-card-header-title">
            <i class="fas fa-list-alt uf-card-header-icon"></i>
            明细账记录
        </div>
        <div class="uf-card-header-actions">
            {% if ledger_data %}
            <div class="uf-table-search">
                <input type="text" class="uf-search-input" id="tableSearchInput"
                       placeholder="搜索摘要、凭证号..." onkeyup="searchTable()">
                <i class="fas fa-search uf-search-icon"></i>
            </div>
            <span class="uf-record-count">共 <span id="recordCount">{{ ledger_data.records|length }}</span> 条记录</span>
            <div class="uf-header-tools">
                <button type="button" class="uf-btn uf-btn-sm uf-btn-outline" onclick="clearSearch()" title="清除搜索">
                    <i class="fas fa-times uf-icon"></i>
                </button>
                <button type="button" class="uf-btn uf-btn-sm uf-btn-outline" onclick="toggleTableView()" title="全屏">
                    <i class="fas fa-expand-arrows-alt uf-icon"></i>
                </button>
                <button type="button" class="uf-btn uf-btn-sm uf-btn-outline" onclick="refreshTable()" title="刷新">
                    <i class="fas fa-sync-alt uf-icon"></i>
                </button>
            </div>
            {% endif %}
        </div>
    </div>
    <div class="uf-card-body" style="padding: 0;">
        <div class="uf-table-container">
            <table class="uf-table uf-ledger-table" id="detailLedgerTable">
                <thead>
                    <tr>
                        <th class="uf-col-line-no">行号</th>
                        <th class="uf-col-date sortable" onclick="sortTable(1)">
                            日期 <i class="fas fa-sort uf-sort-icon"></i>
                        </th>
                        <th class="uf-col-voucher sortable" onclick="sortTable(2)">
                            凭证号 <i class="fas fa-sort uf-sort-icon"></i>
                        </th>
                        <th class="uf-col-summary">摘要</th>
                        <th class="uf-col-debit sortable" onclick="sortTable(4)">
                            借方金额 <i class="fas fa-sort uf-sort-icon"></i>
                        </th>
                        <th class="uf-col-credit sortable" onclick="sortTable(5)">
                            贷方金额 <i class="fas fa-sort uf-sort-icon"></i>
                        </th>
                        <th class="uf-col-balance sortable" onclick="sortTable(6)">
                            余额 <i class="fas fa-sort uf-sort-icon"></i>
                        </th>
                        <th class="uf-col-operations">操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for record in ledger_data.records %}
                    <tr class="uf-ledger-row {% if record.get('is_opening') %}uf-opening-row{% elif record.get('is_closing') %}uf-closing-row{% endif %}"
                        data-line="{{ record.line_number }}" data-voucher="{{ record.voucher_number or '' }}">
                        <td class="uf-line-number">{{ record.line_number }}</td>
                        <td class="uf-date-cell">
                            {% if record.voucher_date %}
                                <span class="uf-date">{{ record.voucher_date.strftime('%Y-%m-%d') if record.voucher_date.strftime else record.voucher_date }}</span>
                            {% else %}
                                <span class="uf-empty">-</span>
                            {% endif %}
                        </td>
                        <td class="uf-voucher-cell">
                            {% if record.voucher_number %}
                                <span class="uf-voucher-number">{{ record.voucher_number }}</span>
                            {% else %}
                                <span class="uf-empty">-</span>
                            {% endif %}
                        </td>
                        <td class="uf-summary-cell">
                            <div class="uf-summary-content">
                                {% if record.get('is_opening') %}
                                    <strong class="uf-opening-text">{{ record.summary }}</strong>
                                {% elif record.get('is_closing') %}
                                    <strong class="uf-closing-text">{{ record.summary }}</strong>
                                {% else %}
                                    <span class="uf-normal-text">{{ record.summary }}</span>
                                {% endif %}
                            </div>
                        </td>
                        <td class="uf-amount-cell uf-debit-cell">
                            {% if record.debit_amount > 0 %}
                                <span class="uf-amount">{{ "{:,.2f}".format(record.debit_amount or 0) }}</span>
                            {% else %}
                                <span class="uf-amount-zero">-</span>
                            {% endif %}
                        </td>
                        <td class="uf-amount-cell uf-credit-cell">
                            {% if record.credit_amount > 0 %}
                                <span class="uf-amount">{{ "{:,.2f}".format(record.credit_amount or 0) }}</span>
                            {% else %}
                                <span class="uf-amount-zero">-</span>
                            {% endif %}
                        </td>
                        <td class="uf-amount-cell uf-balance-cell">
                            <span class="uf-balance {% if record.balance and record.balance > 0 %}uf-positive{% elif record.balance and record.balance < 0 %}uf-negative{% else %}uf-zero{% endif %}">
                                {{ "{:,.2f}".format(record.balance or 0) }}
                            </span>
                        </td>
                        <td class="uf-operations-cell">
                            {% if record.get('voucher_id') %}
                                <div class="uf-btn-group-sm">
                                    <a href="{{ url_for('financial.view_voucher', id=record.voucher_id) }}"
                                       class="uf-btn uf-btn-xs uf-btn-info" title="查看凭证">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <button type="button" class="uf-btn uf-btn-xs uf-btn-secondary"
                                            onclick="showVoucherPreview({{ record.voucher_id }})" title="预览">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                            {% else %}
                                <span class="uf-empty">-</span>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
        </table>
    </div>
</div>

{% else %}
<div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 2px; padding: 16px; text-align: center; color: #856404;">
    <i class="fas fa-info-circle uf-icon"></i> 请选择会计科目和年月，然后点击"查看明细账"或"生成明细账"
</div>
{% endif %}

{% if not selected_subject %}
<!-- 用友风格使用说明 -->
<div class="uf-card" style="margin-top: 10px;">
    <div class="uf-card-header">
        <i class="fas fa-info-circle uf-icon"></i> 明细账功能说明
    </div>
    <div class="uf-card-body">
        <div style="font-size: 12px; line-height: 1.6;">
            <h6 style="color: var(--uf-primary); margin-bottom: 8px; font-size: 13px;">什么是明细账？</h6>
            <p style="margin-bottom: 12px;">明细账是按照会计科目设置的，用来分类登记某一类经济业务，提供有关明细核算资料的账簿。</p>

            <h6 style="color: var(--uf-primary); margin-bottom: 8px; font-size: 13px;">明细账的特点：</h6>
            <ul style="margin-bottom: 12px; padding-left: 20px;">
                <li style="margin-bottom: 4px;"><strong>按月生成</strong>：每个科目按月份生成独立的明细账页</li>
                <li style="margin-bottom: 4px;"><strong>连续记录</strong>：从期初余额开始，按时间顺序记录每笔业务</li>
                <li style="margin-bottom: 4px;"><strong>余额结转</strong>：每笔业务后自动计算并更新余额</li>
                <li style="margin-bottom: 4px;"><strong>标准格式</strong>：符合会计账簿的标准格式要求</li>
            </ul>

            <h6 style="color: var(--uf-primary); margin-bottom: 8px; font-size: 13px;">使用步骤：</h6>
            <ol style="margin: 0; padding-left: 20px;">
                <li style="margin-bottom: 4px;">选择要查看的会计科目</li>
                <li style="margin-bottom: 4px;">选择年份和月份</li>
                <li style="margin-bottom: 4px;">点击"生成明细账"按钮（首次使用）</li>
                <li style="margin-bottom: 4px;">点击"查看明细账"查看已生成的明细账</li>
                <li style="margin-bottom: 0;">可以批量生成所有有发生额科目的明细账</li>
            </ol>
        </div>
    </div>
</div>
{% endif %}


{% endblock %}

{% block financial_js %}
<script>
// 用友风格明细账页面JavaScript
document.addEventListener('DOMContentLoaded', function() {
    initDetailLedgerPage();
});

function initDetailLedgerPage() {
    // 初始化表格功能
    initTableFeatures();

    // 初始化快捷键
    initKeyboardShortcuts();

    // 初始化工具提示
    initTooltips();

    // 初始化科目选择器
    initSubjectSelector();
}

function initTableFeatures() {
    const table = document.getElementById('detailLedgerTable');
    if (table) {
        // 添加表格行悬停效果
        const rows = table.querySelectorAll('tbody tr');
        rows.forEach(row => {
            row.addEventListener('mouseenter', function() {
                this.classList.add('uf-row-hover');
            });
            row.addEventListener('mouseleave', function() {
                this.classList.remove('uf-row-hover');
            });
        });
    }
}

function initKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // F5: 刷新
        if (e.key === 'F5') {
            e.preventDefault();
            refreshLedger();
        }

        // Ctrl+E: 导出
        if (e.ctrlKey && e.key === 'e') {
            e.preventDefault();
            exportDetailLedger();
        }

        // Ctrl+P: 打印
        if (e.ctrlKey && e.key === 'p') {
            e.preventDefault();
            printDetailLedger();
        }

        // Ctrl+G: 生成
        if (e.ctrlKey && e.key === 'g') {
            e.preventDefault();
            generateLedger();
        }
    });
}

function initTooltips() {
    // 为金额单元格添加工具提示
    const amountCells = document.querySelectorAll('.uf-amount');
    amountCells.forEach(cell => {
        const value = cell.textContent.replace(/[,]/g, '');
        if (value && value !== '-') {
            cell.title = `金额：${value}`;
        }
    });
}

function initSubjectSelector() {
    const subjectSelect = document.getElementById('subject_id');
    if (subjectSelect) {
        // 添加搜索功能
        subjectSelect.addEventListener('keyup', function(e) {
            if (e.key === 'Enter') {
                document.querySelector('.uf-query-form').submit();
            }
        });
    }
}

// 表格排序功能
let sortDirection = {};

function sortTable(columnIndex) {
    const table = document.getElementById('detailLedgerTable');
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr:not(.uf-opening-row):not(.uf-closing-row)'));

    if (rows.length === 0) return;

    // 获取当前排序方向
    const currentDirection = sortDirection[columnIndex] || 'asc';
    const newDirection = currentDirection === 'asc' ? 'desc' : 'asc';
    sortDirection[columnIndex] = newDirection;

    // 更新排序图标
    updateSortIcons(columnIndex, newDirection);

    // 排序行
    rows.sort((a, b) => {
        const aValue = a.cells[columnIndex].textContent.trim();
        const bValue = b.cells[columnIndex].textContent.trim();

        // 数字列排序
        if (columnIndex >= 4 && columnIndex <= 6) {
            const aNum = parseFloat(aValue.replace(/[,\-]/g, '')) || 0;
            const bNum = parseFloat(bValue.replace(/[,\-]/g, '')) || 0;
            return newDirection === 'asc' ? aNum - bNum : bNum - aNum;
        }

        // 日期列排序
        if (columnIndex === 1) {
            const aDate = new Date(aValue);
            const bDate = new Date(bValue);
            return newDirection === 'asc' ? aDate - bDate : bDate - aDate;
        }

        // 文本列排序
        return newDirection === 'asc' ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);
    });

    // 重新插入排序后的行
    rows.forEach(row => tbody.appendChild(row));
}

function updateSortIcons(activeColumn, direction) {
    // 清除所有排序图标
    document.querySelectorAll('.uf-sort-icon').forEach(icon => {
        icon.className = 'fas fa-sort uf-sort-icon';
    });

    // 设置当前列的排序图标
    const activeIcon = document.querySelector(`th:nth-child(${activeColumn + 1}) .uf-sort-icon`);
    if (activeIcon) {
        activeIcon.className = `fas fa-sort-${direction === 'asc' ? 'up' : 'down'} uf-sort-icon uf-sort-active`;
    }
}

// 刷新明细账
function refreshLedger() {
    window.location.reload();
}

// 切换表格视图
function toggleTableView() {
    const table = document.getElementById('detailLedgerTable');
    const container = table.closest('.uf-table-container');

    if (container.classList.contains('uf-fullscreen')) {
        container.classList.remove('uf-fullscreen');
    } else {
        container.classList.add('uf-fullscreen');
    }
}

// 刷新表格
function refreshTable() {
    const subjectId = document.getElementById('subject_id').value;
    const year = document.getElementById('year').value;
    const month = document.getElementById('month').value;

    if (subjectId && year && month) {
        window.location.href = `?subject_id=${subjectId}&year=${year}&month=${month}`;
    }
}

// 显示凭证预览
function showVoucherPreview(voucherId) {
    // 创建预览窗口
    const previewUrl = `/financial/vouchers/${voucherId}/preview`;
    const previewWindow = window.open(previewUrl, 'voucherPreview',
        'width=800,height=600,scrollbars=yes,resizable=yes');

    if (previewWindow) {
        previewWindow.focus();
    }
}

// 刷新明细账列表
function refreshLedgerList() {
    window.location.reload();
}

// 切换明细账列表显示
function toggleLedgerList() {
    const body = document.getElementById('ledgerListBody');
    const icon = document.getElementById('toggleIcon');

    if (body.style.display === 'none') {
        body.style.display = 'block';
        icon.className = 'fas fa-chevron-up uf-icon';
    } else {
        body.style.display = 'none';
        icon.className = 'fas fa-chevron-down uf-icon';
    }
}

// 重新生成明细账
function regenerateLedger(subjectId, year, month) {
    if (!confirm(`确定要重新生成 ${year}年${month}月 的明细账吗？这将覆盖现有记录。`)) {
        return;
    }

    // 显示加载状态
    const btn = event.target.closest('button');
    const originalHtml = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
    btn.disabled = true;

    fetch('{{ url_for("financial.generate_detail_ledger_api") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            subject_id: subjectId,
            year: year,
            month: month
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            window.location.reload();
        } else {
            alert('重新生成失败：' + data.message);
        }
    })
    .catch(error => {
        console.error('重新生成明细账错误:', error);
        alert('重新生成失败：' + error.message);
    })
    .finally(() => {
        btn.innerHTML = originalHtml;
        btn.disabled = false;
    });
}

// 导出明细账
function exportLedger(subjectId, year, month) {
    const url = `/financial/ledgers/detail/export?subject_id=${subjectId}&year=${year}&month=${month}`;
    window.open(url, '_blank');
}

// 生成单个科目明细账
function generateLedger() {
    const subjectId = document.getElementById('subject_id').value;
    const year = document.getElementById('year').value;
    const month = document.getElementById('month').value;

    if (!subjectId) {
        showNotification('请先选择科目', 'warning');
        return;
    }

    if (!year || !month) {
        showNotification('请选择年份和月份', 'warning');
        return;
    }

    // 显示加载状态
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 生成中...';
    btn.disabled = true;

    // 添加页面加载遮罩
    showPageLoading('正在生成明细账，请稍候...');

    fetch('{{ url_for("financial.generate_detail_ledger_api") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            subject_id: parseInt(subjectId),
            year: parseInt(year),
            month: parseInt(month)
        })
    })
    .then(response => {
        // 检查响应状态
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        // 检查响应内容类型
        const contentType = response.headers.get('content-type');
        if (!contentType || !contentType.includes('application/json')) {
            throw new Error('服务器返回的不是JSON格式的数据');
        }

        return response.json();
    })
    .then(data => {
        if (data.success) {
            showNotification(data.message, 'success');
            // 延迟跳转，让用户看到成功消息
            setTimeout(() => {
                window.location.href = `?subject_id=${subjectId}&year=${year}&month=${month}`;
            }, 1000);
        } else {
            showNotification('生成失败：' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('生成明细账错误:', error);
        showNotification('生成失败：' + error.message, 'error');
    })
    .finally(() => {
        btn.innerHTML = originalText;
        btn.disabled = false;
        hidePageLoading();
    });
}

// 通知系统
function showNotification(message, type = 'info', duration = 3000) {
    // 移除现有通知
    const existingNotification = document.querySelector('.uf-notification');
    if (existingNotification) {
        existingNotification.remove();
    }

    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = `uf-notification uf-notification-${type}`;
    notification.innerHTML = `
        <div class="uf-notification-content">
            <i class="fas ${getNotificationIcon(type)} uf-notification-icon"></i>
            <span class="uf-notification-message">${message}</span>
            <button type="button" class="uf-notification-close" onclick="this.parentElement.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;

    // 添加到页面
    document.body.appendChild(notification);

    // 自动隐藏
    if (duration > 0) {
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, duration);
    }
}

function getNotificationIcon(type) {
    const icons = {
        'success': 'fa-check-circle',
        'warning': 'fa-exclamation-triangle',
        'error': 'fa-times-circle',
        'info': 'fa-info-circle'
    };
    return icons[type] || icons['info'];
}

// 页面加载遮罩
function showPageLoading(message = '加载中...') {
    // 移除现有遮罩
    hidePageLoading();

    const overlay = document.createElement('div');
    overlay.className = 'uf-page-loading-overlay';
    overlay.innerHTML = `
        <div class="uf-page-loading-content">
            <div class="uf-page-loading-spinner">
                <i class="fas fa-spinner fa-spin"></i>
            </div>
            <div class="uf-page-loading-message">${message}</div>
        </div>
    `;

    document.body.appendChild(overlay);
}

function hidePageLoading() {
    const overlay = document.querySelector('.uf-page-loading-overlay');
    if (overlay) {
        overlay.remove();
    }
}

// 表格搜索功能
function searchTable() {
    const searchInput = document.getElementById('tableSearchInput');
    const searchTerm = searchInput.value.toLowerCase().trim();
    const table = document.getElementById('detailLedgerTable');
    const tbody = table.querySelector('tbody');
    const rows = tbody.querySelectorAll('tr:not(.uf-opening-row):not(.uf-closing-row)');

    let visibleCount = 0;

    rows.forEach(row => {
        const voucherCell = row.cells[2].textContent.toLowerCase();
        const summaryCell = row.cells[3].textContent.toLowerCase();

        if (searchTerm === '' ||
            voucherCell.includes(searchTerm) ||
            summaryCell.includes(searchTerm)) {
            row.style.display = '';
            visibleCount++;
        } else {
            row.style.display = 'none';
        }
    });

    // 更新记录数显示
    const recordCountElement = document.getElementById('recordCount');
    if (recordCountElement) {
        recordCountElement.textContent = searchTerm === '' ? rows.length : visibleCount;
    }

    // 高亮搜索结果
    highlightSearchResults(searchTerm);
}

function highlightSearchResults(searchTerm) {
    const table = document.getElementById('detailLedgerTable');
    const cells = table.querySelectorAll('td');

    cells.forEach(cell => {
        // 移除之前的高亮
        cell.innerHTML = cell.innerHTML.replace(/<mark class="uf-search-highlight">(.*?)<\/mark>/gi, '$1');

        if (searchTerm && searchTerm.length > 0) {
            const regex = new RegExp(`(${searchTerm})`, 'gi');
            cell.innerHTML = cell.innerHTML.replace(regex, '<mark class="uf-search-highlight">$1</mark>');
        }
    });
}

function clearSearch() {
    const searchInput = document.getElementById('tableSearchInput');
    searchInput.value = '';
    searchTable();
    searchInput.focus();
}

// 批量生成明细账
function batchGenerateLedgers() {
    const year = document.getElementById('year').value;
    const month = document.getElementById('month').value;

    if (!year || !month) {
        showNotification('请选择年份和月份', 'warning');
        return;
    }

    if (!confirm(`确定要批量生成 ${year}年${month}月 所有有发生额科目的明细账吗？`)) {
        return;
    }

    // 显示加载状态
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 批量生成中...';
    btn.disabled = true;

    // 添加页面加载遮罩
    showPageLoading('正在批量生成明细账，请稍候...');

    fetch('{{ url_for("financial.batch_generate_detail_ledgers") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            year: parseInt(year),
            month: parseInt(month),
            subject_ids: []  // 空数组表示所有有发生额的科目
        })
    })
    .then(response => {
        // 检查响应状态
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        // 检查响应内容类型
        const contentType = response.headers.get('content-type');
        if (!contentType || !contentType.includes('application/json')) {
            throw new Error('服务器返回的不是JSON格式的数据');
        }

        return response.json();
    })
    .then(data => {
        if (data.success) {
            showNotification(data.message, 'success');
            // 延迟刷新页面
            setTimeout(() => {
                window.location.reload();
            }, 1500);
        } else {
            showNotification('批量生成失败：' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('批量生成明细账错误:', error);
        showNotification('批量生成失败：' + error.message, 'error');
    })
    .finally(() => {
        btn.innerHTML = originalText;
        btn.disabled = false;
        hidePageLoading();
    });
}

// 重新生成明细账
function regenerateLedger() {
    if (!confirm('确定要重新生成明细账吗？这将覆盖现有的明细账数据。')) {
        return;
    }
    generateLedger();
}

// 导出明细账
function exportDetailLedger() {
    const subjectId = document.getElementById('subject_id').value;
    const year = document.getElementById('year').value;
    const month = document.getElementById('month').value;

    if (!subjectId || !year || !month) {
        alert('请先选择科目和年月');
        return;
    }

    const startDate = `${year}-${month.padStart(2, '0')}-01`;
    const url = `{{ url_for('financial.export_report', report_type='detail_ledger') }}?subject_id=${subjectId}&start_date=${startDate}&end_date=${startDate}`;
    window.open(url, '_blank');
}

// 打印明细账
function printDetailLedger() {
    window.print();
}

// 科目选择变化时的处理
document.getElementById('subject_id').addEventListener('change', function() {
    // 不自动提交，让用户手动选择操作
});

// 添加明细账表格样式
document.addEventListener('DOMContentLoaded', function() {
    // 为明细账表格添加特殊样式
    const ledgerTable = document.querySelector('.uf-ledger-table');
    if (ledgerTable) {
        ledgerTable.style.fontSize = '11px';
        ledgerTable.style.fontFamily = 'Microsoft YaHei, SimSun, sans-serif';
    }
});
</script>
{% endblock %}

{% block financial_css %}
<style>
/* 用友财务软件专业明细账样式 - 13px字体标准 */

/* 查询卡片样式 */
.uf-query-card {
    margin-bottom: 12px;
    border: 1px solid var(--uf-border);
    box-shadow: var(--uf-box-shadow);
}

.uf-query-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 20px;
    flex-wrap: wrap;
}

.uf-query-conditions {
    display: flex;
    align-items: center;
    gap: 16px;
    flex: 1;
    min-width: 0;
}

.uf-query-actions {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 0 0 auto;
}

.uf-form-group {
    display: flex;
    align-items: center;
    gap: 6px;
    white-space: nowrap;
}

.uf-form-label {
    font-size: 13px;
    font-weight: 500;
    color: var(--uf-gray-600);
    white-space: nowrap;
}

.uf-form-control {
    font-size: 13px;
    height: 32px;
    padding: 4px 8px;
    border: 1px solid var(--uf-border);
    border-radius: var(--uf-border-radius);
    background: var(--uf-white);
}

.uf-subject-select {
    min-width: 200px;
    max-width: 300px;
}

/* 明细账卡片样式 */
.uf-ledger-card {
    border: 1px solid var(--uf-border);
    box-shadow: var(--uf-box-shadow);
}

.uf-card-header-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 600;
    color: var(--uf-primary);
}

.uf-card-header-actions {
    display: flex;
    align-items: center;
    gap: 12px;
}

.uf-record-count {
    font-size: 12px;
    color: var(--uf-muted);
}

.uf-header-tools {
    display: flex;
    gap: 4px;
}

/* 表格容器样式 */
.uf-table-container {
    overflow: auto;
    max-height: 70vh;
    border: 1px solid var(--uf-border);
}

.uf-table-container.uf-fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1000;
    max-height: 100vh;
    background: var(--uf-white);
}

/* 明细账表格样式 - 13px字体标准 */
.uf-ledger-table {
    width: 100%;
    border-collapse: collapse;
    font-family: var(--uf-font-family);
    font-size: 13px;
    background: var(--uf-white);
    table-layout: fixed;
}

.uf-ledger-table th {
    background: linear-gradient(to bottom, var(--uf-primary) 0%, var(--uf-primary-dark) 100%);
    color: white;
    text-align: center;
    vertical-align: middle;
    font-weight: 600;
    font-size: 13px;
    border: 1px solid var(--uf-grid-border);
    padding: 8px 6px;
    height: 36px;
    position: sticky;
    top: 0;
    z-index: 10;
}

.uf-ledger-table th.sortable {
    cursor: pointer;
    user-select: none;
    position: relative;
}

.uf-ledger-table th.sortable:hover {
    background: linear-gradient(to bottom, var(--uf-primary-light) 0%, var(--uf-primary) 100%);
}

.uf-sort-icon {
    margin-left: 4px;
    font-size: 11px;
    opacity: 0.7;
}

.uf-sort-icon.uf-sort-active {
    opacity: 1;
    color: #fff;
}

/* 表格列宽定义 */
.uf-col-line-no { width: 60px; }
.uf-col-date { width: 100px; }
.uf-col-voucher { width: 100px; }
.uf-col-summary { width: auto; min-width: 200px; }
.uf-col-debit { width: 120px; }
.uf-col-credit { width: 120px; }
.uf-col-balance { width: 120px; }
.uf-col-operations { width: 80px; }

/* 表格行样式 */
.uf-ledger-table td {
    vertical-align: middle;
    padding: 6px 8px;
    font-size: 13px;
    border: 1px solid var(--uf-grid-border);
    height: 36px;
    line-height: 1.3;
}

.uf-ledger-row:hover {
    background: var(--uf-row-hover) !important;
}

.uf-ledger-row:nth-child(even) {
    background: var(--uf-gray-50);
}

/* 特殊行样式 */
.uf-opening-row {
    background: linear-gradient(to bottom, #fff3cd 0%, #ffeaa7 100%) !important;
    font-weight: 600;
}

.uf-closing-row {
    background: linear-gradient(to bottom, #d1ecf1 0%, #bee5eb 100%) !important;
    font-weight: 600;
}

.uf-opening-row:hover,
.uf-closing-row:hover {
    opacity: 0.9;
}

/* 单元格样式 */
.uf-line-number {
    text-align: center;
    font-weight: 500;
    background: var(--uf-gray-50);
    color: var(--uf-gray-600);
    font-size: 12px;
}

.uf-date-cell {
    text-align: center;
}

.uf-date {
    font-family: 'Courier New', monospace;
    font-size: 12px;
}

.uf-voucher-cell {
    text-align: center;
}

.uf-voucher-number {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    color: var(--uf-primary);
    font-size: 12px;
}

.uf-summary-cell {
    text-align: left;
    padding-left: 12px;
}

.uf-summary-content {
    word-wrap: break-word;
    line-height: 1.4;
}

.uf-opening-text,
.uf-closing-text {
    font-weight: 600;
    color: var(--uf-primary);
}

.uf-normal-text {
    color: var(--uf-gray-700);
}

/* 金额单元格样式 */
.uf-amount-cell {
    text-align: right;
    padding-right: 12px;
    font-family: 'Courier New', 'Times New Roman', monospace;
    font-size: 13px;
    white-space: nowrap;
}

.uf-amount {
    font-weight: 500;
    color: var(--uf-gray-700);
}

.uf-amount-zero {
    color: var(--uf-muted);
    font-style: italic;
}

.uf-balance {
    font-weight: 600;
}

.uf-balance.uf-positive {
    color: var(--uf-amount-positive);
}

.uf-balance.uf-negative {
    color: var(--uf-amount-negative);
}

.uf-balance.uf-zero {
    color: var(--uf-amount-zero);
}

.uf-balance-cell {
    background: var(--uf-gray-50);
}

/* 操作单元格样式 */
.uf-operations-cell {
    text-align: center;
}

.uf-btn-group-sm {
    display: flex;
    gap: 2px;
    justify-content: center;
}

.uf-btn-xs {
    padding: 2px 6px;
    font-size: 11px;
    line-height: 1.2;
    border-radius: 2px;
}

.uf-empty {
    color: var(--uf-muted);
    font-style: italic;
}

/* 响应式设计优化 */
@media (max-width: 1200px) {
    .uf-query-row {
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
    }

    .uf-query-conditions {
        justify-content: center;
        flex-wrap: wrap;
        gap: 12px;
    }

    .uf-query-actions {
        justify-content: center;
        flex-wrap: wrap;
        gap: 6px;
    }

    .uf-card-header-actions {
        flex-direction: column;
        align-items: flex-end;
        gap: 8px;
    }

    .uf-header-tools {
        order: -1;
    }
}

@media (max-width: 768px) {
    .uf-query-conditions {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
    }

    .uf-form-group {
        width: 100%;
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }

    .uf-form-control {
        width: 100%;
    }

    .uf-subject-select {
        min-width: auto;
        max-width: none;
    }

    .uf-query-actions {
        flex-wrap: wrap;
        gap: 6px;
    }

    .uf-table-container {
        max-height: 60vh;
    }

    .uf-ledger-table {
        font-size: 12px;
    }

    .uf-ledger-table th,
    .uf-ledger-table td {
        padding: 4px 6px;
        font-size: 12px;
    }

    /* 移动端隐藏部分列 */
    .uf-col-line-no {
        display: none;
    }

    .uf-col-summary {
        min-width: 150px;
    }
}

@media (max-width: 480px) {
    .uf-ledger-table {
        font-size: 11px;
    }

    .uf-ledger-table th,
    .uf-ledger-table td {
        padding: 3px 4px;
        font-size: 11px;
    }

    /* 超小屏幕进一步简化 */
    .uf-col-voucher {
        display: none;
    }

    .uf-btn-group-sm {
        flex-direction: column;
        gap: 1px;
    }
}

/* 打印样式 */
@media print {
    .uf-toolbar,
    .uf-query-card,
    .uf-card-header,
    .page-actions,
    .uf-btn,
    .uf-header-tools {
        display: none !important;
    }

    .uf-ledger-card {
        border: none !important;
        box-shadow: none !important;
        margin: 0 !important;
        page-break-inside: avoid;
    }

    .uf-card-body {
        padding: 0 !important;
    }

    .uf-table-container {
        max-height: none !important;
        overflow: visible !important;
        border: none !important;
    }

    .uf-ledger-table {
        font-size: 10px !important;
        page-break-inside: avoid;
    }

    .uf-ledger-table th {
        font-size: 10px !important;
        padding: 3px 4px !important;
        background: #f0f0f0 !important;
        color: #000 !important;
    }

    .uf-ledger-table td {
        font-size: 10px !important;
        padding: 3px 4px !important;
    }

    /* 打印时隐藏操作列 */
    .uf-col-operations {
        display: none !important;
    }

    /* 打印时优化特殊行样式 */
    .uf-opening-row,
    .uf-closing-row {
        background: #f0f0f0 !important;
        font-weight: 600 !important;
    }

    body {
        font-size: 10px !important;
        margin: 0 !important;
    }

    /* 打印页眉 */
    @page {
        margin: 1cm;
        @top-center {
            content: "明细账查询报表";
            font-size: 14px;
            font-weight: bold;
        }
    }
}

/* 工具提示样式 */
.uf-tooltip {
    position: relative;
    cursor: help;
}

.uf-tooltip:hover::after {
    content: attr(title);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: var(--uf-dark);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    white-space: nowrap;
    z-index: 1000;
}

/* 加载状态样式 */
.uf-loading {
    position: relative;
    pointer-events: none;
    opacity: 0.6;
}

.uf-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid var(--uf-primary);
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: uf-spin 1s linear infinite;
}

@keyframes uf-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 已生成明细账列表样式 */
.uf-generated-ledgers-card {
    margin-bottom: 12px;
    border: 1px solid var(--uf-border);
    box-shadow: var(--uf-box-shadow);
}

.uf-generated-ledgers-table {
    width: 100%;
    border-collapse: collapse;
    font-family: var(--uf-font-family);
    font-size: 12px;
    background: var(--uf-white);
    table-layout: fixed;
}

.uf-generated-ledgers-table th {
    background: linear-gradient(to bottom, var(--uf-primary) 0%, var(--uf-primary-dark) 100%);
    color: white;
    text-align: center;
    vertical-align: middle;
    font-weight: 600;
    font-size: 12px;
    border: 1px solid var(--uf-grid-border);
    padding: 6px 4px;
    height: 32px;
    position: sticky;
    top: 0;
    z-index: 10;
}

.uf-generated-ledgers-table td {
    vertical-align: middle;
    padding: 4px 6px;
    font-size: 12px;
    border: 1px solid var(--uf-grid-border);
    height: 32px;
    line-height: 1.3;
}

/* 已生成明细账表格列宽 */
.uf-col-period { width: 80px; }
.uf-col-subject { width: auto; min-width: 180px; }
.uf-col-type { width: 70px; }
.uf-col-opening { width: 90px; }
.uf-col-debit { width: 90px; }
.uf-col-credit { width: 90px; }
.uf-col-ending { width: 90px; }
.uf-col-records { width: 60px; }
.uf-col-generated { width: 120px; }
.uf-col-operations { width: 100px; }

/* 已生成明细账单元格样式 */
.uf-period-cell {
    text-align: center;
}

.uf-period-text {
    font-weight: 500;
    color: var(--uf-primary);
    font-size: 11px;
}

.uf-subject-cell {
    text-align: left;
    padding-left: 8px;
}

.uf-subject-info {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.uf-subject-code {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    color: var(--uf-primary);
    font-size: 11px;
}

.uf-subject-name {
    color: var(--uf-gray-700);
    font-size: 11px;
}

.uf-records-cell {
    text-align: center;
}

.uf-record-count-text {
    font-weight: 500;
    color: var(--uf-gray-600);
}

.uf-generated-cell {
    text-align: center;
}

.uf-generated-info {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.uf-generated-time {
    font-size: 11px;
    color: var(--uf-gray-600);
}

.uf-generated-by {
    font-size: 10px;
    color: var(--uf-muted);
}

/* 状态标签样式 */
.uf-status-资产 {
    background: var(--uf-info);
    color: white;
}

.uf-status-负债 {
    background: var(--uf-warning);
    color: white;
}

.uf-status-所有者权益 {
    background: var(--uf-success);
    color: white;
}

.uf-status-收入 {
    background: var(--uf-primary);
    color: white;
}

.uf-status-费用 {
    background: var(--uf-danger);
    color: white;
}

/* 通知系统样式 */
.uf-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    min-width: 300px;
    max-width: 500px;
    border-radius: 4px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    animation: uf-notification-slide-in 0.3s ease-out;
}

.uf-notification-content {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    gap: 12px;
    font-size: 13px;
    line-height: 1.4;
}

.uf-notification-icon {
    flex: 0 0 auto;
    font-size: 16px;
}

.uf-notification-message {
    flex: 1;
    word-wrap: break-word;
}

.uf-notification-close {
    flex: 0 0 auto;
    background: none;
    border: none;
    padding: 4px;
    cursor: pointer;
    opacity: 0.7;
    transition: opacity 0.2s;
}

.uf-notification-close:hover {
    opacity: 1;
}

.uf-notification-success {
    background: var(--uf-success);
    color: white;
}

.uf-notification-warning {
    background: var(--uf-warning);
    color: white;
}

.uf-notification-error {
    background: var(--uf-danger);
    color: white;
}

.uf-notification-info {
    background: var(--uf-info);
    color: white;
}

@keyframes uf-notification-slide-in {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* 页面加载遮罩样式 */
.uf-page-loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    z-index: 9998;
    display: flex;
    align-items: center;
    justify-content: center;
}

.uf-page-loading-content {
    text-align: center;
    padding: 40px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    min-width: 200px;
}

.uf-page-loading-spinner {
    font-size: 32px;
    color: var(--uf-primary);
    margin-bottom: 16px;
}

.uf-page-loading-message {
    font-size: 14px;
    color: var(--uf-gray-600);
    font-weight: 500;
}

/* 表格搜索样式 */
.uf-table-search {
    position: relative;
    display: flex;
    align-items: center;
}

.uf-search-input {
    width: 200px;
    height: 28px;
    padding: 4px 30px 4px 8px;
    border: 1px solid var(--uf-border);
    border-radius: 4px;
    font-size: 12px;
    background: var(--uf-white);
    transition: all 0.2s;
}

.uf-search-input:focus {
    outline: none;
    border-color: var(--uf-primary);
    box-shadow: 0 0 0 2px rgba(22, 93, 255, 0.1);
}

.uf-search-icon {
    position: absolute;
    right: 8px;
    color: var(--uf-muted);
    font-size: 12px;
    pointer-events: none;
}

.uf-search-highlight {
    background: #ffeb3b;
    color: #333;
    padding: 1px 2px;
    border-radius: 2px;
    font-weight: 600;
}

/* 表格工具栏增强 */
.uf-card-header-actions {
    display: flex;
    align-items: center;
    gap: 16px;
    flex-wrap: wrap;
}

@media (max-width: 768px) {
    .uf-table-search {
        order: -1;
        width: 100%;
        margin-bottom: 8px;
    }

    .uf-search-input {
        width: 100%;
    }

    .uf-card-header-actions {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
    }

    .uf-header-tools {
        justify-content: center;
    }
}
</style>
{% endblock %}
