"""
优雅的菜单配置 - 影响最小的导航菜单优化方案
保持所有现有功能，只是重新组织菜单结构
"""
from flask import url_for
from flask_login import current_user
from datetime import date, timedelta, datetime

# 优雅的菜单配置 - 将原有11个主菜单合并为5个核心菜单
ELEGANT_MENU_CONFIG = [
    {
        'id': 'dashboard',
        'name': '工作台',
        'icon': 'fas fa-tachometer-alt',
        'url': 'main.index',
        'children': [
            {
                'id': 'canteen_dashboard',
                'name': '食堂仪表盘',
                'url': 'main.index',
                'icon': 'fas fa-chart-pie'
            }
        ]
    },
    {
        'id': 'daily_management',
        'name': '日常管理',
        'icon': 'fas fa-clipboard-list',
        'module': 'daily',
        'action': 'view',
        'children': [
            {
                'id': 'daily_overview',
                'name': '管理概览',
                'url': 'daily_management.index',
                'icon': 'fas fa-tachometer-alt',
                'module': 'daily',
                'action': 'view'
            },
            {
                'id': 'inspection_records',
                'name': '检查记录',
                'url': 'daily_management.inspections_by_date',
                'url_params': {'date_str': 'today'},
                'icon': 'fas fa-search',
                'module': 'daily',
                'action': 'view'
            },
            {
                'id': 'dining_companions',
                'name': '陪餐记录',
                'url': 'daily_management.companions_by_date',
                'url_params': {'date_str': 'today'},
                'icon': 'fas fa-user-friends',
                'module': 'daily',
                'action': 'view'
            },
            {
                'id': 'training_records',
                'name': '培训记录',
                'url': 'daily_management.trainings_by_date',
                'url_params': {'date_str': 'today'},
                'icon': 'fas fa-graduation-cap',
                'module': 'daily',
                'action': 'view'
            },
            {
                'id': 'special_events',
                'name': '特殊事件',
                'url': 'daily_management.events_by_date',
                'url_params': {'date_str': 'today'},
                'icon': 'fas fa-calendar-alt',
                'module': 'daily',
                'action': 'view'
            },
            {
                'id': 'issue_tracking',
                'name': '问题跟踪',
                'url': 'daily_management.issues_by_date',
                'url_params': {'date_str': 'today'},
                'icon': 'fas fa-exclamation-triangle',
                'module': 'daily',
                'action': 'view'
            }
        ]
    },
    {
        'id': 'menu_planning',
        'name': '菜单规划',
        'icon': 'fas fa-utensils',
        'module': 'weekly_menu',
        'action': 'view',
        'children': [
            {
                'id': 'weekly_menu_plan',
                'name': '周菜单计划',
                'url': 'weekly_menu_v2.plan',
                'icon': 'fas fa-calendar-week',
                'module': 'weekly_menu',
                'action': 'edit'
            },
            {
                'id': 'weekly_menu_list',
                'name': '菜单管理',
                'url': 'weekly_menu_v2.index',
                'icon': 'fas fa-list-alt',
                'module': 'weekly_menu',
                'action': 'view'
            },
            {
                'id': 'menu_sync',
                'name': '菜单同步',
                'url': 'menu_sync.index',
                'icon': 'fas fa-sync',
                'module': 'weekly_menu',
                'action': 'edit'
            },
            {
                'id': 'recipe_management',
                'name': '食谱库',
                'url': 'recipe.index',
                'icon': 'fas fa-book',
                'module': 'recipe',
                'action': 'view'
            },
            {
                'id': 'ingredient_management',
                'name': '食材管理',
                'url': 'ingredient.index',
                'icon': 'fas fa-carrot',
                'module': 'ingredient',
                'action': 'view'
            },
            {
                'id': 'ingredient_category',
                'name': '食材分类',
                'url': 'ingredient_category.index',
                'icon': 'fas fa-tags',
                'module': 'ingredient',
                'action': 'view'
            }
        ]
    },
    {
        'id': 'supply_chain',
        'name': '供应链',
        'icon': 'fas fa-truck-loading',
        'module': 'purchase',
        'action': 'view',
        'children': [
            {
                'id': 'purchase_order_list',
                'name': '采购订单',
                'url': 'purchase_order.index',
                'icon': 'fas fa-shopping-cart',
                'module': 'purchase',
                'action': 'view'
            },
            {
                'id': 'purchase_order_create',
                'name': '智能采购',
                'url': 'purchase_order.create_from_menu',
                'icon': 'fas fa-magic',
                'module': 'purchase',
                'action': 'create'
            },
            {
                'id': 'inventory_list',
                'name': '库存总览',
                'url': 'inventory.index',
                'icon': 'fas fa-boxes',
                'module': 'inventory',
                'action': 'view'
            },
            {
                'id': 'stock_in_list',
                'name': '入库管理',
                'url': 'stock_in.index',
                'icon': 'fas fa-arrow-down',
                'module': 'inventory',
                'action': 'view'
            },
            {
                'id': 'stock_out_list',
                'name': '出库管理',
                'url': 'stock_out.index',
                'icon': 'fas fa-arrow-up',
                'module': 'inventory',
                'action': 'view'
            },
            {
                'id': 'consumption_plan_list',
                'name': '消耗计划',
                'url': 'consumption_plan.index',
                'icon': 'fas fa-chart-line',
                'module': 'inventory',
                'action': 'view'
            },
            {
                'id': 'supplier_list',
                'name': '供应商',
                'url': 'supplier.index',
                'icon': 'fas fa-handshake',
                'module': 'supplier',
                'action': 'view'
            },
            {
                'id': 'supplier_product_list',
                'name': '供应商产品',
                'url': 'supplier_product.index',
                'icon': 'fas fa-box-open',
                'module': 'supplier',
                'action': 'view'
            },
            {
                'id': 'warehouse_list',
                'name': '仓库管理',
                'url': 'warehouse.index',
                'icon': 'fas fa-warehouse',
                'module': 'inventory',
                'action': 'view'
            }
        ]
    },
    {
        'id': 'quality_safety',
        'name': '质量安全',
        'icon': 'fas fa-shield-alt',
        'module': 'ingredient',
        'action': 'view',
        'children': [
            {
                'id': 'food_trace',
                'name': '食材溯源',
                'url': 'food_trace.index',
                'icon': 'fas fa-search-plus',
                'module': 'ingredient',
                'action': 'view'
            },
            {
                'id': 'food_sample',
                'name': '留样记录',
                'url': 'food_sample.index',
                'icon': 'fas fa-vial',
                'module': 'food_sample',
                'action': 'view'
            },
            {
                'id': 'food_sample_management',
                'name': '留样管理',
                'url': 'food_trace.sample_management',
                'icon': 'fas fa-clipboard-check',
                'module': 'food_sample',
                'action': 'view'
            },
            {
                'id': 'employee_management',
                'name': '员工管理',
                'url': 'employee.index',
                'icon': 'fas fa-users',
                'module': 'user',
                'action': 'view'
            }
        ]
    },
    {
        'id': 'financial',
        'name': '财务管理',
        'icon': 'fas fa-calculator',
        'children': [
            {
                'id': 'financial_overview',
                'name': '财务概览',
                'url': 'financial.reports_index',
                'icon': 'fas fa-chart-pie',
                'module': '财务管理',
                'action': 'view'
            },
            {
                'id': 'accounting_subjects',
                'name': '会计科目',
                'url': 'financial.accounting_subjects_index',
                'icon': 'fas fa-list-alt',
                'module': '会计科目管理',
                'action': 'view'
            },
            {
                'id': 'financial_vouchers',
                'name': '财务凭证',
                'url': 'financial.vouchers_index',
                'icon': 'fas fa-file-invoice',
                'module': '财务凭证管理',
                'action': 'view'
            },
            {
                'id': 'account_payables',
                'name': '应付账款',
                'url': 'financial.payables_index',
                'icon': 'fas fa-credit-card',
                'module': '应付账款管理',
                'action': 'view'
            },
            {
                'id': 'payment_records',
                'name': '付款记录',
                'url': 'financial.payments_index',
                'icon': 'fas fa-hand-holding-usd',
                'module': '应付账款管理',
                'action': 'view'
            },
            {
                'id': 'pending_stock_ins',
                'name': '待处理入库',
                'url': 'financial.payables_pending_stock_ins',
                'icon': 'fas fa-tasks',
                'module': '应付账款管理',
                'action': 'create'
            },
            {
                'id': 'detail_ledger',
                'name': '明细账查询',
                'url': 'financial.detail_ledger',
                'icon': 'fas fa-list-ul',
                'module': '财务报表',
                'action': 'view'
            },
            {
                'id': 'general_ledger',
                'name': '总账查询',
                'url': 'financial.general_ledger',
                'icon': 'fas fa-calculator',
                'module': '财务报表',
                'action': 'view'
            },
            {
                'id': 'balance_sheet_detail',
                'name': '科目余额表',
                'url': 'financial.balance_sheet_detail',
                'icon': 'fas fa-balance-scale',
                'module': '财务报表',
                'action': 'view'
            },
            {
                'id': 'balance_sheet',
                'name': '资产负债表',
                'url': 'financial.balance_sheet',
                'icon': 'fas fa-chart-bar',
                'module': '财务报表',
                'action': 'view'
            },
            {
                'id': 'cost_analysis',
                'name': '成本分析表',
                'url': 'financial.cost_analysis',
                'icon': 'fas fa-chart-pie',
                'module': '财务报表',
                'action': 'view'
            },
            {
                'id': 'payables_aging',
                'name': '账龄分析',
                'url': 'financial.payables_aging',
                'icon': 'fas fa-clock',
                'module': '财务报表',
                'action': 'view'
            }
        ]
    },
    {
        'id': 'system_admin',
        'name': '系统',
        'icon': 'fas fa-cog',
        'children': [
            # 基础管理（所有用户可见）
            {
                'id': 'area_list',
                'name': '区域管理',
                'url': 'area.index',
                'icon': 'fas fa-map-marker-alt',
                'module': 'area',
                'action': 'view'
            },
            {
                'id': 'settings',
                'name': '系统设置',
                'url': 'system.settings',
                'icon': 'fas fa-sliders-h',
                'module': 'setting',
                'action': 'view'
            },

            {
                'id': 'admin_dashboard',
                'name': '管理仪表盘',
                'url': 'system.dashboard',
                'icon': 'fas fa-chart-bar',
                'admin_only': True
            },
            {
                'id': 'users',
                'name': '用户管理',
                'url': 'system.users',
                'icon': 'fas fa-users',
                'module': 'user',
                'action': 'view',
                'admin_only': True
            },
            {
                'id': 'roles',
                'name': '角色权限',
                'url': 'system.roles',
                'icon': 'fas fa-user-shield',
                'module': 'role',
                'action': 'view',
                'admin_only': True
            },
            {
                'id': 'data_management',
                'name': '数据管理',
                'url': 'admin_data.data_management',
                'icon': 'fas fa-database',
                'admin_only': True
            },
            {
                'id': 'system_tools',
                'name': '系统工具',
                'url': 'system_fix.index',
                'icon': 'fas fa-tools',
                'admin_only': True
            }
        ]
    }
]

# 从原有菜单配置导入权限检查函数
from app.utils.menu import get_url

def get_elegant_user_menu(user):
    """根据用户权限生成优雅的菜单配置"""
    if not user.is_authenticated:
        return []

    # 导入ModuleVisibility模型
    try:
        from app.models_visibility import ModuleVisibility
    except ImportError:
        ModuleVisibility = None

    result = []

    for item in ELEGANT_MENU_CONFIG:
        # 检查是否仅管理员可见
        if item.get('admin_only') and not user.is_admin():
            continue

        # 检查用户是否有权限访问此菜单项
        if 'module' in item and 'action' in item:
            if not user.has_permission(item['module'], item['action']):
                continue

        # 检查区域级别限制
        if 'area_level' in item and user.area_level > item['area_level']:
            continue

        # 复制菜单项
        menu_item = item.copy()

        # 处理子菜单
        if 'children' in item:
            filtered_children = []

            # 获取用户角色ID列表
            role_ids = [role.id for role in user.roles] if hasattr(user, 'roles') else []

            for child in item['children']:
                # 检查是否仅管理员可见
                if child.get('admin_only') and not user.is_admin():
                    continue

                # 检查用户是否有权限访问此子菜单项
                if 'module' in child and 'action' in child:
                    if not user.has_permission(child['module'], child['action']):
                        continue

                # 检查区域级别限制
                if 'area_level' in child and user.area_level > child['area_level']:
                    continue

                # 检查子模块可见性设置
                if ModuleVisibility and not user.is_admin():
                    try:
                        child_visible = False
                        for role_id in role_ids:
                            if ModuleVisibility.get_visibility(child['id'], role_id):
                                child_visible = True
                                break

                        if not child_visible:
                            continue
                    except Exception as e:
                        from flask import current_app
                        if current_app:
                            current_app.logger.error(f"检查子模块可见性时出错: {str(e)}")

                filtered_children.append(child)

            # 只有当有可访问的子菜单时，才保留此父菜单
            if filtered_children:
                menu_item['children'] = filtered_children
                result.append(menu_item)

        else:
            # 没有子菜单的情况
            result.append(menu_item)

    return result
