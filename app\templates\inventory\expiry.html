{% extends 'base.html' %}

{% block title %}临期/过期库存检查{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">⚠️ 临期/过期库存检查</h5>
                    <div class="card-tools">
                        <a href="{{ url_for('inventory.index') }}" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> 返回库存列表
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- 搜索表单 -->
                    <form method="get" action="{{ url_for('inventory.check_expiry') }}" class="mb-4">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label>仓库</label>
                                    <select name="warehouse_id" class="form-control">
                                        <option value="">全部</option>
                                        {% for warehouse in warehouses %}
                                        <option value="{{ warehouse.id }}" {% if warehouse_id == warehouse.id %}selected{% endif %}>{{ warehouse.name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label>临期天数</label>
                                    <select name="days" class="form-control">
                                        <option value="3" {% if days == 3 %}selected{% endif %}>3天内过期</option>
                                        <option value="7" {% if days == 7 %}selected{% endif %}>7天内过期</option>
                                        <option value="15" {% if days == 15 %}selected{% endif %}>15天内过期</option>
                                        <option value="30" {% if days == 30 %}selected{% endif %}>30天内过期</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <button type="submit" class="btn btn-primary btn-block">搜索</button>
                                </div>
                            </div>
                        </div>
                    </form>

                    <!-- 已过期库存 -->
                    <div class="card card-danger">
                        <div class="card-header">
                            <h6 class="card-title">🚨 已过期库存</h6>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-bordered table-striped">
                                    <thead>
                                        <tr>
                                            <th>食材名称</th>
                                            <th>供应商</th>
                                            <th>存储位置</th>
                                            <th>批次号</th>
                                            <th>数量</th>
                                            <th>单位</th>
                                            <th>过期日期</th>
                                            <th>过期天数</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for inventory in expired_inventories %}
                                        <tr>
                                            <td>{{ inventory.ingredient.name }}</td>
                                            <td>{{ inventory.supplier.name if inventory.supplier else '-' }}</td>
                                            <td>{{ inventory.storage_location.name }} ({{ inventory.storage_location.location_code }})</td>
                                            <td>{{ inventory.batch_number }}</td>
                                            <td>{{ inventory.quantity }}</td>
                                            <td>{{ inventory.unit }}</td>
                                            <td>{{  inventory.expiry_date|format_datetime('%Y-%m-%d')  }}</td>
                                            <td>
                                                <span class="badge badge-danger">{{ inventory.days_expired }} 天</span>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="{{ url_for('inventory.detail', id=inventory.id) }}" class="btn btn-info btn-sm">
                                                        <i class="fas fa-eye"></i> 查看
                                                    </a>
                                                    <button type="button" class="btn btn-danger btn-sm" onclick="handleExpiredItem({{ inventory.id }}, '报废')">
                                                        <i class="fas fa-trash"></i> 标记报废
                                                    </button>
                                                    <button type="button" class="btn btn-warning btn-sm" onclick="handleExpiredItem({{ inventory.id }}, '退货')">
                                                        <i class="fas fa-undo"></i> 申请退货
                                                    </button>
                                                    <button type="button" class="btn btn-secondary btn-sm" onclick="handleExpiredItem({{ inventory.id }}, '转移')">
                                                        <i class="fas fa-exchange-alt"></i> 转移处理
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                        {% else %}
                                        <tr>
                                            <td colspan="9" class="text-center">暂无已过期库存</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- 临期库存 -->
                    <div class="card card-warning mt-4">
                        <div class="card-header">
                            <h6 class="card-title">⏰ 临期库存 ({{ days }}天内过期)</h6>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-bordered table-striped">
                                    <thead>
                                        <tr>
                                            <th>食材名称</th>
                                            <th>供应商</th>
                                            <th>存储位置</th>
                                            <th>批次号</th>
                                            <th>数量</th>
                                            <th>单位</th>
                                            <th>过期日期</th>
                                            <th>剩余天数</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for inventory in expiring_inventories %}
                                        {% if inventory.status != '已过期' %}
                                        <tr>
                                            <td>{{ inventory.ingredient.name }}</td>
                                            <td>{{ inventory.supplier.name if inventory.supplier else '-' }}</td>
                                            <td>{{ inventory.storage_location.name }} ({{ inventory.storage_location.location_code }})</td>
                                            <td>{{ inventory.batch_number }}</td>
                                            <td>{{ inventory.quantity }}</td>
                                            <td>{{ inventory.unit }}</td>
                                            <td>{{  inventory.expiry_date|format_datetime('%Y-%m-%d')  }}</td>
                                            <td>
                                                <span class="badge {% if inventory.days_remaining <= 3 %}badge-danger{% elif inventory.days_remaining <= 7 %}badge-warning{% else %}badge-info{% endif %}">
                                                    {{ inventory.days_remaining }} 天
                                                </span>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="{{ url_for('inventory.detail', id=inventory.id) }}" class="btn btn-info btn-sm">
                                                        <i class="fas fa-eye"></i> 查看
                                                    </a>
                                                    <button type="button" class="btn btn-warning btn-sm" onclick="handleExpiringItem({{ inventory.id }}, '优先使用')">
                                                        <i class="fas fa-star"></i> 优先使用
                                                    </button>
                                                    <button type="button" class="btn btn-danger btn-sm" onclick="handleExpiringItem({{ inventory.id }}, '报废')">
                                                        <i class="fas fa-trash"></i> 标记报废
                                                    </button>
                                                    <button type="button" class="btn btn-secondary btn-sm" onclick="handleExpiringItem({{ inventory.id }}, '转移')">
                                                        <i class="fas fa-exchange-alt"></i> 转移处理
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                        {% endif %}
                                        {% else %}
                                        <tr>
                                            <td colspan="9" class="text-center">暂无临期库存</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 处理对话框 -->
<div class="modal fade" id="handlingModal" tabindex="-1" role="dialog" aria-labelledby="handlingModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="handlingModalLabel">过期食材处理</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="handlingForm">
                    <input type="hidden" id="inventoryId" name="inventory_id">
                    <input type="hidden" id="handlingType" name="handling_type">

                    <div class="form-group">
                        <label for="quantity">处理数量</label>
                        <div class="input-group">
                            <input type="number" class="form-control" id="quantity" name="quantity" step="0.01" required>
                            <div class="input-group-append">
                                <span class="input-group-text" id="unitText">-</span>
                            </div>
                        </div>
                        <small class="form-text text-muted">可用数量: <span id="availableQuantity">-</span></small>
                    </div>

                    <div class="form-group">
                        <label for="plannedDate">计划处理日期</label>
                        <input type="date" class="form-control" id="plannedDate" name="planned_date" required>
                    </div>

                    <div class="form-group">
                        <label for="reason">处理原因</label>
                        <textarea class="form-control" id="reason" name="reason" rows="3" placeholder="请说明处理原因..."></textarea>
                    </div>

                    <!-- 退货特定字段 -->
                    <div id="returnFields" style="display: none;">
                        <div class="form-group">
                            <label for="supplierInfo">供应商信息</label>
                            <input type="text" class="form-control" id="supplierInfo" readonly>
                        </div>
                    </div>

                    <!-- 转移特定字段 -->
                    <div id="transferFields" style="display: none;">
                        <div class="form-group">
                            <label for="transferTo">转移目标</label>
                            <input type="text" class="form-control" id="transferTo" name="transfer_to" placeholder="转移到哪里...">
                        </div>
                        <div class="form-group">
                            <label for="transferPurpose">转移用途</label>
                            <input type="text" class="form-control" id="transferPurpose" name="transfer_purpose" placeholder="转移用于什么...">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="notes">备注</label>
                        <textarea class="form-control" id="notes" name="notes" rows="2" placeholder="其他备注信息..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="submitHandling()">确认处理</button>
            </div>
        </div>
    </div>
</div>

<script>
let currentInventory = null;

// 处理过期食材
function handleExpiredItem(inventoryId, handlingType) {
    showHandlingModal(inventoryId, handlingType);
}

// 处理临期食材
function handleExpiringItem(inventoryId, handlingType) {
    if (handlingType === '优先使用') {
        // 优先使用逻辑 - 可以跳转到消耗计划或直接标记
        if (confirm('是否将此食材标记为优先使用？这将在消耗计划中优先安排此食材。')) {
            markAsPriority(inventoryId);
        }
    } else {
        showHandlingModal(inventoryId, handlingType);
    }
}

// 显示处理对话框
function showHandlingModal(inventoryId, handlingType) {
    // 获取库存信息
    fetch(`/inventory/api/detail/${inventoryId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                currentInventory = data.inventory;

                // 设置表单值
                document.getElementById('inventoryId').value = inventoryId;
                document.getElementById('handlingType').value = handlingType;
                document.getElementById('quantity').value = currentInventory.quantity;
                document.getElementById('quantity').max = currentInventory.quantity;
                document.getElementById('unitText').textContent = currentInventory.unit;
                document.getElementById('availableQuantity').textContent = `${currentInventory.quantity} ${currentInventory.unit}`;

                // 设置默认日期为今天
                document.getElementById('plannedDate').value = new Date().toISOString().split('T')[0];

                // 更新对话框标题
                document.getElementById('handlingModalLabel').textContent = `${handlingType} - ${currentInventory.ingredient_name}`;

                // 显示/隐藏特定字段
                document.getElementById('returnFields').style.display = handlingType === '退货' ? 'block' : 'none';
                document.getElementById('transferFields').style.display = handlingType === '转移' ? 'block' : 'none';

                if (handlingType === '退货' && currentInventory.supplier_name) {
                    document.getElementById('supplierInfo').value = currentInventory.supplier_name;
                }

                // 显示对话框
                $('#handlingModal').modal('show');
            } else {
                alert('获取库存信息失败: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('获取库存信息失败');
        });
}

// 提交处理记录
function submitHandling() {
    const form = document.getElementById('handlingForm');
    const formData = new FormData(form);

    // 转换为JSON
    const data = {};
    formData.forEach((value, key) => {
        data[key] = value;
    });

    // 验证数量
    if (parseFloat(data.quantity) > currentInventory.quantity) {
        alert('处理数量不能超过可用数量');
        return;
    }

    // 发送请求
    fetch('/expiry-handling/create', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('处理记录创建成功');
            $('#handlingModal').modal('hide');
            // 刷新页面
            location.reload();
        } else {
            alert('创建失败: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('创建失败');
    });
}

// 标记为优先使用
function markAsPriority(inventoryId) {
    fetch('/inventory/api/mark-priority', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            inventory_id: inventoryId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('已标记为优先使用');
            location.reload();
        } else {
            alert('标记失败: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('标记失败');
    });
}

// 批量处理功能
function batchHandle() {
    // 获取所有选中的库存
    const checkboxes = document.querySelectorAll('input[name="inventory_ids"]:checked');
    if (checkboxes.length === 0) {
        alert('请选择要处理的库存');
        return;
    }

    const inventoryIds = Array.from(checkboxes).map(cb => cb.value);
    const handlingType = prompt('请输入处理类型（报废/退货/转移）：');

    if (!handlingType || !['报废', '退货', '转移'].includes(handlingType)) {
        alert('无效的处理类型');
        return;
    }

    const plannedDate = prompt('请输入计划处理日期（YYYY-MM-DD）：');
    if (!plannedDate) {
        return;
    }

    const reason = prompt('请输入处理原因：') || '';

    // 发送批量处理请求
    fetch('/expiry-handling/batch-create', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            inventory_ids: inventoryIds,
            handling_type: handlingType,
            planned_date: plannedDate,
            reason: reason
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(`批量处理成功：${data.message}`);
            location.reload();
        } else {
            alert('批量处理失败: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('批量处理失败');
    });
}
</script>

{% endblock %}
