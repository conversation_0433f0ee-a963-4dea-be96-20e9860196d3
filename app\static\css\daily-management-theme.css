/* 
 * 日常管理模块专用主题样式
 * 确保日常管理模块始终使用蓝色主题
 * 版本: 1.0.0
 */

/* ==================== 强制蓝色主题 ==================== */
/* 为日常管理模块强制设置蓝色主题变量 */
:root {
    --theme-primary: #007bff !important;
    --theme-primary-dark: #0056b3 !important;
    --theme-primary-light: #b3d7ff !important;
}

/* ==================== 导航栏样式 ==================== */
/* 确保左侧导航栏使用蓝色 */
.sidebar {
    background: #007bff !important;
}

.sidebar-header {
    background: #007bff !important;
}

.sidebar-nav .nav-link:hover {
    background: #0056b3 !important;
}

.sidebar-nav .nav-link.active {
    background: #0056b3 !important;
}

/* 右侧顶部工具栏 */
.top-toolbar {
    background: #007bff !important;
}

/* ==================== 页面内导航 ==================== */
/* 日常管理页面内的导航标签 */
.nav-tabs .nav-link.active {
    background-color: #007bff !important;
    border-color: #007bff !important;
    color: white !important;
}

.nav-tabs .nav-link:hover {
    border-color: #007bff !important;
    color: #007bff !important;
}

/* ==================== 按钮样式 ==================== */
/* 主要按钮使用蓝色 */
.btn-primary {
    background-color: #007bff !important;
    border-color: #007bff !important;
}

.btn-primary:hover {
    background-color: #0056b3 !important;
    border-color: #0056b3 !important;
}

.btn-primary:focus,
.btn-primary.focus {
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
}

/* ==================== 卡片头部 ==================== */
/* 卡片头部的主色调文字 */
.text-primary {
    color: #007bff !important;
}

.card-header .text-primary {
    color: #007bff !important;
}

/* ==================== 表格样式 ==================== */
/* 表格头部样式 */
.table thead th {
    border-top: 1px solid #007bff;
}

/* ==================== 徽章样式 ==================== */
/* 状态徽章 */
.badge-primary {
    background-color: #007bff !important;
}

/* ==================== 进度条样式 ==================== */
/* 进度条 */
.progress-bar {
    background-color: #007bff !important;
}

/* ==================== 链接样式 ==================== */
/* 链接颜色 */
a {
    color: #007bff !important;
}

a:hover {
    color: #0056b3 !important;
}

/* ==================== 表单样式 ==================== */
/* 表单控件焦点状态 */
.form-control:focus {
    border-color: #007bff !important;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
}

/* 自定义选择框 */
.custom-select:focus {
    border-color: #007bff !important;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
}

/* ==================== 分页样式 ==================== */
/* 分页按钮 */
.page-link {
    color: #007bff !important;
}

.page-item.active .page-link {
    background-color: #007bff !important;
    border-color: #007bff !important;
}

.page-link:hover {
    color: #0056b3 !important;
}

/* ==================== 模态框样式 ==================== */
/* 模态框头部 */
.modal-header {
    border-bottom: 1px solid #007bff;
}

/* ==================== 警告框样式 ==================== */
/* 信息警告框 */
.alert-info {
    color: #004085;
    background-color: #cce7ff;
    border-color: #007bff;
}

/* ==================== 响应式优化 ==================== */
/* 移动端优化 */
@media (max-width: 768px) {
    .sidebar {
        background: #007bff !important;
    }
    
    .top-toolbar {
        background: #007bff !important;
    }
}

/* ==================== 打印样式 ==================== */
/* 打印时保持蓝色主题 */
@media print {
    .sidebar,
    .top-toolbar,
    .btn-primary {
        background: #007bff !important;
        color: white !important;
    }
}

/* ==================== 兼容性 ==================== */
/* 确保与其他主题系统的兼容性 */
[data-theme="success"] .daily-management-page :root {
    --theme-primary: #007bff !important;
    --theme-primary-dark: #0056b3 !important;
}

[data-theme="warning"] .daily-management-page :root {
    --theme-primary: #007bff !important;
    --theme-primary-dark: #0056b3 !important;
}

[data-theme="info"] .daily-management-page :root {
    --theme-primary: #007bff !important;
    --theme-primary-dark: #0056b3 !important;
}

[data-theme="danger"] .daily-management-page :root {
    --theme-primary: #007bff !important;
    --theme-primary-dark: #0056b3 !important;
}

/* ==================== 调试信息 ==================== */
/* 在开发模式下显示主题信息 */
.debug-theme-info {
    position: fixed;
    bottom: 10px;
    right: 10px;
    background: rgba(0, 123, 255, 0.9);
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    z-index: 9999;
    display: none;
}

.debug-mode .debug-theme-info {
    display: block;
}

.debug-theme-info::after {
    content: "日常管理蓝色主题已激活";
}
