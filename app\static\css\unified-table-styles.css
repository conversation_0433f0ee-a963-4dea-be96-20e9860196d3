/*
 * StudentsCMSSP 统一表格样式
 * 基于员工管理页面的表格样式，应用到全系统所有表格
 * 版本: 1.0.0
 */

/* ==================== 全局表格样式重置 ==================== */

/* 重置所有表格样式，使用统一的设计 */
.table,
.enterprise-table,
.uf-table,
.table-modern,
table {
    width: 100%;
    margin-bottom: 1rem;
    color: #212529;
    background-color: transparent;
    border-collapse: collapse;
    font-size: 13px !important;
    line-height: 1.4;
}

/* ==================== 统一表头样式 ==================== */

/* 所有表头使用统一的样式 - 基于员工管理页面 */
.table thead th,
.table th,
.enterprise-table thead th,
.enterprise-table th,
.uf-table thead th,
.uf-table th,
.table-modern thead th,
.table-modern th,
table thead th,
table th,
.table-sm thead th,
.table-bordered thead th,
.table-striped thead th,
.table-hover thead th,
.thead-dark th,
.thead-light th {
    /* 背景和颜色 */
    background-color: var(--theme-primary, #007bff) !important;
    color: white !important;
    
    /* 字体样式 */
    font-weight: 600 !important;
    font-size: 13px !important;
    text-transform: none !important;
    letter-spacing: normal !important;
    
    /* 布局和间距 */
    padding: 12px 8px !important;
    text-align: left !important;
    vertical-align: middle !important;
    white-space: nowrap;
    
    /* 边框 */
    border-bottom: 1px solid var(--theme-primary-dark, #0056b3) !important;
    border-top: none !important;
    border-left: none !important;
    border-right: 1px solid rgba(255, 255, 255, 0.2) !important;
    
    /* 其他 */
    position: relative;
    line-height: 1.4;
}

/* 表头最后一列去除右边框 */
.table thead th:last-child,
.enterprise-table thead th:last-child,
.uf-table thead th:last-child,
.table-modern thead th:last-child,
table thead th:last-child {
    border-right: none !important;
}

/* 表头第一列和最后一列圆角 */
.table thead th:first-child,
.enterprise-table thead th:first-child,
.uf-table thead th:first-child,
.table-modern thead th:first-child,
table thead th:first-child {
    border-top-left-radius: 6px;
}

.table thead th:last-child,
.enterprise-table thead th:last-child,
.uf-table thead th:last-child,
.table-modern thead th:last-child,
table thead th:last-child {
    border-top-right-radius: 6px;
}

/* ==================== 表格内容样式 ==================== */

/* 表格行样式 */
.table tbody tr,
.enterprise-table tbody tr,
.uf-table tbody tr,
.table-modern tbody tr,
table tbody tr {
    transition: all 0.2s ease;
    border-bottom: 1px solid #e9ecef;
}

/* 表格行悬停效果 */
.table tbody tr:hover,
.table-hover tbody tr:hover,
.enterprise-table tbody tr:hover,
.uf-table tbody tr:hover,
.table-modern tbody tr:hover,
table tbody tr:hover {
    background-color: var(--theme-gray-100, #f8f9fa) !important;
    transform: none; /* 移除缩放效果，保持稳定 */
}

/* 表格单元格样式 */
.table tbody td,
.table td,
.enterprise-table tbody td,
.enterprise-table td,
.uf-table tbody td,
.uf-table td,
.table-modern tbody td,
.table-modern td,
table tbody td,
table td {
    padding: 8px !important;
    vertical-align: middle !important;
    border-top: 1px solid #e9ecef !important;
    border-bottom: none !important;
    border-left: none !important;
    border-right: none !important;
    font-size: 13px !important;
    line-height: 1.4;
    color: #212529;
}

/* ==================== 表格条纹样式 ==================== */

/* 条纹表格样式 */
.table-striped tbody tr:nth-of-type(odd),
.enterprise-table.table-striped tbody tr:nth-of-type(odd),
.uf-table.table-striped tbody tr:nth-of-type(odd) {
    background-color: var(--theme-gray-50, #f8f9fa) !important;
}

.table-striped tbody tr:nth-of-type(even),
.enterprise-table.table-striped tbody tr:nth-of-type(even),
.uf-table.table-striped tbody tr:nth-of-type(even) {
    background-color: white !important;
}

/* ==================== 表格边框样式 ==================== */

/* 带边框的表格 */
.table-bordered,
.enterprise-table.table-bordered,
.uf-table.table-bordered {
    border: 1px solid #dee2e6;
    border-radius: 6px;
    overflow: hidden;
}

.table-bordered th,
.table-bordered td,
.enterprise-table.table-bordered th,
.enterprise-table.table-bordered td,
.uf-table.table-bordered th,
.uf-table.table-bordered td {
    border: 1px solid #dee2e6 !important;
}

/* ==================== 响应式表格 ==================== */

.table-responsive {
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    margin-bottom: 1.5rem;
    border: 1px solid #e9ecef;
}

/* ==================== 特殊列样式 ==================== */

/* 数字列右对齐 */
.table .number-col,
.enterprise-table .number-col,
.uf-table .number-col,
.amount-column {
    text-align: right !important;
    font-family: 'Courier New', monospace;
}

/* 状态列居中 */
.table .status-col,
.enterprise-table .status-col,
.uf-table .status-col {
    text-align: center !important;
}

/* 操作列居中 */
.table .action-col,
.enterprise-table .action-col,
.uf-table .action-col {
    text-align: center !important;
    white-space: nowrap;
}

/* ==================== 移动端优化 ==================== */

@media (max-width: 768px) {
    .table thead th,
    .enterprise-table thead th,
    .uf-table thead th,
    table thead th {
        font-size: 12px !important;
        padding: 8px 6px !important;
    }
    
    .table tbody td,
    .table td,
    .enterprise-table tbody td,
    .enterprise-table td,
    .uf-table tbody td,
    .uf-table td,
    table tbody td,
    table td {
        font-size: 12px !important;
        padding: 6px !important;
    }
    
    .table-responsive {
        border-radius: 8px;
        margin-bottom: 1rem;
    }
}

/* ==================== 表格内元素样式 ==================== */

/* 表格内按钮样式 */
.table .btn,
.enterprise-table .btn,
.uf-table .btn {
    font-size: 12px !important;
    padding: 4px 8px !important;
    margin: 1px !important;
}

/* 表格内徽章样式 */
.table .badge,
.enterprise-table .badge,
.uf-table .badge {
    font-size: 11px !important;
    padding: 3px 6px !important;
}

/* 表格内链接样式 */
.table a,
.enterprise-table a,
.uf-table a {
    color: var(--theme-primary, #007bff);
    text-decoration: none;
}

.table a:hover,
.enterprise-table a:hover,
.uf-table a:hover {
    color: var(--theme-primary-dark, #0056b3);
    text-decoration: underline;
}

/* ==================== 排序功能样式 ==================== */

.table th.sortable,
.enterprise-table th.sortable,
.uf-table th.sortable {
    cursor: pointer;
    user-select: none;
    position: relative;
}

.table th.sortable:hover,
.enterprise-table th.sortable:hover,
.uf-table th.sortable:hover {
    background-color: var(--theme-primary-dark, #0056b3) !important;
}

.table th.sortable::after,
.enterprise-table th.sortable::after,
.uf-table th.sortable::after {
    content: '\f0dc';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    opacity: 0.5;
    font-size: 10px;
}

/* ==================== 覆盖其他样式 ==================== */

/* 确保覆盖所有可能的表头样式 */
.table thead th,
.table th,
.enterprise-table thead th,
.uf-table thead th,
.table-modern thead th,
table thead th {
    background: var(--theme-primary, #007bff) !important;
    color: white !important;
}

/* 强制覆盖任何内联样式 */
.table thead th[style*="background"],
.enterprise-table thead th[style*="background"],
.uf-table thead th[style*="background"],
table thead th[style*="background"] {
    background: var(--theme-primary, #007bff) !important;
    color: white !important;
}
