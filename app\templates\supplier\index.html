{% extends 'base.html' %}

{% block title %}供应商管理 - {{ super() }}{% endblock %}

{% block content %}
<div class="card">
                <div class="card-header">
                    <!-- 桌面端布局 -->
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="card-title mb-0">供应商管理</h3>
                            {% if current_area %}
                            <small class="text-muted">
                                当前区域:
                                {% for area in area_path %}
                                <span class="badge badge-info">{{ area.get_level_name() }} - {{ area.name }}</span>
                                {% if not loop.last %} <i class="fas fa-chevron-right"></i> {% endif %}
                                {% endfor %}
                            </small>
                            {% endif %}
                        </div>
                        <div class="card-tools">
                            <a href="{{ url_for('supplier.create') }}" class="btn btn-primary btn-sm">
                                <i class="fas fa-plus"></i> 添加供应商
                            </a>
                            <a href="{{ url_for('supplier_category.index') }}" class="btn btn-info btn-sm">
                                <i class="fas fa-tags"></i> 分类管理
                            </a>
                            <a href="{{ url_for('supplier_school.index') }}" class="btn btn-success btn-sm">
                                <i class="fas fa-school"></i> 学校绑定管理
                            </a>
                        </div>
                    </div>

                    <!-- 移动端布局 -->
                    <div class="mobile-only">
                        <div class="row">
                            <div class="col-12">
                                <h3 class="card-title mb-2">供应商管理</h3>
                                {% if current_area %}
                                <div class="mb-3">
                                    <small class="text-muted d-block mb-2">当前区域:</small>
                                    {% for area in area_path %}
                                    <span class="badge badge-info">{{ area.get_level_name() }} - {{ area.name }}</span>
                                    {% if not loop.last %} <i class="fas fa-chevron-right"></i> {% endif %}
                                    {% endfor %}
                                </div>
                                {% endif %}
                                <div class="action-buttons">
                                    <a href="{{ url_for('supplier.create') }}" class="btn btn-primary">
                                        <i class="fas fa-plus"></i> 添加供应商
                                    </a>
                                    <a href="{{ url_for('supplier_category.index') }}" class="btn btn-info">
                                        <i class="fas fa-tags"></i> 分类管理
                                    </a>
                                    <a href="{{ url_for('supplier_school.index') }}" class="btn btn-success">
                                        <i class="fas fa-school"></i> 学校绑定管理
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- 企业级供应商列表 -->
                    <div class="enterprise-table-container desktop-only">
                        <div class="table-toolbar">
                            <div class="table-toolbar-left">
                                <!-- 整合搜索功能 -->
                                <form method="GET" class="d-flex align-items-end gap-3">
                                    <div class="form-group mb-0">
                                        <label for="category_id" class="form-label">分类</label>
                                        <select class="form-select" id="category_id" name="category_id" style="min-width: 120px;">
                                            <option value="">所有分类</option>
                                            {% for category in categories %}
                                            <option value="{{ category.id }}" {% if category_id == category.id %}selected{% endif %}>
                                                {{ category.name }}
                                            </option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                    <div class="form-group mb-0">
                                        <label for="keyword" class="form-label">关键词</label>
                                        <input type="text" class="form-control" id="keyword" name="keyword"
                                               value="{{ keyword }}" placeholder="供应商名称" style="min-width: 200px;">
                                    </div>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search"></i> 搜索
                                    </button>
                                    {% if category_id or keyword %}
                                    <a href="{{ url_for('supplier.index') }}" class="btn btn-outline-secondary">
                                        <i class="fas fa-times"></i> 清除
                                    </a>
                                    {% endif %}
                                </form>
                                <span class="text-muted ms-3">共 {{ pagination.total if pagination else suppliers|length }} 条记录</span>
                            </div>
                            <div class="table-toolbar-right">
                                <div class="table-search">
                                    <i class="fas fa-search search-icon"></i>
                                    <input type="text" class="form-control" placeholder="快速筛选..." id="quickSearch">
                                </div>
                                <div class="dropdown">
                                    <button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                        <i class="fas fa-download"></i> 导出
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="#" onclick="exportSuppliers('excel')">
                                            <i class="fas fa-file-excel text-success"></i> 导出Excel
                                        </a></li>
                                        <li><a class="dropdown-item" href="#" onclick="exportSupplierStatistics()">
                                            <i class="fas fa-chart-bar text-info"></i> 采购统计报表
                                        </a></li>
                                    </ul>
                                </div>
                                <button class="btn btn-outline-success btn-sm" onclick="importSuppliers()">
                                    <i class="fas fa-upload"></i> 导入
                                </button>
                                <button class="btn btn-outline-primary btn-sm" onclick="refreshTable()">
                                    <i class="fas fa-sync"></i> 刷新
                                </button>
                            </div>
                        </div>

                        <table class="enterprise-table">
                            <thead>
                                <tr>
                                    <th class="sortable number-col">ID</th>
                                    <th class="sortable">供应商名称</th>
                                    <th class="sortable">分类</th>
                                    <th>联系人</th>
                                    <th>联系电话</th>
                                    <th>合作学校</th>
                                    <th class="text-center sortable">评级</th>
                                    <th class="status-col">状态</th>
                                    <th class="action-col">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for supplier in suppliers %}
                                <tr>
                                    <td class="number-col">{{ supplier.id }}</td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="supplier-avatar me-3">
                                                <i class="fas fa-building text-primary"></i>
                                            </div>
                                            <div>
                                                <strong>{{ supplier.name }}</strong>
                                                {% if supplier.description %}
                                                <div class="small text-muted">{{ supplier.description|truncate(40) }}</div>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        {% if supplier.category %}
                                        <span class="badge badge-secondary">{{ supplier.category.name }}</span>
                                        {% else %}
                                        <span class="badge badge-light">未分类</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if supplier.contact_person %}
                                        <div>{{ supplier.contact_person }}</div>
                                        {% if supplier.contact_title %}
                                        <small class="text-muted">{{ supplier.contact_title }}</small>
                                        {% endif %}
                                        {% else %}
                                        <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if supplier.phone %}
                                        <a href="tel:{{ supplier.phone }}" class="text-decoration-none">
                                            <i class="fas fa-phone text-success me-1"></i>{{ supplier.phone }}
                                        </a>
                                        {% else %}
                                        <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if supplier.accessible_relations %}
                                        <div class="school-badges">
                                            {% for relation in supplier.accessible_relations %}
                                                {% if relation.status == 1 %}
                                                <span class="badge badge-success mb-1">{{ relation.area.name }}</span>
                                                {% endif %}
                                            {% endfor %}
                                        </div>
                                        {% else %}
                                        <span class="text-muted small">暂无合作学校</span>
                                        {% endif %}
                                    </td>
                                    <td class="text-center">
                                        {% if supplier.rating %}
                                        <div class="rating-stars text-warning">
                                            {% for i in range(supplier.rating|int) %}
                                            <i class="fas fa-star"></i>
                                            {% endfor %}
                                            {% if supplier.rating % 1 > 0 %}
                                            <i class="fas fa-star-half-alt"></i>
                                            {% endif %}
                                            <div class="small text-muted">{{ supplier.rating }}/5</div>
                                        </div>
                                        {% else %}
                                        <span class="text-muted small">暂无评级</span>
                                        {% endif %}
                                    </td>
                                    <td class="status-col">
                                        {% if supplier.status == 1 %}
                                        <span class="badge badge-success">
                                            <i class="fas fa-check-circle me-1"></i>合作中
                                        </span>
                                        {% else %}
                                        <span class="badge badge-secondary">
                                            <i class="fas fa-pause-circle me-1"></i>已停用
                                        </span>
                                        {% endif %}
                                    </td>
                                    <td class="action-col">
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ url_for('supplier.view', id=supplier.id) }}"
                                               class="btn btn-outline-info btn-xs" title="查看详情">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ url_for('supplier.edit', id=supplier.id) }}"
                                               class="btn btn-outline-primary btn-xs" title="编辑">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-outline-danger btn-xs delete-btn"
                                                    data-id="{{ supplier.id }}" title="删除">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="9" class="table-empty">
                                        <div class="table-empty-icon">
                                            <i class="fas fa-building"></i>
                                        </div>
                                        <div class="table-empty-text">暂无供应商数据</div>
                                        <div class="table-empty-subtext">您可以添加新的供应商或调整筛选条件</div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>

                        {% if pagination and pagination.pages > 1 %}
                        <div class="table-pagination">
                            <div class="pagination-info">
                                显示第 {{ (pagination.page - 1) * pagination.per_page + 1 }} -
                                {{ (pagination.page * pagination.per_page) if (pagination.page * pagination.per_page < pagination.total) else pagination.total }}
                                条，共 {{ pagination.total }} 条记录
                            </div>
                            <nav>
                                <ul class="pagination pagination-sm">
                                    {% if pagination.has_prev %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('supplier.index', page=pagination.prev_num, category_id=category_id, keyword=keyword) }}">
                                            <i class="fas fa-chevron-left"></i>
                                        </a>
                                    </li>
                                    {% else %}
                                    <li class="page-item disabled">
                                        <span class="page-link"><i class="fas fa-chevron-left"></i></span>
                                    </li>
                                    {% endif %}

                                    {% for page in pagination.iter_pages() %}
                                        {% if page %}
                                            {% if page != pagination.page %}
                                            <li class="page-item">
                                                <a class="page-link" href="{{ url_for('supplier.index', page=page, category_id=category_id, keyword=keyword) }}">{{ page }}</a>
                                            </li>
                                            {% else %}
                                            <li class="page-item active">
                                                <span class="page-link">{{ page }}</span>
                                            </li>
                                            {% endif %}
                                        {% else %}
                                        <li class="page-item disabled">
                                            <span class="page-link">...</span>
                                        </li>
                                        {% endif %}
                                    {% endfor %}

                                    {% if pagination.has_next %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('supplier.index', page=pagination.next_num, category_id=category_id, keyword=keyword) }}">
                                            <i class="fas fa-chevron-right"></i>
                                        </a>
                                    </li>
                                    {% else %}
                                    <li class="page-item disabled">
                                        <span class="page-link"><i class="fas fa-chevron-right"></i></span>
                                    </li>
                                    {% endif %}
                                </ul>
                            </nav>
                        </div>
                        {% endif %}
                    </div>

                    <!-- 移动端供应商卡片 -->
                    <div class="mobile-only">
                        {% for supplier in suppliers %}
                        <div class="card mb-3 border-left-{% if supplier.status == 1 %}success{% else %}secondary{% endif %}">
                            <div class="card-body py-2">
                                <div class="row">
                                    <div class="col-8">
                                        <h6 class="mb-1">{{ supplier.name }}</h6>
                                        <small class="text-muted">ID: {{ supplier.id }} | {{ supplier.category.name if supplier.category else '未分类' }}</small>
                                    </div>
                                    <div class="col-4 text-right">
                                        {% if supplier.status == 1 %}
                                        <span class="badge badge-success">合作中</span>
                                        {% else %}
                                        <span class="badge badge-secondary">已停用</span>
                                        {% endif %}
                                    </div>
                                </div>

                                <div class="row mt-2">
                                    <div class="col-6">
                                        <small class="text-muted">联系人</small>
                                        <div class="small">{{ supplier.contact_person or '-' }}</div>
                                    </div>
                                    <div class="col-6">
                                        <small class="text-muted">联系电话</small>
                                        <div class="small">{{ supplier.phone or '-' }}</div>
                                    </div>
                                </div>

                                {% if supplier.rating %}
                                <div class="row mt-1">
                                    <div class="col-12">
                                        <small class="text-muted">评级</small>
                                        <div class="text-warning">
                                            {% for i in range(supplier.rating|int) %}
                                            <i class="fas fa-star"></i>
                                            {% endfor %}
                                            {% if supplier.rating % 1 > 0 %}
                                            <i class="fas fa-star-half-alt"></i>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                                {% endif %}

                                {% if supplier.accessible_relations %}
                                <div class="row mt-1">
                                    <div class="col-12">
                                        <small class="text-muted">合作学校</small>
                                        <div>
                                            {% for relation in supplier.accessible_relations %}
                                                {% if relation.status == 1 %}
                                                <span class="badge badge-success mb-1">{{ relation.area.name }}</span>
                                                {% endif %}
                                            {% endfor %}
                                        </div>
                                    </div>
                                </div>
                                {% endif %}

                                <div class="row mt-2">
                                    <div class="col-12">
                                        <div class="btn-group btn-group-sm w-100" role="group">
                                            <a href="{{ url_for('supplier.view', id=supplier.id) }}" class="btn btn-outline-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ url_for('supplier.edit', id=supplier.id) }}" class="btn btn-outline-primary">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-outline-danger delete-btn" data-id="{{ supplier.id }}">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-building fa-3x text-muted mb-3"></i>
                            <h5>暂无供应商数据</h5>
                            <p class="text-muted">您可以添加新的供应商或调整筛选条件</p>
                        </div>
                        {% endfor %}
                    </div>


                </div>
            </div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">确认删除</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                确定要删除这个供应商吗？此操作不可恢复。
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" id="confirmDelete">确认删除</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}">
    $(document).ready(function() {
        // 删除功能
        var deleteId = null;

        $('.delete-btn').click(function() {
            deleteId = $(this).data('id');
            $('#deleteModal').modal('show');
        });

        $('#confirmDelete').click(function() {
            if (deleteId) {
                $.ajax({
                    url: '{{ url_for("supplier.delete", id=0) }}'.replace('0', deleteId),
                    type: 'POST',
                    success: function(response) {
                        if (response.success) {
                            toastr.success(response.message);
                            setTimeout(function() {
                                window.location.reload();
                            }, 1000);
                        } else {
                            toastr.error(response.message);
                        }
                        $('#deleteModal').modal('hide');
                    },
                    error: function() {
                        toastr.error('删除失败，请稍后重试！');
                        $('#deleteModal').modal('hide');
                    }
                });
            }
        });

        // 企业级表格功能 - 简化版，专注稳定性

        // 快速搜索功能
        $('#quickSearch').on('input', function() {
            var searchTerm = $(this).val().toLowerCase();
            var visibleCount = 0;

            $('.enterprise-table tbody tr').each(function() {
                var rowText = $(this).text().toLowerCase();
                if (rowText.indexOf(searchTerm) === -1) {
                    $(this).hide();
                } else {
                    $(this).show();
                    visibleCount++;
                }
            });

            // 更新显示的记录数
            $('.table-toolbar-left .text-muted').text('显示 ' + visibleCount + ' 条记录');
        });

        // 表格排序功能 - 简化版
        $('.enterprise-table th.sortable').click(function() {
            var $this = $(this);
            var column = $this.index();
            var $tbody = $('.enterprise-table tbody');
            var rows = $tbody.find('tr').toArray();

            // 切换排序状态
            var isAsc = $this.hasClass('sort-asc');
            $('.enterprise-table th').removeClass('sort-asc sort-desc');
            $this.addClass(isAsc ? 'sort-desc' : 'sort-asc');

            // 排序
            rows.sort(function(a, b) {
                var aText = $(a).find('td').eq(column).text().trim();
                var bText = $(b).find('td').eq(column).text().trim();

                // 数字排序
                if (!isNaN(aText) && !isNaN(bText)) {
                    return isAsc ? bText - aText : aText - bText;
                }

                // 文本排序
                return isAsc ? bText.localeCompare(aText) : aText.localeCompare(bText);
            });

            // 重新排列
            $tbody.empty().append(rows);
        });

        // 工具栏功能
        window.exportSuppliers = function(format = 'excel') {
            // 显示加载提示
            toastr.info('正在生成导出文件，请稍候...');

            // 获取当前筛选条件
            const categoryId = document.getElementById('categoryFilter')?.value || '';
            const keyword = document.getElementById('quickSearch')?.value || '';

            const params = new URLSearchParams({
                format: format,
                status: 'all',
                category_id: categoryId,
                keyword: keyword
            });

            // 创建隐藏的下载链接
            const link = document.createElement('a');
            link.href = `/supplier/export?${params.toString()}`;
            link.style.display = 'none';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            // 延迟隐藏提示
            setTimeout(() => {
                toastr.success('导出完成');
            }, 2000);
        };

        window.exportSupplierStatistics = function() {
            toastr.info('正在生成统计报表，请稍候...');

            const link = document.createElement('a');
            link.href = '/supplier/export/statistics';
            link.style.display = 'none';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            setTimeout(() => {
                toastr.success('统计报表导出完成');
            }, 2000);
        };

        window.importSuppliers = function() {
            toastr.info('导入功能开发中...');
        };

        window.refreshTable = function() {
            window.location.reload();
        };

        // 电话号码复制功能
        $('a[href^="tel:"]').click(function(e) {
            e.preventDefault();
            var phoneNumber = $(this).text().trim();
            if (navigator.clipboard) {
                navigator.clipboard.writeText(phoneNumber).then(function() {
                    toastr.success('电话号码已复制');
                });
            }
        });
    });
</script>


{% endblock %}
