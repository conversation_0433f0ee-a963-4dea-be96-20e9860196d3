/* 现代化用户体验增强样式 */

/* ===== 全局增强 ===== */
:root {
    /* 现代化色彩系统 */
    --modern-primary: #667eea;
    --modern-primary-dark: #5a67d8;
    --modern-secondary: #764ba2;
    --modern-success: #48bb78;
    --modern-warning: #ed8936;
    --modern-danger: #f56565;
    --modern-info: #4299e1;
    
    /* 渐变色系 */
    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-success: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
    --gradient-warning: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
    --gradient-danger: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
    
    /* 阴影系统 */
    --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    
    /* 动画系统 */
    --transition-fast: 150ms ease;
    --transition-normal: 300ms ease;
    --transition-slow: 500ms ease;
    
    /* 间距系统 */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;
    
    /* 圆角系统 */
    --radius-sm: 0.125rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    --radius-2xl: 1rem;
    --radius-full: 9999px;
}

/* ===== 现代化按钮系统 ===== */
.btn-modern {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-2);
    padding: var(--space-2) var(--space-4);
    border: none;
    border-radius: var(--radius-md);
    font-size: 13px;
    font-weight: 500;
    line-height: 1.5;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition-fast);
    position: relative;
    overflow: hidden;
    white-space: nowrap;
}

.btn-modern:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-modern:active {
    transform: translateY(0);
}

.btn-modern:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* 按钮变体 */
.btn-primary-modern {
    background: var(--gradient-primary);
    color: white;
}

.btn-primary-modern:hover {
    background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
    color: white;
}

.btn-success-modern {
    background: var(--gradient-success);
    color: white;
}

.btn-warning-modern {
    background: var(--gradient-warning);
    color: white;
}

.btn-danger-modern {
    background: var(--gradient-danger);
    color: white;
}

.btn-outline-modern {
    background: transparent;
    border: 1px solid var(--modern-primary);
    color: var(--modern-primary);
}

.btn-outline-modern:hover {
    background: var(--modern-primary);
    color: white;
}

.btn-ghost-modern {
    background: transparent;
    color: var(--modern-primary);
}

.btn-ghost-modern:hover {
    background: rgba(102, 126, 234, 0.1);
}

/* 按钮尺寸 */
.btn-sm-modern {
    padding: var(--space-1) var(--space-3);
    font-size: 12px;
}

.btn-lg-modern {
    padding: var(--space-3) var(--space-6);
    font-size: 14px;
}

/* ===== 现代化卡片系统 ===== */
.card-modern {
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
    transition: var(--transition-normal);
}

.card-modern:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.card-header-modern {
    padding: var(--space-4) var(--space-6);
    border-bottom: 1px solid #e2e8f0;
    background: #f8fafc;
}

.card-body-modern {
    padding: var(--space-6);
}

.card-footer-modern {
    padding: var(--space-4) var(--space-6);
    border-top: 1px solid #e2e8f0;
    background: #f8fafc;
}

/* ===== 现代化表单系统 ===== */
.form-group-modern {
    margin-bottom: var(--space-4);
}

.form-label-modern {
    display: block;
    margin-bottom: var(--space-2);
    font-size: 13px;
    font-weight: 500;
    color: #374151;
}

.form-control-modern {
    width: 100%;
    padding: var(--space-3) var(--space-4);
    border: 1px solid #d1d5db;
    border-radius: var(--radius-md);
    font-size: 13px;
    line-height: 1.5;
    transition: var(--transition-fast);
    background: white;
}

.form-control-modern:focus {
    outline: none;
    border-color: var(--modern-primary);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-control-modern:disabled {
    background: #f9fafb;
    color: #9ca3af;
    cursor: not-allowed;
}

/* ===== 现代化表格系统 ===== */
.table-modern {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.table-modern th {
    background: #f8fafc;
    padding: var(--space-3) var(--space-4);
    text-align: left;
    font-weight: 600;
    font-size: 12px;
    color: #374151;
    border-bottom: 1px solid #e2e8f0;
}

.table-modern td {
    padding: var(--space-3) var(--space-4);
    border-bottom: 1px solid #f3f4f6;
    font-size: 13px;
    color: #374151;
}

.table-modern tbody tr:hover {
    background: #f8fafc;
}

.table-modern tbody tr:last-child td {
    border-bottom: none;
}

/* ===== 现代化状态指示器 ===== */
.status-indicator-modern {
    display: inline-flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-1) var(--space-3);
    border-radius: var(--radius-full);
    font-size: 12px;
    font-weight: 500;
}

.status-success-modern {
    background: #dcfce7;
    color: #166534;
}

.status-warning-modern {
    background: #fef3c7;
    color: #92400e;
}

.status-danger-modern {
    background: #fee2e2;
    color: #991b1b;
}

.status-info-modern {
    background: #dbeafe;
    color: #1e40af;
}

.status-neutral-modern {
    background: #f3f4f6;
    color: #374151;
}

/* ===== 现代化加载动画 ===== */
.loading-modern {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #f3f4f6;
    border-radius: 50%;
    border-top-color: var(--modern-primary);
    animation: spin-modern 1s ease-in-out infinite;
}

@keyframes spin-modern {
    to {
        transform: rotate(360deg);
    }
}

.loading-overlay-modern {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    backdrop-filter: blur(4px);
}

.loading-content-modern {
    background: white;
    padding: var(--space-8);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    text-align: center;
    max-width: 300px;
}

/* ===== 现代化通知系统 ===== */
.notification-modern {
    position: fixed;
    top: var(--space-4);
    right: var(--space-4);
    max-width: 400px;
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
    z-index: 1000;
    transform: translateX(100%);
    transition: var(--transition-normal);
}

.notification-modern.show {
    transform: translateX(0);
}

.notification-header-modern {
    padding: var(--space-4);
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.notification-icon-modern {
    width: 24px;
    height: 24px;
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    color: white;
}

.notification-success-modern .notification-icon-modern {
    background: var(--modern-success);
}

.notification-warning-modern .notification-icon-modern {
    background: var(--modern-warning);
}

.notification-danger-modern .notification-icon-modern {
    background: var(--modern-danger);
}

.notification-info-modern .notification-icon-modern {
    background: var(--modern-info);
}

.notification-content-modern {
    flex: 1;
}

.notification-title-modern {
    font-weight: 600;
    font-size: 14px;
    color: #111827;
    margin-bottom: var(--space-1);
}

.notification-message-modern {
    font-size: 13px;
    color: #6b7280;
    line-height: 1.4;
}

.notification-close-modern {
    background: none;
    border: none;
    color: #9ca3af;
    cursor: pointer;
    padding: var(--space-1);
    border-radius: var(--radius-sm);
    transition: var(--transition-fast);
}

.notification-close-modern:hover {
    background: #f3f4f6;
    color: #374151;
}

/* ===== 现代化进度条 ===== */
.progress-modern {
    width: 100%;
    height: 8px;
    background: #e5e7eb;
    border-radius: var(--radius-full);
    overflow: hidden;
}

.progress-bar-modern {
    height: 100%;
    background: var(--gradient-primary);
    border-radius: var(--radius-full);
    transition: width var(--transition-normal);
    position: relative;
}

.progress-bar-modern::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.4),
        transparent
    );
    animation: shimmer-modern 2s infinite;
}

@keyframes shimmer-modern {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

/* ===== 响应式增强 ===== */
@media (max-width: 768px) {
    .btn-modern {
        padding: var(--space-2) var(--space-3);
        font-size: 12px;
    }
    
    .card-body-modern {
        padding: var(--space-4);
    }
    
    .notification-modern {
        top: var(--space-2);
        right: var(--space-2);
        left: var(--space-2);
        max-width: none;
    }
    
    .table-modern {
        font-size: 12px;
    }
    
    .table-modern th,
    .table-modern td {
        padding: var(--space-2) var(--space-3);
    }
}

/* ===== 可访问性增强 ===== */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.focus-visible:focus {
    outline: 2px solid var(--modern-primary);
    outline-offset: 2px;
}

/* ===== 深色模式支持 ===== */
@media (prefers-color-scheme: dark) {
    :root {
        --modern-bg: #1f2937;
        --modern-surface: #374151;
        --modern-text: #f9fafb;
        --modern-text-secondary: #d1d5db;
    }
    
    .card-modern {
        background: var(--modern-surface);
        color: var(--modern-text);
    }
    
    .form-control-modern {
        background: var(--modern-surface);
        border-color: #4b5563;
        color: var(--modern-text);
    }
    
    .table-modern {
        background: var(--modern-surface);
        color: var(--modern-text);
    }
    
    .table-modern th {
        background: var(--modern-bg);
    }
}
