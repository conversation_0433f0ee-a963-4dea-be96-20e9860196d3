2025-06-22 22:31:26,649 INFO: 应用启动 - PID: 8748 [in C:\StudentsCMSSP\app\__init__.py:868]
2025-06-22 22:34:13,809 INFO: 获取副表数据用于补全主表: weekly_menu_id=43 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:342]
2025-06-22 22:34:13,813 INFO: 副表数据: 日期=1, 餐次=早餐, 菜品=🏫 剁椒笋片, ID=389 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-22 22:34:13,814 INFO: 副表数据: 日期=1, 餐次=早餐, 菜品=🏫 爽口面条, ID=396 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-22 22:34:13,814 INFO: 副表数据: 日期=1, 餐次=早餐, 菜品=🏫 西红柿炒蛋, ID=401 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-22 22:34:13,830 INFO: 副表数据映射构建完成: 1 天, 3 个菜品 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:370]
2025-06-22 22:34:13,955 WARNING: 跳过无效日期: 1 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:380]
2025-06-22 22:34:13,956 INFO: 主表数据补全完成，准备保存: 总菜品数=51, 已补全=51, 未补全=0 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:419]
2025-06-22 22:34:13,970 INFO: 删除现有菜单食谱(主表): weekly_menu_id=43 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:428]
2025-06-22 22:34:13,988 INFO: 删除现有菜单食谱(副表): weekly_menu_id=43 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:437]
2025-06-22 22:34:13,989 WARNING: 跳过无效日期: 1 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:446]
2025-06-22 22:34:14,083 INFO: 保存周菜单成功(主表和副表): id=43 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:500]
2025-06-22 22:34:14,084 INFO: 菜单缓存已清理 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:20]
2025-06-22 22:34:21,212 INFO: 发布周菜单成功: id=43 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:537]
2025-06-22 22:34:21,268 INFO: 更新了日期 2025-06-23 的工作日志菜单 [in C:\StudentsCMSSP\app\services\menu_sync_service.py:150]
2025-06-22 22:34:21,279 INFO: 更新了日期 2025-06-24 的工作日志菜单 [in C:\StudentsCMSSP\app\services\menu_sync_service.py:150]
2025-06-22 22:34:21,302 INFO: 更新了日期 2025-06-25 的工作日志菜单 [in C:\StudentsCMSSP\app\services\menu_sync_service.py:150]
2025-06-22 22:34:21,324 INFO: 更新了日期 2025-06-26 的工作日志菜单 [in C:\StudentsCMSSP\app\services\menu_sync_service.py:150]
2025-06-22 22:34:21,341 INFO: 更新了日期 2025-06-27 的工作日志菜单 [in C:\StudentsCMSSP\app\services\menu_sync_service.py:150]
2025-06-22 22:34:21,376 INFO: 更新了日期 2025-06-28 的工作日志菜单 [in C:\StudentsCMSSP\app\services\menu_sync_service.py:150]
2025-06-22 22:34:21,400 INFO: 更新了日期 2025-06-29 的工作日志菜单 [in C:\StudentsCMSSP\app\services\menu_sync_service.py:150]
2025-06-22 22:35:45,323 WARNING: [安全监控] 2025-06-22 22:35:45 - 可疑请求 | IP: **************:43322 | 路径: /, 指标: 无User-Agent [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-22 22:36:59,474 WARNING: [安全监控] 2025-06-22 22:36:59 - 可疑请求 | IP: ************* | 路径: /, 指标: 无User-Agent [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-22 22:45:05,656 INFO: 生成固定二维码 - 用户: guest_demo, 学校: 海淀区中关村第一小学 (ID: 44) [in C:\StudentsCMSSP\app\routes\daily_management\inspection_qrcode.py:31]
2025-06-22 22:45:05,657 INFO: 员工上传URL: http://xiaoyuanst.com/daily-management/public/inspections/select-date/44/upload [in C:\StudentsCMSSP\app\routes\daily_management\inspection_qrcode.py:38]
2025-06-22 22:45:05,676 INFO: 成功生成二维码base64，数据长度: 1236 [in C:\StudentsCMSSP\app\utils\qrcode_helper.py:78]
2025-06-22 22:45:05,676 INFO: 管理员评分URL: http://xiaoyuanst.com/daily-management/public/inspections/select-date/44/rate [in C:\StudentsCMSSP\app\routes\daily_management\inspection_qrcode.py:46]
2025-06-22 22:45:05,693 INFO: 成功生成二维码base64，数据长度: 1084 [in C:\StudentsCMSSP\app\utils\qrcode_helper.py:78]
