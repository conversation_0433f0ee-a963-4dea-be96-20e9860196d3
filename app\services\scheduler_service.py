"""
定时任务调度服务
"""
import threading
import time
from datetime import datetime, time as dt_time
from app.services.expiry_automation_service import expiry_automation_service
import logging

logger = logging.getLogger(__name__)

class SchedulerService:
    """定时任务调度服务"""
    
    def __init__(self):
        self.running = False
        self.thread = None
        self.tasks = []
        
        # 添加默认任务
        self.add_daily_task(
            name="过期食材自动化处理",
            func=self.run_expiry_automation,
            time=dt_time(6, 0)  # 每天早上6点执行
        )
    
    def add_daily_task(self, name, func, time):
        """添加每日定时任务"""
        task = {
            'name': name,
            'func': func,
            'time': time,
            'last_run': None
        }
        self.tasks.append(task)
        logger.info(f"添加定时任务: {name}, 执行时间: {time}")
    
    def start(self):
        """启动调度器"""
        if self.running:
            logger.warning("调度器已经在运行")
            return
        
        self.running = True
        self.thread = threading.Thread(target=self._run_scheduler, daemon=True)
        self.thread.start()
        logger.info("定时任务调度器已启动")
    
    def stop(self):
        """停止调度器"""
        self.running = False
        if self.thread:
            self.thread.join()
        logger.info("定时任务调度器已停止")
    
    def _run_scheduler(self):
        """运行调度器主循环"""
        while self.running:
            try:
                current_time = datetime.now().time()
                current_date = datetime.now().date()
                
                for task in self.tasks:
                    # 检查是否到了执行时间
                    if self._should_run_task(task, current_time, current_date):
                        logger.info(f"执行定时任务: {task['name']}")
                        try:
                            task['func']()
                            task['last_run'] = current_date
                            logger.info(f"定时任务执行成功: {task['name']}")
                        except Exception as e:
                            logger.error(f"定时任务执行失败: {task['name']}, 错误: {str(e)}")
                
                # 每分钟检查一次
                time.sleep(60)
                
            except Exception as e:
                logger.error(f"调度器运行错误: {str(e)}")
                time.sleep(60)
    
    def _should_run_task(self, task, current_time, current_date):
        """判断任务是否应该执行"""
        # 检查时间是否匹配（允许1分钟的误差）
        task_time = task['time']
        time_diff = abs(
            (current_time.hour * 60 + current_time.minute) - 
            (task_time.hour * 60 + task_time.minute)
        )
        
        # 如果时间匹配且今天还没有执行过
        if time_diff <= 1 and task['last_run'] != current_date:
            return True
        
        return False
    
    def run_expiry_automation(self):
        """执行过期食材自动化处理"""
        try:
            result = expiry_automation_service.run_daily_automation()
            if result['success']:
                logger.info(f"过期食材自动化处理完成: {result}")
            else:
                logger.error(f"过期食材自动化处理失败: {result}")
        except Exception as e:
            logger.error(f"过期食材自动化处理异常: {str(e)}")
    
    def get_task_status(self):
        """获取任务状态"""
        status = []
        for task in self.tasks:
            status.append({
                'name': task['name'],
                'time': task['time'].strftime('%H:%M'),
                'last_run': task['last_run'].strftime('%Y-%m-%d') if task['last_run'] else '从未执行',
                'next_run': self._get_next_run_time(task)
            })
        return status
    
    def _get_next_run_time(self, task):
        """获取下次执行时间"""
        from datetime import datetime, timedelta
        
        now = datetime.now()
        today = now.date()
        task_datetime = datetime.combine(today, task['time'])
        
        # 如果今天的执行时间已过，则下次执行是明天
        if task_datetime <= now:
            task_datetime += timedelta(days=1)
        
        return task_datetime.strftime('%Y-%m-%d %H:%M')

# 创建全局调度器实例
scheduler = SchedulerService()

def init_scheduler(app):
    """初始化调度器"""
    with app.app_context():
        try:
            scheduler.start()
            app.logger.info("定时任务调度器初始化成功")
        except Exception as e:
            app.logger.error(f"定时任务调度器初始化失败: {str(e)}")

def get_scheduler():
    """获取调度器实例"""
    return scheduler
