<!DOCTYPE html>
<html>
<head>
    <title>出库单</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style nonce="{{ csp_nonce }}">
        /* 打印样式 */
        @page {
            size: 210mm 297mm; /* A4纸张精确尺寸 */
            margin: 1.5cm;
        }
        /* 确保打印时背景色显示 */
        * {
            -webkit-print-color-adjust: exact !important;
            color-adjust: exact !important;
        }
        html, body {
            width: 210mm; /* A4宽度 */
            height: 297mm; /* A4高度 */
            margin: 0 auto;
            padding: 0;
            font-family: "Microsoft YaHei", "微软雅黑", SimSun, "宋体", sans-serif;
            font-size: 11pt;
            line-height: 1.6;
            background-color: white;
            color: #333;
        }

        .container {
            width: 180mm; /* A4宽度减去页边距 */
            margin: 0 auto;
            padding: 15px 0;
            box-sizing: border-box;
        }

        /* 页眉 */
        .header {
            text-align: center;
            margin-bottom: 20px;
            page-break-inside: avoid;
        }

        .header h2 {
            font-size: 18pt;
            font-weight: bold;
            margin-bottom: 8px;
            color: #2c3e50;
        }

        .header p {
            font-size: 12pt;
            color: #666;
            margin: 4px 0;
        }

        /* 信息表格 */
        .info-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            border: 2px solid #2c3e50;
        }

        .info-table th,
        .info-table td {
            border: 1px solid #2c3e50;
            padding: 8px;
            vertical-align: top;
            font-size: 10pt;
        }

        .info-table th {
            background-color: white;
            color: #000;
            font-weight: bold;
            text-align: center;
            width: 15%;
            border: 1px solid #000;
        }

        /* 明细表格 */
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            border: 2px solid #2c3e50;
        }

        .items-table th,
        .items-table td {
            border: 1px solid #2c3e50;
            padding: 6px;
            text-align: center;
            vertical-align: middle;
            font-size: 10pt;
        }

        .items-table th {
            background-color: white;
            color: #000;
            font-weight: bold;
            border: 1px solid #000;
        }

        .items-table td {
            background-color: white;
        }

        /* 签名区域 */
        .footer {
            display: flex;
            justify-content: space-between;
            margin-top: 30px;
            page-break-inside: avoid;
        }

        .signature {
            width: 45%;
            text-align: left;
        }

        .signature p {
            margin: 8px 0;
            font-size: 10pt;
            line-height: 1.8;
        }

        @media print {
            html, body {
                width: 210mm;
                height: 297mm;
            }
            .no-print {
                display: none !important;
            }
            .container {
                width: 100%;
                padding: 0;
                box-shadow: none;
            }
            /* 允许表格跨页 */
            table { page-break-inside: auto; }
            tr { page-break-inside: avoid; }
            /* 避免页面元素被分割 */
            .header, .footer { page-break-inside: avoid; }

            /* 表头在每页重复显示 */
            thead { display: table-header-group; }
            tfoot { display: table-footer-group; }

            /* 分页后保持表头样式 */
            .items-table thead tr th {
                background-color: white !important;
                color: #000 !important;
                border: 1px solid #000 !important;
            }
            .info-table thead tr th {
                background-color: white !important;
                color: #000 !important;
                border: 1px solid #000 !important;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h2>出库单</h2>
            <p>单号：{{ stock_out.stock_out_number }}</p>
        </div>

        <table class="info-table">
            <tr>
                <th>领用部门</th>
                <td>{{ stock_out.department or '-' }}</td>
                <th>出库日期</th>
                <td>{{ stock_out.stock_out_date.strftime('%Y-%m-%d %H:%M') if stock_out.stock_out_date else '-' }}</td>
                <th>出库类型</th>
                <td>{{ stock_out.stock_out_type }}</td>
            </tr>
            <tr>
                <th>操作人</th>
                <td>{{ stock_out.operator.real_name or stock_out.operator.username if stock_out.operator else '-' }}</td>
                <th>状态</th>
                <td>{{ stock_out.status }}</td>
                <th>创建时间</th>
                <td>{{ stock_out.created_at.strftime('%Y-%m-%d %H:%M') if stock_out.created_at else '-' }}</td>
            </tr>
            {% if consumption_plan and menu_plan %}
            <tr>
                <th>关联消耗计划</th>
                <td colspan="5">{{ consumption_plan.id }} ({{ menu_plan.plan_date.strftime('%Y-%m-%d') if menu_plan.plan_date else '' }} {{ menu_plan.meal_type }})</td>
            </tr>
            {% endif %}
            <tr>
                <th>备注</th>
                <td colspan="5">{{ stock_out.notes or '-' }}</td>
            </tr>
        </table>
    
    <table class="items-table">
        <thead>
            <tr>
                <th>序号</th>
                <th>食材名称</th>
                <th>批次号</th>
                <th>出库数量</th>
                <th>单位</th>
            </tr>
        </thead>
        <tbody>
            {% for item in stock_out_items %}
            <tr>
                <td>{{ loop.index }}</td>
                <td>{{ item.ingredient.name }}</td>
                <td>{{ item.batch_number }}</td>
                <td>{{ item.quantity }}</td>
                <td>{{ item.unit }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>

        <div class="footer">
            <div class="signature">
                <p>出库人：{{ stock_out.operator.real_name or stock_out.operator.username if stock_out.operator else '________________' }}</p>
                <p>签名：________________</p>
            </div>
            <div class="signature">
                <p>领用人：________________</p>
                <p>签名：________________</p>
            </div>
        </div>

        <div class="footer">
            <div class="signature">
                <p>库存管理员：________________</p>
                <p>签名：________________</p>
            </div>
            <div class="signature">
                <p>打印时间：{{ current_time.strftime('%Y-%m-%d %H:%M:%S') if current_time else '' }}</p>
            </div>
        </div>

        <div class="no-print" style="text-align: center; margin-top: 20px;">
            <button class="print-button">打印</button>
            <button data-onclick="window.close()">关闭</button>
        </div>
    </div>

    <script nonce="{{ csp_nonce }}">
        // 页面加载完成后自动聚焦
        window.onload = function() {
            window.focus();
        };
    </script>

<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/comprehensive-event-handler.js') }}"></script>
</body>
</html>
