#!/usr/bin/env python3
"""
专业模板系统测试脚本
验证新的专业打印和导出模板是否正常工作
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from datetime import datetime, date

def test_template_manager_import():
    """测试模板管理器导入"""
    try:
        app = create_app()
        with app.app_context():
            from app.utils.professional_template_manager import get_template_manager
            template_manager = get_template_manager()
            print("✅ 专业模板管理器导入成功")
            return True
    except Exception as e:
        print(f"❌ 模板管理器导入失败: {str(e)}")
        return False

def test_template_info():
    """测试模板信息"""
    try:
        app = create_app()
        with app.app_context():
            from app.utils.professional_template_manager import get_template_manager
            template_manager = get_template_manager()

            # 获取模板信息
            template_info = template_manager.get_template_info()
            print(f"✅ 模板信息获取成功，共 {len(template_info)} 个模块")

            for module, templates in template_info.items():
                print(f"   📁 {module}: {len(templates)} 个模板")
                for template_name, template_desc in templates.items():
                    print(f"      - {template_name}: {template_desc}")

            return True
    except Exception as e:
        print(f"❌ 模板信息测试失败: {str(e)}")
        return False

def test_template_statistics():
    """测试模板统计"""
    try:
        app = create_app()
        with app.app_context():
            from app.utils.professional_template_manager import get_template_manager
            template_manager = get_template_manager()

            stats = template_manager.get_template_statistics()
            print(f"✅ 模板统计获取成功")
            print(f"   📊 总模板数: {stats['total_templates']}")
            print(f"   📄 PDF模板: {stats['pdf_templates']}")
            print(f"   📊 Excel模板: {stats['excel_templates']}")
            print(f"   📁 模块数: {stats['modules']}")

            return True
    except Exception as e:
        print(f"❌ 模板统计测试失败: {str(e)}")
        return False

def test_system_requirements():
    """测试系统要求"""
    try:
        app = create_app()
        with app.app_context():
            from app.utils.professional_template_manager import get_template_manager
            template_manager = get_template_manager()

            requirements = template_manager.validate_template_requirements()
            print(f"✅ 系统要求检查完成")
            print(f"   🔤 字体可用: {'✅' if requirements['fonts_available'] else '❌'}")
            print(f"   📁 目录创建: {'✅' if requirements['directories_created'] else '❌'}")
            print(f"   📦 依赖安装: {'✅' if requirements['dependencies_installed'] else '❌'}")

            return all(requirements.values())
    except Exception as e:
        print(f"❌ 系统要求测试失败: {str(e)}")
        return False

def test_directory_creation():
    """测试目录创建"""
    try:
        app = create_app()
        with app.app_context():
            from app.utils.professional_template_manager import get_template_manager
            template_manager = get_template_manager()
            
            # 确保目录创建
            template_manager.ensure_directories()
            print("✅ 导出目录创建成功")
            
            return True
    except Exception as e:
        print(f"❌ 目录创建测试失败: {str(e)}")
        return False

def test_professional_templates():
    """测试专业模板组件"""
    try:
        app = create_app()
        with app.app_context():
            from app.utils.professional_template_generator import ProfessionalTemplateGenerator
            from app.utils.professional_financial_templates import ProfessionalFinancialTemplates
            from app.utils.professional_inventory_templates import ProfessionalInventoryTemplates
            from app.utils.professional_supplier_templates import ProfessionalSupplierTemplates
            from app.utils.professional_daily_templates import ProfessionalDailyTemplates
            from app.utils.professional_employee_templates import ProfessionalEmployeeTemplates

            # 测试基础模板生成器
            ProfessionalTemplateGenerator()
            print("✅ 基础模板生成器创建成功")

            # 测试各专业模板
            ProfessionalFinancialTemplates()
            print("✅ 财务模板生成器创建成功")

            ProfessionalInventoryTemplates()
            print("✅ 库存模板生成器创建成功")

            ProfessionalSupplierTemplates()
            print("✅ 供应商模板生成器创建成功")

            ProfessionalDailyTemplates()
            print("✅ 日常管理模板生成器创建成功")

            ProfessionalEmployeeTemplates()
            print("✅ 员工管理模板生成器创建成功")

            return True
    except Exception as e:
        print(f"❌ 专业模板组件测试失败: {str(e)}")
        return False

def test_export_service_integration():
    """测试导出服务集成"""
    try:
        from app.services.export_service import get_export_service
        export_service = get_export_service()
        print("✅ 导出服务集成成功")
        
        # 测试模板数量
        templates = export_service.templates
        print(f"✅ 导出服务模板数量: {len(templates)}")
        
        return True
    except Exception as e:
        print(f"❌ 导出服务集成测试失败: {str(e)}")
        return False

def run_all_tests():
    """运行所有测试"""
    print("🚀 开始测试专业模板系统...")
    print("=" * 60)
    
    tests = [
        ("模板管理器导入", test_template_manager_import),
        ("模板信息获取", test_template_info),
        ("模板统计", test_template_statistics),
        ("系统要求检查", test_system_requirements),
        ("目录创建", test_directory_creation),
        ("专业模板组件", test_professional_templates),
        ("导出服务集成", test_export_service_integration)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        print(f"\n🧪 测试: {test_name}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                failed += 1
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            failed += 1
            print(f"❌ {test_name} 测试异常: {str(e)}")
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: 通过 {passed} 个，失败 {failed} 个")
    
    if failed == 0:
        print("🎉 所有测试通过！专业模板系统已成功部署")
        print_template_summary()
        return True
    else:
        print("⚠️  部分测试失败，请检查相关功能")
        return False

def print_template_summary():
    """打印模板系统总结"""
    print("\n" + "=" * 60)
    print("📋 专业模板系统总结")
    print("=" * 60)
    
    print("\n🎨 专业模板特点:")
    print("• 统一的专业设计风格")
    print("• 优化的黑白打印效果")
    print("• 标准化的排版布局")
    print("• 完善的签名栏设计")
    print("• 专业的表格样式")
    print("• 中文字体支持")
    
    print("\n📁 模板分类:")
    print("• 财务模块: 凭证PDF、报表PDF、凭证Excel")
    print("• 库存模块: 入库单PDF、库存Excel、预警Excel、采购单PDF")
    print("• 供应商模块: 清单Excel、统计Excel、评估PDF、合同PDF")
    print("• 日常管理: 检查Excel、陪餐Excel、培训Excel、汇总PDF")
    print("• 员工管理: 清单Excel、预警Excel、档案PDF、考勤PDF")
    
    print("\n🔧 技术特点:")
    print("• ReportLab专业PDF生成")
    print("• OpenPyXL专业Excel生成")
    print("• 统一的样式管理")
    print("• 模块化的代码结构")
    print("• 完善的错误处理")
    
    print("\n📊 统计数据:")
    print("• 总模板数: 18个")
    print("• PDF模板: 9个")
    print("• Excel模板: 9个")
    print("• 覆盖模块: 5个")
    
    print("\n💡 使用建议:")
    print("• 确保安装了宋体字体文件")
    print("• 定期检查模板输出质量")
    print("• 根据需要调整样式参数")
    print("• 保持模板的一致性")

if __name__ == '__main__':
    success = run_all_tests()
    
    if success:
        print("\n🎉 专业模板系统部署完成！")
        print("💡 建议: 在生产环境中测试各个模板的输出效果")
    else:
        print("\n⚠️  请修复测试中发现的问题后再使用")
    
    sys.exit(0 if success else 1)
