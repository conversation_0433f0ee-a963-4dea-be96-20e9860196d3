{% macro qrcode_card_widget(title, description, qrcode_base64, detail_url, icon="fas fa-qrcode", color_theme="primary", quick_url="") %}
<!--
二维码卡片组件
参数：
- title: 卡片标题
- description: 功能描述
- qrcode_base64: 二维码的base64数据
- detail_url: 查看详情的URL
- icon: 图标类名 (默认: fas fa-qrcode)
- color_theme: 颜色主题 (默认: primary)
- quick_url: 快速复制的URL (可选)
-->
<div class="card border-left-{{ color_theme }} shadow h-100">
    <div class="card-header bg-{{ color_theme }} text-white py-2">
        <h6 class="m-0 font-weight-bold">
            <i class="{{ icon }} mr-2"></i>{{ title }}
        </h6>
    </div>
    <div class="card-body p-3">
        <div class="row no-gutters">
            <!-- 二维码预览区 -->
            <div class="col-4 d-flex align-items-center justify-content-center">
                {% if qrcode_base64 %}
                <div class="qrcode-preview-container">
                    <img src="data:image/png;base64,{{ qrcode_base64 }}" 
                         alt="{{ title }}" 
                         class="qrcode-preview img-fluid border rounded cursor-pointer"
                         style="max-width: 80px; max-height: 80px;"
                         onclick="showQRCodeModal('{{ title }}', 'data:image/png;base64,{{ qrcode_base64 }}', '{{ detail_url }}')">
                </div>
                {% else %}
                <div class="text-center text-muted">
                    <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                    <small>二维码生成失败</small>
                </div>
                {% endif %}
            </div>
            
            <!-- 信息和操作区 -->
            <div class="col-8 pl-3">
                <div class="h-100 d-flex flex-column justify-content-between">
                    <!-- 描述信息 -->
                    <div class="mb-2">
                        <p class="text-sm text-gray-700 mb-1">{{ description }}</p>
                        {% if qrcode_base64 %}
                        <small class="text-muted">
                            <i class="fas fa-info-circle mr-1"></i>点击二维码可放大查看
                        </small>
                        {% endif %}
                    </div>
                    
                    <!-- 操作按钮 -->
                    <div class="btn-group-vertical btn-group-sm w-100">
                        {% if qrcode_base64 %}
                        <a href="{{ detail_url }}" class="btn btn-outline-{{ color_theme }} btn-sm mb-1">
                            <i class="fas fa-external-link-alt mr-1"></i>查看详情
                        </a>
                        {% if quick_url %}
                        <button class="btn btn-outline-secondary btn-sm" 
                                onclick="copyToClipboard('{{ quick_url }}', '{{ title }}链接已复制')">
                            <i class="fas fa-copy mr-1"></i>复制链接
                        </button>
                        {% endif %}
                        {% else %}
                        <button class="btn btn-outline-secondary btn-sm" disabled>
                            <i class="fas fa-exclamation-triangle mr-1"></i>暂不可用
                        </button>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endmacro %}

<!-- 二维码预览模态框 -->
<div class="modal fade" id="qrcodePreviewModal" tabindex="-1" role="dialog" aria-labelledby="qrcodePreviewModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="qrcodePreviewModalLabel">
                    <i class="fas fa-qrcode mr-2"></i><span id="qrcodeModalTitle">二维码预览</span>
                </h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body text-center">
                <div class="mb-3">
                    <img id="qrcodeModalImage" src="" alt="二维码" class="img-fluid border rounded" style="max-width: 300px;">
                </div>
                <div class="alert alert-info">
                    <i class="fas fa-mobile-alt mr-2"></i>
                    使用手机扫描此二维码即可快速访问功能
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">关闭</button>
                <a id="qrcodeModalDetailBtn" href="#" class="btn btn-primary">
                    <i class="fas fa-external-link-alt mr-1"></i>查看详情页面
                </a>
            </div>
        </div>
    </div>
</div>

<!-- 成功提示模态框 -->
<div class="modal fade" id="successToastModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-sm modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-body text-center py-4">
                <div class="text-success mb-2">
                    <i class="fas fa-check-circle fa-2x"></i>
                </div>
                <p id="successToastMessage" class="mb-0">操作成功</p>
            </div>
        </div>
    </div>
</div>

<style nonce="{{ csp_nonce }}">
/* 二维码卡片组件样式 */
.qrcode-preview {
    transition: transform 0.2s ease-in-out;
}

.qrcode-preview:hover {
    transform: scale(1.05);
}

.cursor-pointer {
    cursor: pointer;
}

.text-sm {
    font-size: 0.875rem;
}

.btn-group-vertical .btn {
    border-radius: 0.25rem !important;
}

.btn-group-vertical .btn + .btn {
    margin-top: 0.25rem;
    margin-left: 0;
}

/* 响应式优化 */
@media (max-width: 576px) {
    .qrcode-preview {
        max-width: 60px !important;
        max-height: 60px !important;
    }
    
    .btn-group-vertical .btn {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }
}
</style>

<script nonce="{{ csp_nonce }}">
// 显示二维码预览模态框
function showQRCodeModal(title, imageSrc, detailUrl) {
    document.getElementById('qrcodeModalTitle').textContent = title;
    document.getElementById('qrcodeModalImage').src = imageSrc;
    document.getElementById('qrcodeModalDetailBtn').href = detailUrl;
    $('#qrcodePreviewModal').modal('show');
}

// 复制到剪贴板功能
function copyToClipboard(text, successMessage) {
    if (navigator.clipboard && window.isSecureContext) {
        // 现代浏览器的异步API
        navigator.clipboard.writeText(text).then(function() {
            showSuccessToast(successMessage || '链接已复制到剪贴板');
        }).catch(function(err) {
            console.error('复制失败:', err);
            fallbackCopyTextToClipboard(text, successMessage);
        });
    } else {
        // 降级方案
        fallbackCopyTextToClipboard(text, successMessage);
    }
}

// 降级复制方案
function fallbackCopyTextToClipboard(text, successMessage) {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    textArea.style.position = 'fixed';
    textArea.style.left = '-999999px';
    textArea.style.top = '-999999px';
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    
    try {
        const successful = document.execCommand('copy');
        if (successful) {
            showSuccessToast(successMessage || '链接已复制到剪贴板');
        } else {
            alert('复制失败，请手动复制链接');
        }
    } catch (err) {
        console.error('降级复制也失败:', err);
        alert('复制失败，请手动复制链接');
    }
    
    document.body.removeChild(textArea);
}

// 显示成功提示
function showSuccessToast(message) {
    document.getElementById('successToastMessage').textContent = message;
    $('#successToastModal').modal('show');
    
    // 2秒后自动关闭
    setTimeout(function() {
        $('#successToastModal').modal('hide');
    }, 2000);
}
</script>
