#!/usr/bin/env python3
"""
简单的导出功能测试脚本
验证导出服务是否正常工作
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_export_service_import():
    """测试导出服务是否可以正常导入"""
    try:
        from app.services.export_service import UnifiedExportService
        print("✅ 导出服务导入成功")
        
        # 创建服务实例
        service = UnifiedExportService()
        print("✅ 导出服务实例创建成功")
        
        # 检查模板
        templates = service.templates
        print(f"✅ 模板加载成功，共 {len(templates)} 个模板")
        
        for name, template in templates.items():
            print(f"   - {name}: {template['title']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 导出服务测试失败: {str(e)}")
        return False

def test_template_structure():
    """测试模板结构"""
    try:
        from app.services.export_service import UnifiedExportService
        service = UnifiedExportService()
        
        required_templates = [
            'supplier_list',
            'inventory_list',
            'daily_inspection',
            'companion_dining',
            'training_records',
            'employee_list'
        ]
        
        for template_name in required_templates:
            if template_name in service.templates:
                template = service.templates[template_name]
                assert 'headers' in template
                assert 'columns' in template
                assert 'title' in template
                assert len(template['headers']) == len(template['columns'])
                print(f"✅ 模板 {template_name} 结构正确")
            else:
                print(f"❌ 模板 {template_name} 不存在")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 模板结构测试失败: {str(e)}")
        return False

def test_data_formatting():
    """测试数据格式化"""
    try:
        from app.services.export_service import UnifiedExportService
        service = UnifiedExportService()
        
        # 测试数据
        test_obj = {
            'name': '测试名称',
            'value': 123,
            'empty': None,
            'date': None
        }
        
        # 测试获取值
        assert service._get_nested_value(test_obj, 'name') == '测试名称'
        assert service._get_nested_value(test_obj, 'value') == '123'
        assert service._get_nested_value(test_obj, 'empty') == ''
        assert service._get_nested_value(test_obj, 'nonexistent') == ''
        
        print("✅ 数据格式化功能正常")
        return True
        
    except Exception as e:
        print(f"❌ 数据格式化测试失败: {str(e)}")
        return False

def test_export_directories():
    """测试导出目录"""
    try:
        # 检查目录是否存在
        directories = [
            'app/static/excel',
            'app/static/pdf',
            'app/static/export_templates'
        ]
        
        for directory in directories:
            if os.path.exists(directory):
                print(f"✅ 目录存在: {directory}")
            else:
                print(f"⚠️  目录不存在: {directory}")
                # 创建目录
                os.makedirs(directory, exist_ok=True)
                print(f"✅ 已创建目录: {directory}")
        
        return True
        
    except Exception as e:
        print(f"❌ 目录测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始简单导出功能测试...")
    print("=" * 50)
    
    tests = [
        ("导出服务导入", test_export_service_import),
        ("模板结构", test_template_structure),
        ("数据格式化", test_data_formatting),
        ("导出目录", test_export_directories)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        print(f"\n🧪 测试: {test_name}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                failed += 1
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            failed += 1
            print(f"❌ {test_name} 测试异常: {str(e)}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: 通过 {passed} 个，失败 {failed} 个")
    
    if failed == 0:
        print("🎉 所有测试通过！导出功能基础组件正常")
        print("\n📝 下一步:")
        print("1. 访问 http://localhost:8080")
        print("2. 登录系统")
        print("3. 进入供应商管理页面测试导出功能")
        print("4. 进入库存管理页面测试导出功能")
        print("5. 进入日常管理页面测试导出功能")
        
        print("\n🔍 测试导出功能的步骤:")
        print("- 供应商管理: 点击'导出' -> '导出Excel'")
        print("- 库存管理: 点击'导出' -> '导出库存清单'")
        print("- 日常管理: 点击'导出报表' -> 选择记录类型")
        
        return True
    else:
        print("⚠️  部分测试失败，请检查相关功能")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
