<!-- 导出功能前端模板改进方案 -->

<!-- 1. 供应商管理页面导出功能 -->
<!-- 在 app/templates/supplier/index.html 中修改 -->

<!-- 替换现有的导出按钮 -->
<div class="card-header d-flex justify-content-between align-items-center">
    <h3 class="card-title mb-0">供应商管理</h3>
    <div class="btn-group">
        <!-- 导出下拉菜单 -->
        <div class="dropdown">
            <button class="btn btn-success dropdown-toggle" type="button" id="exportDropdown" data-bs-toggle="dropdown">
                <i class="fas fa-download"></i> 导出
            </button>
            <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="#" onclick="exportSuppliers('excel')">
                    <i class="fas fa-file-excel text-success"></i> 导出Excel
                </a></li>
                <li><a class="dropdown-item" href="#" onclick="exportSupplierStatistics()">
                    <i class="fas fa-chart-bar text-info"></i> 采购统计报表
                </a></li>
            </ul>
        </div>
        <button class="btn btn-primary" onclick="refreshTable()">
            <i class="fas fa-sync-alt"></i> 刷新
        </button>
    </div>
</div>

<!-- 导出筛选模态框 -->
<div class="modal fade" id="exportModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">导出设置</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="exportForm">
                    <div class="mb-3">
                        <label class="form-label">导出格式</label>
                        <select class="form-select" id="exportFormat">
                            <option value="excel">Excel格式</option>
                            <option value="pdf">PDF格式</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">供应商状态</label>
                        <select class="form-select" id="statusFilter">
                            <option value="all">全部</option>
                            <option value="active">启用</option>
                            <option value="inactive">禁用</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="confirmExport()">确认导出</button>
            </div>
        </div>
    </div>
</div>

<!-- 2. 库存管理页面导出功能 -->
<!-- 在 app/templates/inventory/index.html 中添加 -->

<div class="card-header d-flex justify-content-between align-items-center">
    <h3 class="card-title mb-0">库存管理</h3>
    <div class="btn-group">
        <!-- 导出下拉菜单 -->
        <div class="dropdown">
            <button class="btn btn-success dropdown-toggle" type="button" data-bs-toggle="dropdown">
                <i class="fas fa-download"></i> 导出
            </button>
            <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="#" onclick="showExportModal('inventory')">
                    <i class="fas fa-file-excel text-success"></i> 导出库存清单
                </a></li>
                <li><a class="dropdown-item" href="#" onclick="exportExpiryReport()">
                    <i class="fas fa-exclamation-triangle text-warning"></i> 过期预警报表
                </a></li>
                <li><a class="dropdown-item" href="#" onclick="exportLowStockReport()">
                    <i class="fas fa-arrow-down text-danger"></i> 低库存报表
                </a></li>
            </ul>
        </div>
    </div>
</div>

<!-- 库存导出筛选模态框 -->
<div class="modal fade" id="inventoryExportModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">库存导出设置</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="inventoryExportForm">
                    <div class="mb-3">
                        <label class="form-label">仓库选择</label>
                        <select class="form-select" id="warehouseFilter">
                            <option value="">全部仓库</option>
                            {% for warehouse in warehouses %}
                            <option value="{{ warehouse.id }}">{{ warehouse.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="lowStockOnly">
                            <label class="form-check-label" for="lowStockOnly">
                                仅导出低库存商品
                            </label>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="expiredOnly">
                            <label class="form-check-label" for="expiredOnly">
                                仅导出过期商品
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="confirmInventoryExport()">确认导出</button>
            </div>
        </div>
    </div>
</div>

<!-- 3. 日常管理页面导出功能 -->
<!-- 在 app/templates/daily_management/index.html 中添加 -->

<div class="card-header d-flex justify-content-between align-items-center">
    <h3 class="card-title mb-0">日常管理</h3>
    <div class="btn-group">
        <!-- 导出下拉菜单 -->
        <div class="dropdown">
            <button class="btn btn-success dropdown-toggle" type="button" data-bs-toggle="dropdown">
                <i class="fas fa-download"></i> 导出报表
            </button>
            <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="#" onclick="showDailyExportModal('inspection')">
                    <i class="fas fa-clipboard-check text-primary"></i> 检查记录
                </a></li>
                <li><a class="dropdown-item" href="#" onclick="showDailyExportModal('companion')">
                    <i class="fas fa-utensils text-success"></i> 陪餐记录
                </a></li>
                <li><a class="dropdown-item" href="#" onclick="showDailyExportModal('training')">
                    <i class="fas fa-graduation-cap text-info"></i> 培训记录
                </a></li>
                <li><a class="dropdown-item" href="#" onclick="showDailyExportModal('issues')">
                    <i class="fas fa-exclamation-circle text-warning"></i> 问题记录
                </a></li>
            </ul>
        </div>
    </div>
</div>

<!-- 日常管理导出模态框 -->
<div class="modal fade" id="dailyExportModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="dailyExportTitle">导出设置</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="dailyExportForm">
                    <div class="row">
                        <div class="col-md-6">
                            <label class="form-label">开始日期</label>
                            <input type="date" class="form-control" id="startDate" 
                                   value="{{ (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d') }}">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">结束日期</label>
                            <input type="date" class="form-control" id="endDate" 
                                   value="{{ datetime.now().strftime('%Y-%m-%d') }}">
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="confirmDailyExport()">确认导出</button>
            </div>
        </div>
    </div>
</div>

<!-- 4. 通用导出进度提示 -->
<div class="modal fade" id="exportProgressModal" tabindex="-1" data-bs-backdrop="static">
    <div class="modal-dialog modal-sm">
        <div class="modal-content">
            <div class="modal-body text-center">
                <div class="spinner-border text-primary mb-3" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mb-0">正在生成导出文件...</p>
                <small class="text-muted">请稍候，不要关闭页面</small>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript代码 -->
<script>
// 全局导出变量
let currentExportType = '';

// 供应商导出功能
function exportSuppliers(format = 'excel') {
    showExportProgress();
    
    const params = new URLSearchParams({
        format: format,
        status: document.getElementById('statusFilter')?.value || 'all'
    });
    
    // 创建隐藏的下载链接
    const link = document.createElement('a');
    link.href = `/supplier/export?${params.toString()}`;
    link.style.display = 'none';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    // 延迟隐藏进度提示
    setTimeout(() => {
        hideExportProgress();
    }, 2000);
}

function exportSupplierStatistics() {
    showExportProgress();
    
    const link = document.createElement('a');
    link.href = '/supplier/export/statistics';
    link.style.display = 'none';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    setTimeout(() => {
        hideExportProgress();
    }, 2000);
}

// 库存导出功能
function showExportModal(type) {
    currentExportType = type;
    const modal = new bootstrap.Modal(document.getElementById('inventoryExportModal'));
    modal.show();
}

function confirmInventoryExport() {
    showExportProgress();
    
    const params = new URLSearchParams({
        format: 'excel',
        warehouse_id: document.getElementById('warehouseFilter')?.value || '',
        low_stock_only: document.getElementById('lowStockOnly')?.checked || false,
        expired_only: document.getElementById('expiredOnly')?.checked || false
    });
    
    const link = document.createElement('a');
    link.href = `/inventory/export?${params.toString()}`;
    link.style.display = 'none';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    // 隐藏模态框
    bootstrap.Modal.getInstance(document.getElementById('inventoryExportModal')).hide();
    
    setTimeout(() => {
        hideExportProgress();
    }, 2000);
}

function exportExpiryReport() {
    showExportProgress();
    
    const link = document.createElement('a');
    link.href = '/inventory/export/expiry_report';
    link.style.display = 'none';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    setTimeout(() => {
        hideExportProgress();
    }, 2000);
}

// 日常管理导出功能
function showDailyExportModal(exportType) {
    currentExportType = exportType;
    
    const titles = {
        'inspection': '导出检查记录',
        'companion': '导出陪餐记录',
        'training': '导出培训记录',
        'issues': '导出问题记录'
    };
    
    document.getElementById('dailyExportTitle').textContent = titles[exportType] || '导出设置';
    
    const modal = new bootstrap.Modal(document.getElementById('dailyExportModal'));
    modal.show();
}

function confirmDailyExport() {
    showExportProgress();
    
    const startDate = document.getElementById('startDate').value;
    const endDate = document.getElementById('endDate').value;
    
    const params = new URLSearchParams({
        start_date: startDate,
        end_date: endDate
    });
    
    const link = document.createElement('a');
    link.href = `/daily-management/export/${currentExportType}?${params.toString()}`;
    link.style.display = 'none';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    // 隐藏模态框
    bootstrap.Modal.getInstance(document.getElementById('dailyExportModal')).hide();
    
    setTimeout(() => {
        hideExportProgress();
    }, 2000);
}

// 通用进度提示功能
function showExportProgress() {
    const modal = new bootstrap.Modal(document.getElementById('exportProgressModal'));
    modal.show();
}

function hideExportProgress() {
    const modal = bootstrap.Modal.getInstance(document.getElementById('exportProgressModal'));
    if (modal) {
        modal.hide();
    }
}

// 错误处理
function handleExportError(message) {
    hideExportProgress();
    toastr.error(message || '导出失败，请重试');
}

// 成功提示
function handleExportSuccess(message) {
    hideExportProgress();
    toastr.success(message || '导出成功');
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 设置默认日期
    const today = new Date();
    const thirtyDaysAgo = new Date(today.getTime() - (30 * 24 * 60 * 60 * 1000));
    
    if (document.getElementById('startDate')) {
        document.getElementById('startDate').value = thirtyDaysAgo.toISOString().split('T')[0];
    }
    if (document.getElementById('endDate')) {
        document.getElementById('endDate').value = today.toISOString().split('T')[0];
    }
});
</script>

<!-- CSS样式 -->
<style>
.export-btn-group {
    display: flex;
    gap: 0.5rem;
}

.export-dropdown .dropdown-item {
    padding: 0.5rem 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.export-dropdown .dropdown-item:hover {
    background-color: #f8f9fa;
}

.export-progress {
    text-align: center;
    padding: 2rem;
}

.export-progress .spinner-border {
    width: 3rem;
    height: 3rem;
}

#exportProgressModal .modal-content {
    border: none;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.export-form-group {
    margin-bottom: 1rem;
}

.export-checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}
</style>
