"""
过期食材自动化处理命令
"""
import click
from flask.cli import with_appcontext
from app.services.expiry_automation_service import expiry_automation_service
import logging

logger = logging.getLogger(__name__)

@click.command()
@with_appcontext
def run_expiry_automation():
    """运行过期食材自动化处理"""
    click.echo("开始执行过期食材自动化处理...")
    
    try:
        result = expiry_automation_service.run_daily_automation()
        
        if result['success']:
            click.echo(f"✅ 自动化处理完成:")
            click.echo(f"   - 过期状态更新: {result['expired_count']} 条")
            click.echo(f"   - 生成通知: {result['notification_count']} 条")
            click.echo(f"   - 损失计算: {result['loss_calculation_count']} 条")
            click.echo(f"   - 清理通知: {result['cleaned_count']} 条")
        else:
            click.echo(f"❌ 自动化处理失败: {result['error']}")
            
    except Exception as e:
        click.echo(f"❌ 执行过程中发生错误: {str(e)}")
        logger.error(f"过期食材自动化处理命令执行失败: {str(e)}")

@click.command()
@click.option('--days', default=7, help='预警天数')
@with_appcontext
def generate_expiry_warnings(days):
    """生成过期预警通知"""
    click.echo(f"生成 {days} 天内的过期预警通知...")
    
    try:
        count = expiry_automation_service.generate_expiry_notifications()
        click.echo(f"✅ 生成了 {count} 条预警通知")
        
    except Exception as e:
        click.echo(f"❌ 生成预警通知失败: {str(e)}")
        logger.error(f"生成过期预警通知失败: {str(e)}")

@click.command()
@with_appcontext
def update_expired_status():
    """更新过期库存状态"""
    click.echo("更新过期库存状态...")
    
    try:
        count = expiry_automation_service.auto_update_expired_status()
        click.echo(f"✅ 更新了 {count} 条过期库存状态")
        
    except Exception as e:
        click.echo(f"❌ 更新过期状态失败: {str(e)}")
        logger.error(f"更新过期状态失败: {str(e)}")

@click.command()
@with_appcontext
def calculate_losses():
    """计算损失金额"""
    click.echo("计算处理记录的损失金额...")
    
    try:
        count = expiry_automation_service.auto_calculate_losses()
        click.echo(f"✅ 计算了 {count} 条记录的损失金额")
        
    except Exception as e:
        click.echo(f"❌ 计算损失金额失败: {str(e)}")
        logger.error(f"计算损失金额失败: {str(e)}")

def register_expiry_commands(app):
    """注册过期处理相关命令"""
    app.cli.add_command(run_expiry_automation)
    app.cli.add_command(generate_expiry_warnings)
    app.cli.add_command(update_expired_status)
    app.cli.add_command(calculate_losses)
