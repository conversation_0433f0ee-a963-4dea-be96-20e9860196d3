from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify, current_app
from flask_login import login_required, current_user
from app import db
from app.models import Supplier, SupplierCategory, SupplierSchoolRelation, AdministrativeArea
from app.forms.supplier import SupplierForm
from app.utils.log_activity import log_activity
from datetime import datetime
from sqlalchemy import text, func

supplier_bp = Blueprint('supplier', __name__)

@supplier_bp.route('/')
@login_required
def index():
    """供应商列表页面"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 10, type=int)
    category_id = request.args.get('category_id', type=int)
    keyword = request.args.get('keyword', '')

    # 基本查询
    query = Supplier.query

    if category_id:
        query = query.filter_by(category_id=category_id)

    if keyword:
        query = query.filter(Supplier.name.like(f'%{keyword}%'))

    # 根据用户区域权限筛选供应商
    if not current_user.is_admin():
        # 获取用户可访问的区域ID列表
        accessible_area_ids = [area.id for area in current_user.get_accessible_areas()]

        # 通过供应商-学校关联表筛选供应商
        query = query.join(SupplierSchoolRelation, Supplier.id == SupplierSchoolRelation.supplier_id)\
                    .filter(SupplierSchoolRelation.area_id.in_(accessible_area_ids))\
                    .filter(SupplierSchoolRelation.status == 1)\
                    .distinct()

    pagination = query.order_by(Supplier.id.desc()).paginate(page=page, per_page=per_page)
    suppliers = pagination.items

    # 为每个供应商添加权限过滤后的学校关系
    if current_user.is_admin():
        # 管理员可以看到所有学校关系
        for supplier in suppliers:
            supplier.accessible_relations = supplier.school_relations
    else:
        # 普通用户只能看到自己区域内的学校关系
        accessible_area_ids = [area.id for area in current_user.get_accessible_areas()]
        for supplier in suppliers:
            supplier.accessible_relations = [
                relation for relation in supplier.school_relations
                if relation.area_id in accessible_area_ids
            ]

    categories = SupplierCategory.query.all()

    # 获取当前用户的区域信息
    current_area = current_user.get_current_area()
    area_path = []
    if current_area:
        area_path = [current_area]
        ancestors = current_area.get_ancestors()
        area_path = ancestors + area_path

    return render_template('supplier/index.html',
                          suppliers=suppliers,
                          pagination=pagination,
                          categories=categories,
                          category_id=category_id,
                          keyword=keyword,
                          current_area=current_area,
                          area_path=area_path,
                          now=datetime.now())

@supplier_bp.route('/create', methods=['GET', 'POST'])
@login_required
def create():
    """添加供应商"""
    form = SupplierForm()

    # 获取供应商分类选项
    categories = SupplierCategory.query.all()
    form.category_id.choices = [(0, '-- 请选择分类 --')] + [(c.id, c.name) for c in categories]

    # 获取学校选项，根据用户区域权限筛选
    if current_user.is_admin():
        # 系统管理员可以看到所有学校
        schools = AdministrativeArea.query.filter_by(level=3).all()  # 假设level=3表示学校级别
    else:
        # 普通用户只能看到自己区域内的学校
        accessible_areas = current_user.get_accessible_areas()
        schools = [area for area in accessible_areas if area.level == 3]  # 只保留学校级别的区域

    # 构建学校选项列表
    form.area_id.choices = [(0, '-- 请选择学校 --')] + [(s.id, s.name) for s in schools]

    if form.validate_on_submit():
        # 检查是否已存在相同的供应商-学校关系
        existing = SupplierSchoolRelation.query.join(Supplier).filter(
            Supplier.name == form.name.data,
            SupplierSchoolRelation.area_id == form.area_id.data,
            SupplierSchoolRelation.status == 1  # 有效状态
        ).first()

        if existing:
            flash('该供应商与学校已存在有效的合作关系！', 'danger')
            return render_template('supplier/form.html', form=form, title='添加供应商', now=datetime.now())

        try:
            # 使用原始SQL创建供应商，避免SQLAlchemy的时间戳处理问题
            # 根据最佳实践，明确提供时间值，精确到0.1秒，去除微秒
            current_time = datetime.now().replace(microsecond=0)

            supplier_sql = text("""
                INSERT INTO suppliers
                (name, category_id, contact_person, phone, email, address,
                 business_license, tax_id, bank_name, bank_account, rating, status,
                 created_at, updated_at)
                OUTPUT inserted.id
                VALUES
                (:name, :category_id, :contact_person, :phone, :email, :address,
                 :business_license, :tax_id, :bank_name, :bank_account, :rating, :status,
                 :created_at, :updated_at)
            """)

            supplier_params = {
                'name': form.name.data,
                'category_id': form.category_id.data if form.category_id.data != 0 else None,
                'contact_person': form.contact_person.data,
                'phone': form.phone.data,
                'email': form.email.data or None,
                'address': form.address.data,
                'business_license': form.business_license.data,
                'tax_id': form.tax_id.data or None,
                'bank_name': form.bank_name.data or None,
                'bank_account': form.bank_account.data or None,
                'rating': form.rating.data,
                'status': form.status.data,
                'created_at': current_time,
                'updated_at': current_time
            }

            result = db.session.execute(supplier_sql, supplier_params)
            supplier_id = result.fetchone()[0]

            # 生成合同编号（如果为空）
            contract_number = form.contract_number.data
            if not contract_number:
                now = datetime.now()
                contract_number = f"C{now.strftime('%Y%m%d')}{str(supplier_id).zfill(4)}"

            # 使用原始SQL创建供应商-学校关系
            # 根据最佳实践，明确提供时间值，精确到0.1秒，去除微秒
            relation_sql = text("""
                INSERT INTO supplier_school_relations
                (supplier_id, area_id, contract_number, start_date, end_date, status, notes,
                 created_at, updated_at)
                OUTPUT inserted.id
                VALUES
                (:supplier_id, :area_id, :contract_number, :start_date, :end_date, :status, :notes,
                 :created_at, :updated_at)
            """)

            # 处理日期格式，避免ODBC驱动程序问题
            start_date_str = form.start_date.data.strftime('%Y-%m-%d') if form.start_date.data else None
            end_date_str = form.end_date.data.strftime('%Y-%m-%d') if form.end_date.data else None

            relation_params = {
                'supplier_id': supplier_id,
                'area_id': form.area_id.data,
                'contract_number': contract_number,
                'start_date': start_date_str,
                'end_date': end_date_str,
                'status': form.relation_status.data,
                'notes': form.notes.data or None,
                'created_at': current_time,
                'updated_at': current_time
            }

            relation_result = db.session.execute(relation_sql, relation_params)
            relation_id = relation_result.fetchone()[0]

            # 添加审计日志
            log_activity(
                action='create',
                resource_type='Supplier',
                resource_id=supplier_id,
                details={
                    'supplier_name': form.name.data,
                    'contact_person': form.contact_person.data,
                    'phone': form.phone.data,
                    'school_id': form.area_id.data,
                    'contract_number': contract_number
                }
            )

            db.session.commit()
            flash('供应商添加成功，学校合作关系已建立！', 'success')
            return redirect(url_for('supplier.index'))

        except Exception as e:
            db.session.rollback()
            flash(f'添加供应商失败: {str(e)}', 'danger')
            return render_template('supplier/form.html', form=form, title='添加供应商', now=datetime.now())

    return render_template('supplier/form.html', form=form, title='添加供应商', now=datetime.now())

@supplier_bp.route('/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit(id):
    """编辑供应商"""
    supplier = Supplier.query.get_or_404(id)

    # 创建表单并预填充数据，传入edit_mode=True表示编辑模式
    form = SupplierForm(obj=supplier, edit_mode=True)

    # 获取供应商分类选项
    categories = SupplierCategory.query.all()
    form.category_id.choices = [(0, '-- 请选择分类 --')] + [(c.id, c.name) for c in categories]

    # 为学校关系字段设置空的choices，避免验证错误
    # 在编辑模式下，这些字段不会显示，但需要设置choices以避免验证错误
    form.area_id.choices = [(0, '-- 请选择学校 --')]
    form.relation_status.choices = [(1, '有效'), (0, '已终止')]

    # 获取当前用户有权限查看的学校关系
    accessible_relations = []
    if current_user.is_admin():
        # 系统管理员可以看到所有学校关系
        accessible_relations = supplier.school_relations
    else:
        # 普通用户只能看到自己区域内的学校关系
        accessible_area_ids = [area.id for area in current_user.get_accessible_areas()]
        accessible_relations = [
            relation for relation in supplier.school_relations
            if relation.area_id in accessible_area_ids
        ]

    if form.validate_on_submit():
        old_data = {
            'name': supplier.name,
            'category_id': supplier.category_id,
            'contact_person': supplier.contact_person,
            'phone': supplier.phone,
            'email': supplier.email,
            'address': supplier.address,
            'business_license': supplier.business_license,
            'tax_id': supplier.tax_id,
            'bank_name': supplier.bank_name,
            'bank_account': supplier.bank_account,
            'rating': supplier.rating,
            'status': supplier.status
        }

        supplier.name = form.name.data
        supplier.category_id = form.category_id.data if form.category_id.data != 0 else None
        supplier.contact_person = form.contact_person.data
        supplier.phone = form.phone.data
        supplier.email = form.email.data
        supplier.address = form.address.data
        supplier.business_license = form.business_license.data
        supplier.tax_id = form.tax_id.data
        supplier.bank_name = form.bank_name.data
        supplier.bank_account = form.bank_account.data
        supplier.rating = form.rating.data
        supplier.status = form.status.data

        # 添加审计日志
        log_activity(
            action='update',
            resource_type='Supplier',
            resource_id=supplier.id,
            details={
                'old': old_data,
                'new': {
                    'name': supplier.name,
                    'category_id': supplier.category_id,
                    'contact_person': supplier.contact_person,
                    'phone': supplier.phone,
                    'email': supplier.email,
                    'address': supplier.address,
                    'business_license': supplier.business_license,
                    'tax_id': supplier.tax_id,
                    'bank_name': supplier.bank_name,
                    'bank_account': supplier.bank_account,
                    'rating': supplier.rating,
                    'status': supplier.status
                }
            }
        )

        db.session.commit()
        flash('供应商更新成功！', 'success')
        return redirect(url_for('supplier.index'))

    return render_template('supplier/form.html',
                         form=form,
                         supplier=supplier,
                         accessible_relations=accessible_relations,
                         title='编辑供应商',
                         now=datetime.now())

@supplier_bp.route('/<int:id>/view')
@login_required
def view(id):
    """查看供应商详情"""
    supplier = Supplier.query.get_or_404(id)

    # 使用log_activity函数记录审计日志
    log_activity(
        action='view',
        resource_type='Supplier',
        resource_id=supplier.id,
        area_id=None
    )

    return render_template('supplier/view.html', supplier=supplier, title='供应商详情', now=datetime.now())

@supplier_bp.route('/<int:id>/delete', methods=['POST'])
@login_required
def delete(id):
    """删除供应商"""
    supplier = Supplier.query.get_or_404(id)

    # 检查是否有关联的产品
    if supplier.products.count() > 0:
        return jsonify({'success': 0, 'message': '该供应商有关联的产品，不能删除！'})

    # 检查是否有关联的采购订单
    if supplier.purchase_orders.count() > 0:
        return jsonify({'success': 0, 'message': '该供应商有关联的采购订单，不能删除！'})

    try:
        # 先删除与供应商关联的证书
        from app.models import SupplierCertificate, SupplierSchoolRelation
        SupplierCertificate.query.filter_by(supplier_id=supplier.id).delete()

        # 删除与供应商关联的学校关系
        SupplierSchoolRelation.query.filter_by(supplier_id=supplier.id).delete()

        # 使用log_activity函数记录审计日志
        log_activity(
            action='delete',
            resource_type='Supplier',
            resource_id=supplier.id,
            area_id=None,
            details={
                'supplier_name': supplier.name,
                'contact_person': supplier.contact_person,
                'phone': supplier.phone
            }
        )

        db.session.delete(supplier)
        db.session.commit()

        return jsonify({'success': 1, 'message': '供应商删除成功！'})
    except Exception as e:
        db.session.rollback()
        print(f"删除供应商时出错: {e}")
        return jsonify({'success': 0, 'message': f'删除供应商时出错: {str(e)}'})

@supplier_bp.route('/api')
@login_required
def api_list():
    """供应商API"""
    category_id = request.args.get('category_id', type=int)
    keyword = request.args.get('keyword', '')

    # 基本查询
    query = Supplier.query

    if category_id:
        query = query.filter_by(category_id=category_id)

    if keyword:
        query = query.filter(Supplier.name.like(f'%{keyword}%'))

    # 根据用户区域权限筛选供应商
    if not current_user.is_admin():
        # 获取用户可访问的区域ID列表
        accessible_area_ids = [area.id for area in current_user.get_accessible_areas()]

        # 通过供应商-学校关联表筛选供应商
        query = query.join(SupplierSchoolRelation, Supplier.id == SupplierSchoolRelation.supplier_id)\
                    .filter(SupplierSchoolRelation.area_id.in_(accessible_area_ids))\
                    .filter(SupplierSchoolRelation.status == 1)\
                    .distinct()

    suppliers = query.all()
    return jsonify([{
        'id': s.id,
        'name': s.name,
        'contact_person': s.contact_person,
        'phone': s.phone,
        'status': s.status,
        'category_name': s.category.name if s.category else None
    } for s in suppliers])

@supplier_bp.route('/export')
@login_required
def export_suppliers():
    """导出供应商信息"""
    try:
        # 获取查询参数
        export_format = request.args.get('format', 'excel')
        status_filter = request.args.get('status', 'all')
        category_id = request.args.get('category_id', type=int)

        # 构建查询
        query = Supplier.query

        if category_id:
            query = query.filter_by(category_id=category_id)

        if status_filter == 'active':
            query = query.filter_by(is_active=True)
        elif status_filter == 'inactive':
            query = query.filter_by(is_active=False)

        # 根据用户区域权限筛选供应商
        if not current_user.is_admin():
            # 获取用户可访问的区域ID列表
            accessible_area_ids = [area.id for area in current_user.get_accessible_areas()]

            # 通过供应商-学校关联表筛选供应商
            query = query.join(SupplierSchoolRelation, Supplier.id == SupplierSchoolRelation.supplier_id)\
                        .filter(SupplierSchoolRelation.area_id.in_(accessible_area_ids))\
                        .filter(SupplierSchoolRelation.status == 1)\
                        .distinct()

        suppliers = query.order_by(Supplier.created_at.desc()).all()

        if not suppliers:
            return jsonify({'success': False, 'message': '没有找到供应商数据'}), 400

        # 获取用户当前区域
        user_area = current_user.get_current_area()

        # 导出数据
        from app.services.export_service import export_suppliers_excel
        return export_suppliers_excel(suppliers, user_area)

    except Exception as e:
        current_app.logger.error(f"导出供应商失败: {str(e)}")
        return jsonify({'success': False, 'message': f'导出失败: {str(e)}'}), 500

@supplier_bp.route('/export/statistics')
@login_required
def export_supplier_statistics():
    """导出供应商统计报表"""
    try:
        # 获取统计数据
        from app.models import PurchaseOrder

        # 供应商采购统计
        supplier_stats = db.session.query(
            Supplier.name,
            func.count(PurchaseOrder.id).label('order_count'),
            func.sum(PurchaseOrder.total_amount).label('total_amount'),
            func.max(PurchaseOrder.order_date).label('last_order_date')
        ).outerjoin(PurchaseOrder).group_by(Supplier.id, Supplier.name).all()

        # 准备导出数据
        data = []
        for stat in supplier_stats:
            data.append({
                'supplier_name': stat.name,
                'order_count': stat.order_count or 0,
                'total_amount': f"{stat.total_amount:.2f}" if stat.total_amount else "0.00",
                'last_order_date': stat.last_order_date.strftime('%Y-%m-%d') if stat.last_order_date else ''
            })

        from app.services.export_service import UnifiedExportService
        export_service = UnifiedExportService()

        # 使用自定义模板
        export_service.templates['supplier_statistics'] = {
            'headers': ['供应商名称', '订单数量', '采购总额', '最后订单日期'],
            'columns': ['supplier_name', 'order_count', 'total_amount', 'last_order_date'],
            'title': '供应商采购统计'
        }

        user_area = current_user.get_current_area()
        return export_service.export_to_excel(data, 'supplier_statistics', user_area=user_area)

    except Exception as e:
        current_app.logger.error(f"导出供应商统计失败: {str(e)}")
        return jsonify({'success': False, 'message': f'导出失败: {str(e)}'}), 500
