{% extends "base.html" %}

{% block title %}过期食材处理记录{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-clipboard-list"></i> 过期食材处理记录
                    </h3>
                    <div class="card-tools">
                        <a href="{{ url_for('inventory.check_expiry') }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus"></i> 返回过期检查
                        </a>
                    </div>
                </div>
                
                <div class="card-body">
                    <!-- 筛选器 -->
                    <div class="row mb-3">
                        <div class="col-md-3">
                            <select class="form-control" id="statusFilter" onchange="filterRecords()">
                                <option value="">全部状态</option>
                                <option value="待处理" {% if status == '待处理' %}selected{% endif %}>待处理</option>
                                <option value="处理中" {% if status == '处理中' %}selected{% endif %}>处理中</option>
                                <option value="已完成" {% if status == '已完成' %}selected{% endif %}>已完成</option>
                                <option value="已取消" {% if status == '已取消' %}selected{% endif %}>已取消</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select class="form-control" id="typeFilter" onchange="filterRecords()">
                                <option value="">全部类型</option>
                                <option value="报废" {% if handling_type == '报废' %}selected{% endif %}>报废</option>
                                <option value="退货" {% if handling_type == '退货' %}selected{% endif %}>退货</option>
                                <option value="转移" {% if handling_type == '转移' %}selected{% endif %}>转移</option>
                                <option value="其他" {% if handling_type == '其他' %}selected{% endif %}>其他</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <div class="btn-group float-right">
                                <button type="button" class="btn btn-info btn-sm" onclick="exportRecords()">
                                    <i class="fas fa-download"></i> 导出记录
                                </button>
                                <button type="button" class="btn btn-success btn-sm" onclick="generateLossReport()">
                                    <i class="fas fa-chart-line"></i> 生成损失报告
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 统计卡片 -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="info-box bg-info">
                                <span class="info-box-icon"><i class="fas fa-clipboard-list"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">总处理记录</span>
                                    <span class="info-box-number">{{ handlings|length }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box bg-warning">
                                <span class="info-box-icon"><i class="fas fa-clock"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">待处理</span>
                                    <span class="info-box-number">{{ handlings|selectattr('handling_status', 'equalto', '待处理')|list|length }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box bg-success">
                                <span class="info-box-icon"><i class="fas fa-check"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">已完成</span>
                                    <span class="info-box-number">{{ handlings|selectattr('handling_status', 'equalto', '已完成')|list|length }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box bg-danger">
                                <span class="info-box-icon"><i class="fas fa-dollar-sign"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">总损失金额</span>
                                    <span class="info-box-number">¥{{ handlings|selectattr('total_loss')|map(attribute='total_loss')|sum|round(2) }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 处理记录表格 -->
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>食材名称</th>
                                    <th>批次号</th>
                                    <th>处理类型</th>
                                    <th>处理数量</th>
                                    <th>损失金额</th>
                                    <th>处理状态</th>
                                    <th>计划日期</th>
                                    <th>实际日期</th>
                                    <th>操作人</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for handling in handlings %}
                                <tr>
                                    <td>{{ handling.inventory.ingredient.name }}</td>
                                    <td>{{ handling.inventory.batch_number }}</td>
                                    <td>
                                        <span class="badge {% if handling.handling_type == '报废' %}badge-danger{% elif handling.handling_type == '退货' %}badge-warning{% elif handling.handling_type == '转移' %}badge-info{% else %}badge-secondary{% endif %}">
                                            {{ handling.handling_type }}
                                        </span>
                                    </td>
                                    <td>{{ handling.quantity }} {{ handling.unit }}</td>
                                    <td>
                                        {% if handling.total_loss %}
                                            ¥{{ handling.total_loss|round(2) }}
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge {% if handling.handling_status == '待处理' %}badge-warning{% elif handling.handling_status == '处理中' %}badge-info{% elif handling.handling_status == '已完成' %}badge-success{% else %}badge-secondary{% endif %}">
                                            {{ handling.handling_status }}
                                        </span>
                                    </td>
                                    <td>{{ handling.planned_date|format_datetime('%Y-%m-%d') }}</td>
                                    <td>
                                        {% if handling.handling_date %}
                                            {{ handling.handling_date|format_datetime('%Y-%m-%d') }}
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                    <td>{{ handling.operator.real_name or handling.operator.username }}</td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ url_for('expiry_handling.view', handling_id=handling.id) }}" class="btn btn-info btn-sm">
                                                <i class="fas fa-eye"></i> 查看
                                            </a>
                                            {% if handling.handling_status in ['待处理', '处理中'] %}
                                            <button type="button" class="btn btn-success btn-sm" onclick="updateStatus({{ handling.id }}, '已完成')">
                                                <i class="fas fa-check"></i> 完成
                                            </button>
                                            <button type="button" class="btn btn-secondary btn-sm" onclick="updateStatus({{ handling.id }}, '已取消')">
                                                <i class="fas fa-times"></i> 取消
                                            </button>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="10" class="text-center">暂无处理记录</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- 分页 -->
                    {% if pagination.pages > 1 %}
                    <nav aria-label="处理记录分页">
                        <ul class="pagination justify-content-center">
                            {% if pagination.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('expiry_handling.index', page=pagination.prev_num, status=status, handling_type=handling_type) }}">上一页</a>
                            </li>
                            {% endif %}
                            
                            {% for page_num in pagination.iter_pages() %}
                                {% if page_num %}
                                    {% if page_num != pagination.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('expiry_handling.index', page=page_num, status=status, handling_type=handling_type) }}">{{ page_num }}</a>
                                    </li>
                                    {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                    {% endif %}
                                {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">…</span>
                                </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if pagination.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('expiry_handling.index', page=pagination.next_num, status=status, handling_type=handling_type) }}">下一页</a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// 筛选记录
function filterRecords() {
    const status = document.getElementById('statusFilter').value;
    const type = document.getElementById('typeFilter').value;
    
    const params = new URLSearchParams();
    if (status) params.append('status', status);
    if (type) params.append('handling_type', type);
    
    window.location.href = '{{ url_for("expiry_handling.index") }}?' + params.toString();
}

// 更新处理状态
function updateStatus(handlingId, newStatus) {
    let confirmMessage = `确认将处理状态更新为"${newStatus}"吗？`;
    let approvalNotes = '';
    
    if (newStatus === '已完成') {
        approvalNotes = prompt('请输入审批意见（可选）：') || '';
    }
    
    if (confirm(confirmMessage)) {
        fetch(`/expiry-handling/${handlingId}/update-status`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                status: newStatus,
                approval_notes: approvalNotes
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
                location.reload();
            } else {
                alert('更新失败: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('更新失败');
        });
    }
}

// 导出记录
function exportRecords() {
    const status = document.getElementById('statusFilter').value;
    const type = document.getElementById('typeFilter').value;
    
    const params = new URLSearchParams();
    if (status) params.append('status', status);
    if (type) params.append('handling_type', type);
    params.append('export', 'true');
    
    window.open('{{ url_for("expiry_handling.index") }}?' + params.toString());
}

// 生成损失报告
function generateLossReport() {
    window.open('{{ url_for("expiry_handling.loss_report") }}');
}
</script>

{% endblock %}
