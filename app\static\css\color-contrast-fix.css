/* 颜色对比度和字体优化 - 突出表格内容，优化表头样式 */

/* ===== 表头样式优化 ===== */

/* 1. 统一表头样式 - 柔和背景，正常字重 */
.table thead th,
thead th,
.table-responsive thead th,
.thead-dark th,
.thead-light th {
    color: #ffffff !important;
    background-color: #6c757d !important; /* 柔和的中灰色 */
    font-size: 1rem !important; /* 16px - 表头字体适中 */
    font-weight: 500 !important; /* 中等字重，不用粗体 */
    text-align: center !important;
    vertical-align: middle !important;
    padding: 0.75rem 0.5rem !important;
    border-bottom: 2px solid #dee2e6 !important;
    letter-spacing: 0.5px !important; /* 轻微字间距 */
}

/* 2. 浅色表头特殊处理 */
.thead-light th {
    color: #495057 !important;
    background-color: #f1f3f4 !important; /* 更柔和的浅色 */
    font-weight: 500 !important;
}

/* ===== 表格内容突出显示 ===== */

/* 1. 表格数据字体 - 突出显示 */
.table tbody td {
    font-size: 1.125rem !important; /* 18px - 比表头大 */
    font-weight: 600 !important; /* 稍微加粗，突出内容 */
    color: #212529 !important; /* 深色文字，清晰可读 */
    padding: 0.75rem 0.5rem !important;
    vertical-align: middle !important;
    line-height: 1.4 !important; /* 适当行高 */
}

/* 2. 重要数据突出显示 */
.table .ingredient-highlight,
.table .stock-in-number,
.table .important-data {
    font-size: 1.25rem !important; /* 20px - 更大字体 */
    font-weight: 700 !important; /* 加粗显示 */
    color: #0d6efd !important; /* 蓝色突出 */
}

/* 3. 数字数据特殊样式 */
.table td.text-right,
.table .quantity,
.table .amount,
.table .price {
    font-size: 1.25rem !important; /* 20px */
    font-weight: 700 !important; /* 加粗 */
    color: #198754 !important; /* 绿色表示数值 */
    text-align: right !important;
}

/* 4. 食材名称突出显示 */
.table .ingredient-name {
    font-size: 1.25rem !important; /* 20px */
    font-weight: 700 !important;
    color: #0d6efd !important;
}

/* ===== 表格整体优化 ===== */

/* 1. 表格基础样式 */
.table {
    font-size: 1.125rem !important; /* 18px 基础字体 */
    border-collapse: separate !important;
    border-spacing: 0 !important;
    margin-bottom: 1.5rem !important;
}

/* 2. 表格边框优化 */
.table-bordered {
    border: 1px solid #dee2e6 !important;
}

.table-bordered th,
.table-bordered td {
    border: 1px solid #dee2e6 !important;
}

/* 3. 表格悬停效果 */
.table-hover tbody tr:hover {
    background-color: rgba(13, 110, 253, 0.075) !important; /* 淡蓝色悬停 */
}

.table-hover tbody tr:hover td {
    color: #0d6efd !important; /* 悬停时文字变蓝 */
}

/* ===== 特殊元素样式 ===== */

/* 1. 徽章样式 */
.table .badge {
    font-size: 0.875rem !important; /* 14px */
    font-weight: 600 !important;
    padding: 0.375rem 0.75rem !important;
    border-radius: 0.375rem !important;
}

/* 2. 小文字保持适中 */
.table small,
.table .text-muted {
    font-size: 0.875rem !important; /* 14px */
    font-weight: 400 !important;
    color: #6c757d !important;
}

/* 3. 按钮在表格中的样式 */
.table .btn {
    font-size: 0.875rem !important; /* 14px */
    font-weight: 500 !important;
    padding: 0.25rem 0.5rem !important;
}

.table .btn-xs {
    font-size: 0.75rem !important; /* 12px */
    padding: 0.125rem 0.375rem !important;
}

/* ===== 响应式优化 ===== */

/* 平板设备 */
@media (max-width: 768px) {
    .table thead th {
        font-size: 0.875rem !important; /* 14px */
        padding: 0.5rem 0.25rem !important;
    }

    .table tbody td {
        font-size: 1rem !important; /* 16px */
        padding: 0.5rem 0.25rem !important;
    }

    .table .ingredient-highlight,
    .table .stock-in-number {
        font-size: 1.125rem !important; /* 18px */
    }
}

/* 手机设备 */
@media (max-width: 576px) {
    .table thead th {
        font-size: 0.75rem !important; /* 12px */
        padding: 0.375rem 0.125rem !important;
    }

    .table tbody td {
        font-size: 0.875rem !important; /* 14px */
        padding: 0.375rem 0.125rem !important;
    }

    .table .ingredient-highlight,
    .table .stock-in-number {
        font-size: 1rem !important; /* 16px */
    }
}

/* ===== 库存页面特殊优化 ===== */

/* 1. 库存表格食材信息突出 */
.inventory-table .ingredient-highlight {
    font-size: 1.375rem !important; /* 22px - 更大字体 */
    font-weight: 700 !important;
    color: #0d6efd !important;
    display: block !important;
    margin-bottom: 0.25rem !important;
}

/* 2. 库存分类信息 */
.inventory-table .ingredient-category {
    font-size: 0.875rem !important; /* 14px */
    color: #6c757d !important;
    font-weight: 400 !important;
}

/* 3. 库存数量突出显示 */
.inventory-table .text-primary {
    font-size: 1.5rem !important; /* 24px - 最大字体 */
    font-weight: 800 !important; /* 最粗字体 */
    color: #198754 !important; /* 绿色表示库存 */
}

/* 4. 批次号等代码样式 */
.inventory-table .text-monospace {
    font-size: 1rem !important; /* 16px */
    font-weight: 600 !important;
    color: #6f42c1 !important; /* 紫色表示代码 */
    background-color: #f8f9fa !important;
    padding: 0.125rem 0.25rem !important;
    border-radius: 0.25rem !important;
}

/* ===== 状态指示器优化 ===== */

/* 1. 状态徽章 - 高对比度版本 */
.badge-success {
    background-color: #198754 !important;
    color: #ffffff !important;
    font-weight: 600 !important;
    border: 1px solid #146c43 !important;
}
.badge-warning {
    background-color: #ffc107 !important;
    color: #000000 !important;
    font-weight: 600 !important;
    border: 1px solid #ffca2c !important;
}
.badge-danger {
    background-color: #dc3545 !important;
    color: #ffffff !important;
    font-weight: 600 !important;
    border: 1px solid #b02a37 !important;
}
.badge-info {
    background-color: #0dcaf0 !important;
    color: #000000 !important;
    font-weight: 600 !important;
    border: 1px solid #3dd5f3 !important;
}
.badge-secondary {
    background-color: #6c757d !important;
    color: #ffffff !important;
    font-weight: 600 !important;
    border: 1px solid #565e64 !important;
}



/* 2. 库存状态指示器 */
.stock-indicator {
    display: inline-block !important;
    width: 8px !important;
    height: 8px !important;
    border-radius: 50% !important;
    margin-right: 0.5rem !important;
}

.stock-indicator.sufficient { background-color: #198754 !important; }
.stock-indicator.low { background-color: #ffc107 !important; }
.stock-indicator.critical { background-color: #fd7e14 !important; }
.stock-indicator.expired { background-color: #dc3545 !important; }

/* ===== 按钮组优化 ===== */

.btn-group-compact .btn {
    margin-right: 0.25rem !important;
    font-size: 0.75rem !important;
    padding: 0.25rem 0.5rem !important;
}

.btn-group-compact .btn:last-child {
    margin-right: 0 !important;
}

/* ===== 分页样式优化 ===== */

.pagination .page-link {
    font-size: 1rem !important; /* 16px */
    font-weight: 500 !important;
    padding: 0.5rem 0.75rem !important;
}

.pagination .page-item.active .page-link {
    font-weight: 700 !important;
}

/* ===== 表格容器优化 ===== */

.table-responsive {
    border-radius: 0.375rem !important;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
}

/* ===== 确保所有表格应用样式 ===== */

table th,
table td {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif !important;
}

/* 强制应用到所有表格 */
.table,
.table th,
.table td,
.table-responsive table,
.table-responsive th,
.table-responsive td {
    font-family: inherit !important;
}
