{% extends "base.html" %}

{% block title %}智能采购建议{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-brain"></i> 智能采购建议 - {{ area.name }}
                    </h3>
                    <div class="card-tools">
                        <button type="button" class="btn btn-primary btn-sm" onclick="refreshSuggestions()">
                            <i class="fas fa-sync"></i> 刷新建议
                        </button>
                        <a href="{{ url_for('purchase_order.index') }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> 返回列表
                        </a>
                    </div>
                </div>
                
                <div class="card-body">
                    <!-- 统计概览 -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="info-box bg-danger">
                                <span class="info-box-icon"><i class="fas fa-exclamation-triangle"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">紧急采购</span>
                                    <span class="info-box-number">{{ grouped_suggestions['紧急']|length }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box bg-warning">
                                <span class="info-box-icon"><i class="fas fa-clock"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">高优先级</span>
                                    <span class="info-box-number">{{ grouped_suggestions['高']|length }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box bg-info">
                                <span class="info-box-icon"><i class="fas fa-list"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">中优先级</span>
                                    <span class="info-box-number">{{ grouped_suggestions['中']|length }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box bg-success">
                                <span class="info-box-icon"><i class="fas fa-check"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">总建议项目</span>
                                    <span class="info-box-number">{{ suggestions|length }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 紧急采购提醒 -->
                    {% if grouped_suggestions['紧急'] %}
                    <div class="alert alert-danger">
                        <h5><i class="fas fa-exclamation-triangle"></i> 紧急采购提醒</h5>
                        <p>以下食材库存严重不足，建议立即安排采购：</p>
                        <div class="row">
                            {% for suggestion in grouped_suggestions['紧急'] %}
                            <div class="col-md-4 mb-2">
                                <div class="card card-danger">
                                    <div class="card-body p-2">
                                        <strong>{{ suggestion.ingredient_name }}</strong><br>
                                        <small>库存: {{ suggestion.current_stock.total_quantity|round(1) }}</small><br>
                                        <small>可用: {{ suggestion.stock_days|round(1) }}天</small><br>
                                        <span class="badge badge-danger">建议采购: {{ suggestion.suggested_quantity|round(1) }}</span>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}
                    
                    <!-- 采购建议列表 -->
                    <div class="row">
                        {% for suggestion in suggestions %}
                        <div class="col-md-6 mb-4">
                            <div class="card {% if suggestion.urgency_level == '紧急' %}card-danger{% elif suggestion.urgency_level == '高' %}card-warning{% elif suggestion.urgency_level == '中' %}card-info{% else %}card-secondary{% endif %}">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">
                                        {{ suggestion.ingredient_name }}
                                        <span class="badge {% if suggestion.urgency_level == '紧急' %}badge-danger{% elif suggestion.urgency_level == '高' %}badge-warning{% elif suggestion.urgency_level == '中' %}badge-info{% else %}badge-secondary{% endif %} float-right">
                                            {{ suggestion.urgency_level }}优先级
                                        </span>
                                    </h5>
                                    <small class="text-muted">{{ suggestion.category_name }}</small>
                                </div>
                                <div class="card-body">
                                    <!-- 关键指标 -->
                                    <div class="row mb-3">
                                        <div class="col-4">
                                            <small class="text-muted">当前库存</small><br>
                                            <strong>{{ suggestion.current_stock.total_quantity|round(1) }}</strong>
                                        </div>
                                        <div class="col-4">
                                            <small class="text-muted">可用天数</small><br>
                                            <strong class="{% if suggestion.stock_days <= 3 %}text-danger{% elif suggestion.stock_days <= 7 %}text-warning{% else %}text-success{% endif %}">
                                                {{ suggestion.stock_days|round(1) }}天
                                            </strong>
                                        </div>
                                        <div class="col-4">
                                            <small class="text-muted">建议采购</small><br>
                                            <strong class="text-primary">{{ suggestion.suggested_quantity|round(1) }}</strong>
                                        </div>
                                    </div>
                                    
                                    <!-- 消耗数据 -->
                                    <div class="row mb-3">
                                        <div class="col-6">
                                            <small class="text-muted">日均消耗</small><br>
                                            <span>{{ suggestion.consumption_data.avg_daily_consumption|round(2) }}</span>
                                        </div>
                                        <div class="col-6">
                                            <small class="text-muted">消耗趋势</small><br>
                                            <span class="{% if suggestion.consumption_data.trend > 0 %}text-success{% elif suggestion.consumption_data.trend < 0 %}text-danger{% else %}text-muted{% endif %}">
                                                {% if suggestion.consumption_data.trend > 0 %}
                                                    ↗ +{{ (suggestion.consumption_data.trend * 100)|round(1) }}%
                                                {% elif suggestion.consumption_data.trend < 0 %}
                                                    ↘ {{ (suggestion.consumption_data.trend * 100)|round(1) }}%
                                                {% else %}
                                                    → 稳定
                                                {% endif %}
                                            </span>
                                        </div>
                                    </div>
                                    
                                    <!-- 过期率 -->
                                    {% if suggestion.expiry_rate > 0 %}
                                    <div class="mb-3">
                                        <small class="text-muted">历史过期率</small><br>
                                        <span class="badge {% if suggestion.expiry_rate > 0.2 %}badge-danger{% elif suggestion.expiry_rate > 0.1 %}badge-warning{% else %}badge-success{% endif %}">
                                            {{ (suggestion.expiry_rate * 100)|round(1) }}%
                                        </span>
                                        {% if suggestion.expiry_rate > 0.1 %}
                                        <small class="text-muted">（建议适量采购）</small>
                                        {% endif %}
                                    </div>
                                    {% endif %}
                                    
                                    <!-- 推荐原因 -->
                                    <div class="mb-3">
                                        <small class="text-muted">推荐原因:</small><br>
                                        <small>{{ suggestion.recommendation_reason }}</small>
                                    </div>
                                    
                                    <!-- 操作按钮 -->
                                    <div class="btn-group w-100">
                                        <button type="button" class="btn btn-primary btn-sm" onclick="createPurchaseOrder({{ suggestion.ingredient_id }}, {{ suggestion.suggested_quantity }})">
                                            <i class="fas fa-plus"></i> 创建采购单
                                        </button>
                                        <button type="button" class="btn btn-info btn-sm" onclick="viewIngredientDetail({{ suggestion.ingredient_id }})">
                                            <i class="fas fa-eye"></i> 查看详情
                                        </button>
                                        <button type="button" class="btn btn-secondary btn-sm" onclick="adjustSuggestion({{ suggestion.ingredient_id }}, {{ suggestion.suggested_quantity }})">
                                            <i class="fas fa-edit"></i> 调整数量
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% else %}
                        <div class="col-12">
                            <div class="alert alert-info text-center">
                                <i class="fas fa-info-circle"></i> 当前没有需要采购的食材建议
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    
                    <!-- 批量操作 -->
                    {% if suggestions %}
                    <div class="card mt-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">批量操作</h5>
                        </div>
                        <div class="card-body">
                            <div class="btn-group">
                                <button type="button" class="btn btn-success" onclick="batchCreateOrders('urgent')">
                                    <i class="fas fa-plus"></i> 批量创建紧急采购单
                                </button>
                                <button type="button" class="btn btn-warning" onclick="batchCreateOrders('high')">
                                    <i class="fas fa-plus"></i> 批量创建高优先级采购单
                                </button>
                                <button type="button" class="btn btn-info" onclick="exportSuggestions()">
                                    <i class="fas fa-download"></i> 导出建议清单
                                </button>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 调整数量对话框 -->
<div class="modal fade" id="adjustModal" tabindex="-1" role="dialog" aria-labelledby="adjustModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="adjustModalLabel">调整采购数量</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="adjustForm">
                    <input type="hidden" id="adjustIngredientId">
                    <div class="form-group">
                        <label for="adjustQuantity">采购数量</label>
                        <input type="number" class="form-control" id="adjustQuantity" step="0.01" required>
                        <small class="form-text text-muted">建议数量: <span id="suggestedQuantity">-</span></small>
                    </div>
                    <div class="form-group">
                        <label for="adjustReason">调整原因</label>
                        <textarea class="form-control" id="adjustReason" rows="3" placeholder="请说明调整原因..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="confirmAdjustment()">确认调整</button>
            </div>
        </div>
    </div>
</div>

<script>
// 刷新建议
function refreshSuggestions() {
    location.reload();
}

// 创建采购订单
function createPurchaseOrder(ingredientId, suggestedQuantity) {
    const url = `/purchase-order/create-form?ingredient_id=${ingredientId}&suggested_quantity=${suggestedQuantity}&from_suggestion=true`;
    window.open(url, '_blank');
}

// 查看食材详情
function viewIngredientDetail(ingredientId) {
    const url = `/inventory?ingredient_id=${ingredientId}`;
    window.open(url, '_blank');
}

// 调整建议数量
function adjustSuggestion(ingredientId, suggestedQuantity) {
    document.getElementById('adjustIngredientId').value = ingredientId;
    document.getElementById('adjustQuantity').value = suggestedQuantity;
    document.getElementById('suggestedQuantity').textContent = suggestedQuantity;
    $('#adjustModal').modal('show');
}

// 确认调整
function confirmAdjustment() {
    const ingredientId = document.getElementById('adjustIngredientId').value;
    const quantity = document.getElementById('adjustQuantity').value;
    const reason = document.getElementById('adjustReason').value;
    
    if (!quantity || quantity <= 0) {
        alert('请输入有效的采购数量');
        return;
    }
    
    // 这里可以保存用户的调整偏好，或直接创建采购单
    createPurchaseOrder(ingredientId, quantity);
    $('#adjustModal').modal('hide');
}

// 批量创建采购单
function batchCreateOrders(urgencyLevel) {
    const urgencyMap = {
        'urgent': '紧急',
        'high': '高'
    };
    
    const urgencyText = urgencyMap[urgencyLevel];
    if (confirm(`确认批量创建所有${urgencyText}优先级的采购订单吗？`)) {
        // 收集对应优先级的食材
        const ingredients = [];
        {% for suggestion in suggestions %}
        {% if suggestion.urgency_level == '紧急' %}
        if (urgencyLevel === 'urgent') {
            ingredients.push({
                id: {{ suggestion.ingredient_id }},
                name: '{{ suggestion.ingredient_name }}',
                quantity: {{ suggestion.suggested_quantity }}
            });
        }
        {% elif suggestion.urgency_level == '高' %}
        if (urgencyLevel === 'high') {
            ingredients.push({
                id: {{ suggestion.ingredient_id }},
                name: '{{ suggestion.ingredient_name }}',
                quantity: {{ suggestion.suggested_quantity }}
            });
        }
        {% endif %}
        {% endfor %}
        
        if (ingredients.length > 0) {
            // 构建批量创建URL
            const params = new URLSearchParams();
            params.append('batch_create', 'true');
            params.append('urgency_level', urgencyLevel);
            ingredients.forEach(ing => {
                params.append('ingredient_ids', ing.id);
                params.append('quantities', ing.quantity);
            });
            
            const url = `/purchase-order/create-form?${params.toString()}`;
            window.open(url, '_blank');
        } else {
            alert(`没有${urgencyText}优先级的采购建议`);
        }
    }
}

// 导出建议清单
function exportSuggestions() {
    const url = '/purchase-order/api/smart-suggestions?export=true';
    window.open(url);
}
</script>

{% endblock %}
