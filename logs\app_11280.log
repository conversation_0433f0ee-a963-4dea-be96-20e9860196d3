2025-06-22 21:14:30,079 INFO: 应用启动 - PID: 11280 [in C:\StudentsCMSSP\app\__init__.py:868]
2025-06-22 21:14:48,659 ERROR: Exception on /food-trace [GET] [in C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py:838]
Traceback (most recent call last):
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 146, in __init__
    self._dbapi_connection = engine.raw_connection()
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 3300, in raw_connection
    return self.pool.connect()
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 449, in connect
    return _ConnectionFairy._checkout(self)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 1263, in _checkout
    fairy = _ConnectionRecord.checkout(pool)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 712, in checkout
    rec = pool._do_get()
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\impl.py", line 180, in _do_get
    self._dec_overflow()
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\util\langhelpers.py", line 146, in __exit__
    raise exc_value.with_traceback(exc_tb)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\impl.py", line 177, in _do_get
    return self._create_connection()
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 390, in _create_connection
    return _ConnectionRecord(self)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 674, in __init__
    self.__connect()
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 901, in __connect
    pool.logger.debug("Error on connect(): %s", e)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\util\langhelpers.py", line 146, in __exit__
    raise exc_value.with_traceback(exc_tb)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 896, in __connect
    self.dbapi_connection = connection = pool._invoke_creator(self)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\create.py", line 643, in connect
    return dialect.connect(*cargs, **cparams)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\default.py", line 620, in connect
    return self.loaded_dbapi.connect(*cargs, **cparams)
pyodbc.OperationalError: ('08001', '[08001] [Microsoft][ODBC SQL Server Driver][DBNETLIB]SQL Server 不存在或访问被拒绝 (17) (SQLDriverConnect); [08001] [Microsoft][ODBC SQL Server Driver][DBNETLIB]ConnectionOpen (Connect()). (2)')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 1473, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 882, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_cors\extension.py", line 194, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 878, in full_dispatch_request
    rv = self.preprocess_request()
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 1253, in preprocess_request
    rv = self.ensure_sync(before_func)()
  File "C:\StudentsCMSSP\app\utils\school_middleware.py", line 79, in check_school
    if not current_user.is_authenticated:
  File "C:\StudentsCMSSP\venv\lib\site-packages\werkzeug\local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "C:\StudentsCMSSP\venv\lib\site-packages\werkzeug\local.py", line 526, in _get_current_object
    return get_name(local())
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_login\utils.py", line 25, in <lambda>
    current_user = LocalProxy(lambda: _get_user())
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_login\utils.py", line 370, in _get_user
    current_app.login_manager._load_user()
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_login\login_manager.py", line 364, in _load_user
    user = self._user_callback(user_id)
  File "C:\StudentsCMSSP\app\models.py", line 284, in load_user
    return User.query.get(int(id))
  File "<string>", line 2, in get
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\util\deprecations.py", line 386, in warned
    return fn(*args, **kwargs)  # type: ignore[no-any-return]
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\query.py", line 1127, in get
    return self._get_impl(ident, loading.load_on_pk_identity)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\query.py", line 1136, in _get_impl
    return self.session._get_impl(
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\session.py", line 3862, in _get_impl
    return db_load_fn(
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\loading.py", line 694, in load_on_pk_identity
    session.execute(
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\session.py", line 2351, in execute
    return self._execute_internal(
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\session.py", line 2226, in _execute_internal
    conn = self._connection_for_bind(bind)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\session.py", line 2095, in _connection_for_bind
    return trans._connection_for_bind(engine, execution_options)
  File "<string>", line 2, in _connection_for_bind
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\state_changes.py", line 139, in _go
    ret_value = fn(self, *arg, **kw)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\session.py", line 1189, in _connection_for_bind
    conn = bind.connect()
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 3276, in connect
    return self._connection_cls(self)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 148, in __init__
    Connection._handle_dbapi_exception_noconnection(
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 2440, in _handle_dbapi_exception_noconnection
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 146, in __init__
    self._dbapi_connection = engine.raw_connection()
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 3300, in raw_connection
    return self.pool.connect()
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 449, in connect
    return _ConnectionFairy._checkout(self)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 1263, in _checkout
    fairy = _ConnectionRecord.checkout(pool)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 712, in checkout
    rec = pool._do_get()
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\impl.py", line 180, in _do_get
    self._dec_overflow()
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\util\langhelpers.py", line 146, in __exit__
    raise exc_value.with_traceback(exc_tb)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\impl.py", line 177, in _do_get
    return self._create_connection()
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 390, in _create_connection
    return _ConnectionRecord(self)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 674, in __init__
    self.__connect()
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 901, in __connect
    pool.logger.debug("Error on connect(): %s", e)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\util\langhelpers.py", line 146, in __exit__
    raise exc_value.with_traceback(exc_tb)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 896, in __connect
    self.dbapi_connection = connection = pool._invoke_creator(self)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\create.py", line 643, in connect
    return dialect.connect(*cargs, **cparams)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\default.py", line 620, in connect
    return self.loaded_dbapi.connect(*cargs, **cparams)
sqlalchemy.exc.OperationalError: (pyodbc.OperationalError) ('08001', '[08001] [Microsoft][ODBC SQL Server Driver][DBNETLIB]SQL Server 不存在或访问被拒绝 (17) (SQLDriverConnect); [08001] [Microsoft][ODBC SQL Server Driver][DBNETLIB]ConnectionOpen (Connect()). (2)')
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-22 21:14:52,826 ERROR: Exception on /weekly-menu-v2 [GET] [in C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py:838]
Traceback (most recent call last):
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 146, in __init__
    self._dbapi_connection = engine.raw_connection()
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 3300, in raw_connection
    return self.pool.connect()
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 449, in connect
    return _ConnectionFairy._checkout(self)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 1263, in _checkout
    fairy = _ConnectionRecord.checkout(pool)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 712, in checkout
    rec = pool._do_get()
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\impl.py", line 180, in _do_get
    self._dec_overflow()
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\util\langhelpers.py", line 146, in __exit__
    raise exc_value.with_traceback(exc_tb)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\impl.py", line 177, in _do_get
    return self._create_connection()
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 390, in _create_connection
    return _ConnectionRecord(self)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 674, in __init__
    self.__connect()
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 901, in __connect
    pool.logger.debug("Error on connect(): %s", e)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\util\langhelpers.py", line 146, in __exit__
    raise exc_value.with_traceback(exc_tb)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 896, in __connect
    self.dbapi_connection = connection = pool._invoke_creator(self)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\create.py", line 643, in connect
    return dialect.connect(*cargs, **cparams)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\default.py", line 620, in connect
    return self.loaded_dbapi.connect(*cargs, **cparams)
pyodbc.OperationalError: ('08001', '[08001] [Microsoft][ODBC SQL Server Driver][DBNETLIB]SQL Server 不存在或访问被拒绝 (17) (SQLDriverConnect); [08001] [Microsoft][ODBC SQL Server Driver][DBNETLIB]ConnectionOpen (Connect()). (2)')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 1473, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 882, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_cors\extension.py", line 194, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 878, in full_dispatch_request
    rv = self.preprocess_request()
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 1253, in preprocess_request
    rv = self.ensure_sync(before_func)()
  File "C:\StudentsCMSSP\app\utils\school_middleware.py", line 79, in check_school
    if not current_user.is_authenticated:
  File "C:\StudentsCMSSP\venv\lib\site-packages\werkzeug\local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "C:\StudentsCMSSP\venv\lib\site-packages\werkzeug\local.py", line 526, in _get_current_object
    return get_name(local())
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_login\utils.py", line 25, in <lambda>
    current_user = LocalProxy(lambda: _get_user())
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_login\utils.py", line 370, in _get_user
    current_app.login_manager._load_user()
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_login\login_manager.py", line 364, in _load_user
    user = self._user_callback(user_id)
  File "C:\StudentsCMSSP\app\models.py", line 284, in load_user
    return User.query.get(int(id))
  File "<string>", line 2, in get
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\util\deprecations.py", line 386, in warned
    return fn(*args, **kwargs)  # type: ignore[no-any-return]
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\query.py", line 1127, in get
    return self._get_impl(ident, loading.load_on_pk_identity)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\query.py", line 1136, in _get_impl
    return self.session._get_impl(
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\session.py", line 3862, in _get_impl
    return db_load_fn(
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\loading.py", line 694, in load_on_pk_identity
    session.execute(
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\session.py", line 2351, in execute
    return self._execute_internal(
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\session.py", line 2226, in _execute_internal
    conn = self._connection_for_bind(bind)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\session.py", line 2095, in _connection_for_bind
    return trans._connection_for_bind(engine, execution_options)
  File "<string>", line 2, in _connection_for_bind
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\state_changes.py", line 139, in _go
    ret_value = fn(self, *arg, **kw)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\session.py", line 1189, in _connection_for_bind
    conn = bind.connect()
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 3276, in connect
    return self._connection_cls(self)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 148, in __init__
    Connection._handle_dbapi_exception_noconnection(
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 2440, in _handle_dbapi_exception_noconnection
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 146, in __init__
    self._dbapi_connection = engine.raw_connection()
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 3300, in raw_connection
    return self.pool.connect()
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 449, in connect
    return _ConnectionFairy._checkout(self)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 1263, in _checkout
    fairy = _ConnectionRecord.checkout(pool)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 712, in checkout
    rec = pool._do_get()
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\impl.py", line 180, in _do_get
    self._dec_overflow()
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\util\langhelpers.py", line 146, in __exit__
    raise exc_value.with_traceback(exc_tb)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\impl.py", line 177, in _do_get
    return self._create_connection()
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 390, in _create_connection
    return _ConnectionRecord(self)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 674, in __init__
    self.__connect()
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 901, in __connect
    pool.logger.debug("Error on connect(): %s", e)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\util\langhelpers.py", line 146, in __exit__
    raise exc_value.with_traceback(exc_tb)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 896, in __connect
    self.dbapi_connection = connection = pool._invoke_creator(self)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\create.py", line 643, in connect
    return dialect.connect(*cargs, **cparams)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\default.py", line 620, in connect
    return self.loaded_dbapi.connect(*cargs, **cparams)
sqlalchemy.exc.OperationalError: (pyodbc.OperationalError) ('08001', '[08001] [Microsoft][ODBC SQL Server Driver][DBNETLIB]SQL Server 不存在或访问被拒绝 (17) (SQLDriverConnect); [08001] [Microsoft][ODBC SQL Server Driver][DBNETLIB]ConnectionOpen (Connect()). (2)')
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-22 21:14:53,953 WARNING: [安全监控] 2025-06-22 21:14:53 - 可疑请求 | IP: **************:58906 | 路径: /wp-admin/setup-config.php, 指标: 可疑路径 [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-22 21:14:54,168 ERROR: Exception on /weekly-menu-v2 [GET] [in C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py:838]
Traceback (most recent call last):
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 146, in __init__
    self._dbapi_connection = engine.raw_connection()
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 3300, in raw_connection
    return self.pool.connect()
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 449, in connect
    return _ConnectionFairy._checkout(self)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 1263, in _checkout
    fairy = _ConnectionRecord.checkout(pool)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 712, in checkout
    rec = pool._do_get()
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\impl.py", line 180, in _do_get
    self._dec_overflow()
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\util\langhelpers.py", line 146, in __exit__
    raise exc_value.with_traceback(exc_tb)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\impl.py", line 177, in _do_get
    return self._create_connection()
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 390, in _create_connection
    return _ConnectionRecord(self)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 674, in __init__
    self.__connect()
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 901, in __connect
    pool.logger.debug("Error on connect(): %s", e)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\util\langhelpers.py", line 146, in __exit__
    raise exc_value.with_traceback(exc_tb)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 896, in __connect
    self.dbapi_connection = connection = pool._invoke_creator(self)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\create.py", line 643, in connect
    return dialect.connect(*cargs, **cparams)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\default.py", line 620, in connect
    return self.loaded_dbapi.connect(*cargs, **cparams)
pyodbc.OperationalError: ('08001', '[08001] [Microsoft][ODBC SQL Server Driver][DBNETLIB]SQL Server 不存在或访问被拒绝 (17) (SQLDriverConnect); [08001] [Microsoft][ODBC SQL Server Driver][DBNETLIB]ConnectionOpen (Connect()). (2)')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 1473, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 882, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_cors\extension.py", line 194, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 878, in full_dispatch_request
    rv = self.preprocess_request()
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 1253, in preprocess_request
    rv = self.ensure_sync(before_func)()
  File "C:\StudentsCMSSP\app\utils\school_middleware.py", line 79, in check_school
    if not current_user.is_authenticated:
  File "C:\StudentsCMSSP\venv\lib\site-packages\werkzeug\local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "C:\StudentsCMSSP\venv\lib\site-packages\werkzeug\local.py", line 526, in _get_current_object
    return get_name(local())
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_login\utils.py", line 25, in <lambda>
    current_user = LocalProxy(lambda: _get_user())
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_login\utils.py", line 370, in _get_user
    current_app.login_manager._load_user()
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_login\login_manager.py", line 364, in _load_user
    user = self._user_callback(user_id)
  File "C:\StudentsCMSSP\app\models.py", line 284, in load_user
    return User.query.get(int(id))
  File "<string>", line 2, in get
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\util\deprecations.py", line 386, in warned
    return fn(*args, **kwargs)  # type: ignore[no-any-return]
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\query.py", line 1127, in get
    return self._get_impl(ident, loading.load_on_pk_identity)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\query.py", line 1136, in _get_impl
    return self.session._get_impl(
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\session.py", line 3862, in _get_impl
    return db_load_fn(
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\loading.py", line 694, in load_on_pk_identity
    session.execute(
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\session.py", line 2351, in execute
    return self._execute_internal(
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\session.py", line 2226, in _execute_internal
    conn = self._connection_for_bind(bind)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\session.py", line 2095, in _connection_for_bind
    return trans._connection_for_bind(engine, execution_options)
  File "<string>", line 2, in _connection_for_bind
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\state_changes.py", line 139, in _go
    ret_value = fn(self, *arg, **kw)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\session.py", line 1189, in _connection_for_bind
    conn = bind.connect()
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 3276, in connect
    return self._connection_cls(self)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 148, in __init__
    Connection._handle_dbapi_exception_noconnection(
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 2440, in _handle_dbapi_exception_noconnection
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 146, in __init__
    self._dbapi_connection = engine.raw_connection()
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 3300, in raw_connection
    return self.pool.connect()
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 449, in connect
    return _ConnectionFairy._checkout(self)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 1263, in _checkout
    fairy = _ConnectionRecord.checkout(pool)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 712, in checkout
    rec = pool._do_get()
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\impl.py", line 180, in _do_get
    self._dec_overflow()
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\util\langhelpers.py", line 146, in __exit__
    raise exc_value.with_traceback(exc_tb)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\impl.py", line 177, in _do_get
    return self._create_connection()
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 390, in _create_connection
    return _ConnectionRecord(self)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 674, in __init__
    self.__connect()
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 901, in __connect
    pool.logger.debug("Error on connect(): %s", e)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\util\langhelpers.py", line 146, in __exit__
    raise exc_value.with_traceback(exc_tb)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 896, in __connect
    self.dbapi_connection = connection = pool._invoke_creator(self)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\create.py", line 643, in connect
    return dialect.connect(*cargs, **cparams)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\default.py", line 620, in connect
    return self.loaded_dbapi.connect(*cargs, **cparams)
sqlalchemy.exc.OperationalError: (pyodbc.OperationalError) ('08001', '[08001] [Microsoft][ODBC SQL Server Driver][DBNETLIB]SQL Server 不存在或访问被拒绝 (17) (SQLDriverConnect); [08001] [Microsoft][ODBC SQL Server Driver][DBNETLIB]ConnectionOpen (Connect()). (2)')
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-22 21:14:55,627 ERROR: Exception on /weekly-menu-v2 [GET] [in C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py:838]
Traceback (most recent call last):
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 146, in __init__
    self._dbapi_connection = engine.raw_connection()
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 3300, in raw_connection
    return self.pool.connect()
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 449, in connect
    return _ConnectionFairy._checkout(self)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 1263, in _checkout
    fairy = _ConnectionRecord.checkout(pool)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 712, in checkout
    rec = pool._do_get()
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\impl.py", line 180, in _do_get
    self._dec_overflow()
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\util\langhelpers.py", line 146, in __exit__
    raise exc_value.with_traceback(exc_tb)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\impl.py", line 177, in _do_get
    return self._create_connection()
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 390, in _create_connection
    return _ConnectionRecord(self)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 674, in __init__
    self.__connect()
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 901, in __connect
    pool.logger.debug("Error on connect(): %s", e)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\util\langhelpers.py", line 146, in __exit__
    raise exc_value.with_traceback(exc_tb)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 896, in __connect
    self.dbapi_connection = connection = pool._invoke_creator(self)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\create.py", line 643, in connect
    return dialect.connect(*cargs, **cparams)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\default.py", line 620, in connect
    return self.loaded_dbapi.connect(*cargs, **cparams)
pyodbc.OperationalError: ('08001', '[08001] [Microsoft][ODBC SQL Server Driver][DBNETLIB]SQL Server 不存在或访问被拒绝 (17) (SQLDriverConnect); [08001] [Microsoft][ODBC SQL Server Driver][DBNETLIB]ConnectionOpen (Connect()). (2)')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 1473, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 882, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_cors\extension.py", line 194, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 878, in full_dispatch_request
    rv = self.preprocess_request()
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 1253, in preprocess_request
    rv = self.ensure_sync(before_func)()
  File "C:\StudentsCMSSP\app\utils\school_middleware.py", line 79, in check_school
    if not current_user.is_authenticated:
  File "C:\StudentsCMSSP\venv\lib\site-packages\werkzeug\local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "C:\StudentsCMSSP\venv\lib\site-packages\werkzeug\local.py", line 526, in _get_current_object
    return get_name(local())
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_login\utils.py", line 25, in <lambda>
    current_user = LocalProxy(lambda: _get_user())
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_login\utils.py", line 370, in _get_user
    current_app.login_manager._load_user()
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_login\login_manager.py", line 364, in _load_user
    user = self._user_callback(user_id)
  File "C:\StudentsCMSSP\app\models.py", line 284, in load_user
    return User.query.get(int(id))
  File "<string>", line 2, in get
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\util\deprecations.py", line 386, in warned
    return fn(*args, **kwargs)  # type: ignore[no-any-return]
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\query.py", line 1127, in get
    return self._get_impl(ident, loading.load_on_pk_identity)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\query.py", line 1136, in _get_impl
    return self.session._get_impl(
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\session.py", line 3862, in _get_impl
    return db_load_fn(
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\loading.py", line 694, in load_on_pk_identity
    session.execute(
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\session.py", line 2351, in execute
    return self._execute_internal(
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\session.py", line 2226, in _execute_internal
    conn = self._connection_for_bind(bind)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\session.py", line 2095, in _connection_for_bind
    return trans._connection_for_bind(engine, execution_options)
  File "<string>", line 2, in _connection_for_bind
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\state_changes.py", line 139, in _go
    ret_value = fn(self, *arg, **kw)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\session.py", line 1189, in _connection_for_bind
    conn = bind.connect()
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 3276, in connect
    return self._connection_cls(self)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 148, in __init__
    Connection._handle_dbapi_exception_noconnection(
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 2440, in _handle_dbapi_exception_noconnection
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 146, in __init__
    self._dbapi_connection = engine.raw_connection()
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 3300, in raw_connection
    return self.pool.connect()
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 449, in connect
    return _ConnectionFairy._checkout(self)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 1263, in _checkout
    fairy = _ConnectionRecord.checkout(pool)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 712, in checkout
    rec = pool._do_get()
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\impl.py", line 180, in _do_get
    self._dec_overflow()
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\util\langhelpers.py", line 146, in __exit__
    raise exc_value.with_traceback(exc_tb)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\impl.py", line 177, in _do_get
    return self._create_connection()
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 390, in _create_connection
    return _ConnectionRecord(self)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 674, in __init__
    self.__connect()
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 901, in __connect
    pool.logger.debug("Error on connect(): %s", e)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\util\langhelpers.py", line 146, in __exit__
    raise exc_value.with_traceback(exc_tb)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 896, in __connect
    self.dbapi_connection = connection = pool._invoke_creator(self)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\create.py", line 643, in connect
    return dialect.connect(*cargs, **cparams)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\default.py", line 620, in connect
    return self.loaded_dbapi.connect(*cargs, **cparams)
sqlalchemy.exc.OperationalError: (pyodbc.OperationalError) ('08001', '[08001] [Microsoft][ODBC SQL Server Driver][DBNETLIB]SQL Server 不存在或访问被拒绝 (17) (SQLDriverConnect); [08001] [Microsoft][ODBC SQL Server Driver][DBNETLIB]ConnectionOpen (Connect()). (2)')
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-22 21:14:57,254 ERROR: Exception on /weekly-menu-v2/plan [GET] [in C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py:838]
Traceback (most recent call last):
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 146, in __init__
    self._dbapi_connection = engine.raw_connection()
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 3300, in raw_connection
    return self.pool.connect()
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 449, in connect
    return _ConnectionFairy._checkout(self)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 1263, in _checkout
    fairy = _ConnectionRecord.checkout(pool)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 712, in checkout
    rec = pool._do_get()
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\impl.py", line 180, in _do_get
    self._dec_overflow()
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\util\langhelpers.py", line 146, in __exit__
    raise exc_value.with_traceback(exc_tb)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\impl.py", line 177, in _do_get
    return self._create_connection()
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 390, in _create_connection
    return _ConnectionRecord(self)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 674, in __init__
    self.__connect()
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 901, in __connect
    pool.logger.debug("Error on connect(): %s", e)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\util\langhelpers.py", line 146, in __exit__
    raise exc_value.with_traceback(exc_tb)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 896, in __connect
    self.dbapi_connection = connection = pool._invoke_creator(self)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\create.py", line 643, in connect
    return dialect.connect(*cargs, **cparams)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\default.py", line 620, in connect
    return self.loaded_dbapi.connect(*cargs, **cparams)
pyodbc.OperationalError: ('08001', '[08001] [Microsoft][ODBC SQL Server Driver][DBNETLIB]SQL Server 不存在或访问被拒绝 (17) (SQLDriverConnect); [08001] [Microsoft][ODBC SQL Server Driver][DBNETLIB]ConnectionOpen (Connect()). (2)')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 1473, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 882, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_cors\extension.py", line 194, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 878, in full_dispatch_request
    rv = self.preprocess_request()
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 1253, in preprocess_request
    rv = self.ensure_sync(before_func)()
  File "C:\StudentsCMSSP\app\utils\school_middleware.py", line 79, in check_school
    if not current_user.is_authenticated:
  File "C:\StudentsCMSSP\venv\lib\site-packages\werkzeug\local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "C:\StudentsCMSSP\venv\lib\site-packages\werkzeug\local.py", line 526, in _get_current_object
    return get_name(local())
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_login\utils.py", line 25, in <lambda>
    current_user = LocalProxy(lambda: _get_user())
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_login\utils.py", line 370, in _get_user
    current_app.login_manager._load_user()
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_login\login_manager.py", line 364, in _load_user
    user = self._user_callback(user_id)
  File "C:\StudentsCMSSP\app\models.py", line 284, in load_user
    return User.query.get(int(id))
  File "<string>", line 2, in get
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\util\deprecations.py", line 386, in warned
    return fn(*args, **kwargs)  # type: ignore[no-any-return]
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\query.py", line 1127, in get
    return self._get_impl(ident, loading.load_on_pk_identity)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\query.py", line 1136, in _get_impl
    return self.session._get_impl(
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\session.py", line 3862, in _get_impl
    return db_load_fn(
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\loading.py", line 694, in load_on_pk_identity
    session.execute(
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\session.py", line 2351, in execute
    return self._execute_internal(
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\session.py", line 2226, in _execute_internal
    conn = self._connection_for_bind(bind)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\session.py", line 2095, in _connection_for_bind
    return trans._connection_for_bind(engine, execution_options)
  File "<string>", line 2, in _connection_for_bind
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\state_changes.py", line 139, in _go
    ret_value = fn(self, *arg, **kw)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\session.py", line 1189, in _connection_for_bind
    conn = bind.connect()
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 3276, in connect
    return self._connection_cls(self)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 148, in __init__
    Connection._handle_dbapi_exception_noconnection(
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 2440, in _handle_dbapi_exception_noconnection
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 146, in __init__
    self._dbapi_connection = engine.raw_connection()
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 3300, in raw_connection
    return self.pool.connect()
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 449, in connect
    return _ConnectionFairy._checkout(self)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 1263, in _checkout
    fairy = _ConnectionRecord.checkout(pool)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 712, in checkout
    rec = pool._do_get()
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\impl.py", line 180, in _do_get
    self._dec_overflow()
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\util\langhelpers.py", line 146, in __exit__
    raise exc_value.with_traceback(exc_tb)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\impl.py", line 177, in _do_get
    return self._create_connection()
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 390, in _create_connection
    return _ConnectionRecord(self)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 674, in __init__
    self.__connect()
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 901, in __connect
    pool.logger.debug("Error on connect(): %s", e)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\util\langhelpers.py", line 146, in __exit__
    raise exc_value.with_traceback(exc_tb)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 896, in __connect
    self.dbapi_connection = connection = pool._invoke_creator(self)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\create.py", line 643, in connect
    return dialect.connect(*cargs, **cparams)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\default.py", line 620, in connect
    return self.loaded_dbapi.connect(*cargs, **cparams)
sqlalchemy.exc.OperationalError: (pyodbc.OperationalError) ('08001', '[08001] [Microsoft][ODBC SQL Server Driver][DBNETLIB]SQL Server 不存在或访问被拒绝 (17) (SQLDriverConnect); [08001] [Microsoft][ODBC SQL Server Driver][DBNETLIB]ConnectionOpen (Connect()). (2)')
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-22 21:15:09,364 ERROR: Exception on /notifications/check [GET] [in C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py:838]
Traceback (most recent call last):
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 146, in __init__
    self._dbapi_connection = engine.raw_connection()
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 3300, in raw_connection
    return self.pool.connect()
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 449, in connect
    return _ConnectionFairy._checkout(self)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 1263, in _checkout
    fairy = _ConnectionRecord.checkout(pool)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 712, in checkout
    rec = pool._do_get()
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\impl.py", line 180, in _do_get
    self._dec_overflow()
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\util\langhelpers.py", line 146, in __exit__
    raise exc_value.with_traceback(exc_tb)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\impl.py", line 177, in _do_get
    return self._create_connection()
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 390, in _create_connection
    return _ConnectionRecord(self)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 674, in __init__
    self.__connect()
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 901, in __connect
    pool.logger.debug("Error on connect(): %s", e)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\util\langhelpers.py", line 146, in __exit__
    raise exc_value.with_traceback(exc_tb)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 896, in __connect
    self.dbapi_connection = connection = pool._invoke_creator(self)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\create.py", line 643, in connect
    return dialect.connect(*cargs, **cparams)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\default.py", line 620, in connect
    return self.loaded_dbapi.connect(*cargs, **cparams)
pyodbc.OperationalError: ('08001', '[08001] [Microsoft][ODBC SQL Server Driver][DBNETLIB]SQL Server 不存在或访问被拒绝 (17) (SQLDriverConnect); [08001] [Microsoft][ODBC SQL Server Driver][DBNETLIB]ConnectionOpen (Connect()). (2)')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 1473, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 882, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_cors\extension.py", line 194, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 878, in full_dispatch_request
    rv = self.preprocess_request()
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 1253, in preprocess_request
    rv = self.ensure_sync(before_func)()
  File "C:\StudentsCMSSP\app\utils\school_middleware.py", line 79, in check_school
    if not current_user.is_authenticated:
  File "C:\StudentsCMSSP\venv\lib\site-packages\werkzeug\local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "C:\StudentsCMSSP\venv\lib\site-packages\werkzeug\local.py", line 526, in _get_current_object
    return get_name(local())
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_login\utils.py", line 25, in <lambda>
    current_user = LocalProxy(lambda: _get_user())
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_login\utils.py", line 370, in _get_user
    current_app.login_manager._load_user()
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_login\login_manager.py", line 364, in _load_user
    user = self._user_callback(user_id)
  File "C:\StudentsCMSSP\app\models.py", line 284, in load_user
    return User.query.get(int(id))
  File "<string>", line 2, in get
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\util\deprecations.py", line 386, in warned
    return fn(*args, **kwargs)  # type: ignore[no-any-return]
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\query.py", line 1127, in get
    return self._get_impl(ident, loading.load_on_pk_identity)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\query.py", line 1136, in _get_impl
    return self.session._get_impl(
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\session.py", line 3862, in _get_impl
    return db_load_fn(
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\loading.py", line 694, in load_on_pk_identity
    session.execute(
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\session.py", line 2351, in execute
    return self._execute_internal(
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\session.py", line 2226, in _execute_internal
    conn = self._connection_for_bind(bind)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\session.py", line 2095, in _connection_for_bind
    return trans._connection_for_bind(engine, execution_options)
  File "<string>", line 2, in _connection_for_bind
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\state_changes.py", line 139, in _go
    ret_value = fn(self, *arg, **kw)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\session.py", line 1189, in _connection_for_bind
    conn = bind.connect()
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 3276, in connect
    return self._connection_cls(self)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 148, in __init__
    Connection._handle_dbapi_exception_noconnection(
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 2440, in _handle_dbapi_exception_noconnection
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 146, in __init__
    self._dbapi_connection = engine.raw_connection()
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 3300, in raw_connection
    return self.pool.connect()
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 449, in connect
    return _ConnectionFairy._checkout(self)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 1263, in _checkout
    fairy = _ConnectionRecord.checkout(pool)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 712, in checkout
    rec = pool._do_get()
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\impl.py", line 180, in _do_get
    self._dec_overflow()
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\util\langhelpers.py", line 146, in __exit__
    raise exc_value.with_traceback(exc_tb)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\impl.py", line 177, in _do_get
    return self._create_connection()
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 390, in _create_connection
    return _ConnectionRecord(self)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 674, in __init__
    self.__connect()
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 901, in __connect
    pool.logger.debug("Error on connect(): %s", e)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\util\langhelpers.py", line 146, in __exit__
    raise exc_value.with_traceback(exc_tb)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 896, in __connect
    self.dbapi_connection = connection = pool._invoke_creator(self)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\create.py", line 643, in connect
    return dialect.connect(*cargs, **cparams)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\default.py", line 620, in connect
    return self.loaded_dbapi.connect(*cargs, **cparams)
sqlalchemy.exc.OperationalError: (pyodbc.OperationalError) ('08001', '[08001] [Microsoft][ODBC SQL Server Driver][DBNETLIB]SQL Server 不存在或访问被拒绝 (17) (SQLDriverConnect); [08001] [Microsoft][ODBC SQL Server Driver][DBNETLIB]ConnectionOpen (Connect()). (2)')
(Background on this error at: https://sqlalche.me/e/20/e3q8)
