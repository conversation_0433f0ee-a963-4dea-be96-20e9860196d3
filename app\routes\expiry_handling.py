"""
过期食材处理路由
"""
from flask import Blueprint, request, jsonify, render_template, redirect, url_for, flash
from flask_login import login_required, current_user
from datetime import datetime, date
from app import db
from app.models import Inventory, ExpiryHandling, ExpiryNotification, User, Supplier
from app.utils.helpers import format_datetime
import logging

# 创建蓝图
expiry_handling_bp = Blueprint('expiry_handling', __name__, url_prefix='/expiry-handling')

@expiry_handling_bp.route('/')
@login_required
def index():
    """过期处理记录列表"""
    try:
        # 获取查询参数
        page = request.args.get('page', 1, type=int)
        per_page = 20
        status = request.args.get('status', '')
        handling_type = request.args.get('handling_type', '')
        
        # 构建查询
        query = ExpiryHandling.query
        
        # 学校级数据隔离
        if hasattr(current_user, 'area_id') and current_user.area_id:
            query = query.filter(ExpiryHandling.area_id == current_user.area_id)
        
        # 状态筛选
        if status:
            query = query.filter(ExpiryHandling.handling_status == status)
            
        # 处理类型筛选
        if handling_type:
            query = query.filter(ExpiryHandling.handling_type == handling_type)
        
        # 排序和分页
        query = query.order_by(ExpiryHandling.created_at.desc())
        pagination = query.paginate(page=page, per_page=per_page, error_out=False)
        handlings = pagination.items
        
        return render_template('expiry_handling/index.html',
                             handlings=handlings,
                             pagination=pagination,
                             status=status,
                             handling_type=handling_type)
                             
    except Exception as e:
        logging.error(f"获取过期处理记录失败: {str(e)}")
        flash('获取过期处理记录失败', 'error')
        return redirect(url_for('inventory.check_expiry'))

@expiry_handling_bp.route('/create', methods=['POST'])
@login_required
def create():
    """创建过期处理记录"""
    try:
        data = request.get_json()
        
        # 验证必需字段
        required_fields = ['inventory_id', 'handling_type', 'quantity', 'planned_date']
        for field in required_fields:
            if field not in data or not data[field]:
                return jsonify({'success': False, 'message': f'缺少必需字段: {field}'}), 400
        
        # 获取库存记录
        inventory = Inventory.query.get(data['inventory_id'])
        if not inventory:
            return jsonify({'success': False, 'message': '库存记录不存在'}), 404
        
        # 验证处理数量
        if data['quantity'] > inventory.quantity:
            return jsonify({'success': False, 'message': '处理数量不能超过库存数量'}), 400
        
        # 创建处理记录
        handling = ExpiryHandling(
            inventory_id=data['inventory_id'],
            handling_type=data['handling_type'],
            quantity=data['quantity'],
            unit=inventory.unit,
            planned_date=datetime.strptime(data['planned_date'], '%Y-%m-%d').date(),
            operator_id=current_user.id,
            reason=data.get('reason', ''),
            notes=data.get('notes', ''),
            area_id=getattr(current_user, 'area_id', None)
        )
        
        # 根据处理类型设置特定字段
        if data['handling_type'] == '退货':
            handling.return_to_supplier = True
        elif data['handling_type'] == '转移':
            handling.transfer_to = data.get('transfer_to', '')
            handling.transfer_purpose = data.get('transfer_purpose', '')
        
        # 计算损失金额（如果有单价信息）
        if data.get('unit_cost'):
            handling.unit_cost = data['unit_cost']
            handling.calculate_total_loss()
        
        db.session.add(handling)
        db.session.commit()
        
        # 创建通知
        create_handling_notification(handling)
        
        return jsonify({
            'success': True, 
            'message': '过期处理记录创建成功',
            'handling_id': handling.id
        })
        
    except Exception as e:
        db.session.rollback()
        logging.error(f"创建过期处理记录失败: {str(e)}")
        return jsonify({'success': False, 'message': f'创建失败: {str(e)}'}), 500

@expiry_handling_bp.route('/<int:handling_id>/update-status', methods=['POST'])
@login_required
def update_status(handling_id):
    """更新处理状态"""
    try:
        data = request.get_json()
        new_status = data.get('status')
        
        if not new_status:
            return jsonify({'success': False, 'message': '缺少状态参数'}), 400
        
        handling = ExpiryHandling.query.get_or_404(handling_id)
        
        # 权限检查
        if hasattr(current_user, 'area_id') and current_user.area_id:
            if handling.area_id != current_user.area_id:
                return jsonify({'success': False, 'message': '无权限操作此记录'}), 403
        
        old_status = handling.handling_status
        handling.handling_status = new_status
        
        # 如果状态变为已完成，设置处理日期
        if new_status == '已完成' and not handling.handling_date:
            handling.handling_date = date.today()
            
            # 更新库存状态
            if handling.handling_type == '报废':
                # 减少库存数量
                handling.inventory.quantity -= handling.quantity
                if handling.inventory.quantity <= 0:
                    handling.inventory.status = '已报废'
            elif handling.handling_type == '转移':
                # 减少库存数量
                handling.inventory.quantity -= handling.quantity
                if handling.inventory.quantity <= 0:
                    handling.inventory.status = '已转移'
        
        # 如果需要审批
        if new_status == '已完成' and data.get('approval_notes'):
            handling.approval_notes = data['approval_notes']
            handling.approver_id = current_user.id
            handling.approved_at = datetime.now()
        
        db.session.commit()
        
        return jsonify({
            'success': True, 
            'message': f'状态已从"{old_status}"更新为"{new_status}"'
        })
        
    except Exception as e:
        db.session.rollback()
        logging.error(f"更新处理状态失败: {str(e)}")
        return jsonify({'success': False, 'message': f'更新失败: {str(e)}'}), 500

@expiry_handling_bp.route('/<int:handling_id>')
@login_required
def view(handling_id):
    """查看处理记录详情"""
    try:
        handling = ExpiryHandling.query.get_or_404(handling_id)
        
        # 权限检查
        if hasattr(current_user, 'area_id') and current_user.area_id:
            if handling.area_id != current_user.area_id:
                flash('无权限查看此记录', 'error')
                return redirect(url_for('expiry_handling.index'))
        
        return render_template('expiry_handling/view.html', handling=handling)
        
    except Exception as e:
        logging.error(f"查看处理记录失败: {str(e)}")
        flash('查看处理记录失败', 'error')
        return redirect(url_for('expiry_handling.index'))

def create_handling_notification(handling):
    """创建处理通知"""
    try:
        # 确定通知级别
        if handling.handling_type == '报废':
            level = '高'
            message = f"食材 {handling.inventory.ingredient.name} (批次: {handling.inventory.batch_number}) 已标记为报废处理"
        elif handling.handling_type == '退货':
            level = '中'
            message = f"食材 {handling.inventory.ingredient.name} (批次: {handling.inventory.batch_number}) 已申请退货处理"
        elif handling.handling_type == '转移':
            level = '中'
            message = f"食材 {handling.inventory.ingredient.name} (批次: {handling.inventory.batch_number}) 已申请转移处理"
        else:
            level = '低'
            message = f"食材 {handling.inventory.ingredient.name} (批次: {handling.inventory.batch_number}) 已创建处理记录"
        
        # 创建通知给管理员
        # 这里可以根据实际需求确定通知接收人
        notification = ExpiryNotification(
            inventory_id=handling.inventory_id,
            notification_type='处理提醒',
            notification_level=level,
            message=message,
            recipient_id=handling.operator_id,  # 暂时发给操作人，实际应该发给管理员
            handling_id=handling.id,
            area_id=handling.area_id
        )
        
        db.session.add(notification)
        db.session.commit()
        
    except Exception as e:
        logging.error(f"创建处理通知失败: {str(e)}")

@expiry_handling_bp.route('/batch-create', methods=['POST'])
@login_required
def batch_create():
    """批量创建处理记录"""
    try:
        data = request.get_json()
        inventory_ids = data.get('inventory_ids', [])
        handling_type = data.get('handling_type')
        planned_date = data.get('planned_date')
        reason = data.get('reason', '')
        
        if not inventory_ids or not handling_type or not planned_date:
            return jsonify({'success': False, 'message': '缺少必需参数'}), 400
        
        created_count = 0
        errors = []
        
        for inventory_id in inventory_ids:
            try:
                inventory = Inventory.query.get(inventory_id)
                if not inventory:
                    errors.append(f"库存ID {inventory_id} 不存在")
                    continue
                
                handling = ExpiryHandling(
                    inventory_id=inventory_id,
                    handling_type=handling_type,
                    quantity=inventory.quantity,  # 默认处理全部数量
                    unit=inventory.unit,
                    planned_date=datetime.strptime(planned_date, '%Y-%m-%d').date(),
                    operator_id=current_user.id,
                    reason=reason,
                    area_id=getattr(current_user, 'area_id', None)
                )
                
                db.session.add(handling)
                created_count += 1
                
            except Exception as e:
                errors.append(f"处理库存ID {inventory_id} 失败: {str(e)}")
        
        db.session.commit()
        
        message = f"成功创建 {created_count} 条处理记录"
        if errors:
            message += f"，{len(errors)} 条失败"
        
        return jsonify({
            'success': True,
            'message': message,
            'created_count': created_count,
            'errors': errors
        })

    except Exception as e:
        db.session.rollback()
        logging.error(f"批量创建处理记录失败: {str(e)}")
        return jsonify({'success': False, 'message': f'批量创建失败: {str(e)}'}), 500

@expiry_handling_bp.route('/automation/status')
@login_required
def automation_status():
    """查看自动化任务状态"""
    try:
        from app.services.scheduler_service import get_scheduler
        scheduler = get_scheduler()

        # 获取任务状态
        task_status = scheduler.get_task_status()

        # 获取最近的自动化执行记录
        recent_notifications = ExpiryNotification.query.filter(
            ExpiryNotification.notification_type == '处理提醒'
        ).order_by(ExpiryNotification.created_at.desc()).limit(10).all()

        return render_template('expiry_handling/automation_status.html',
                             task_status=task_status,
                             recent_notifications=recent_notifications,
                             scheduler_running=scheduler.running)

    except Exception as e:
        logging.error(f"获取自动化状态失败: {str(e)}")
        flash('获取自动化状态失败', 'error')
        return redirect(url_for('expiry_handling.index'))

@expiry_handling_bp.route('/automation/run-now', methods=['POST'])
@login_required
def run_automation_now():
    """立即执行自动化任务"""
    try:
        from app.services.expiry_automation_service import expiry_automation_service

        result = expiry_automation_service.run_daily_automation()

        if result['success']:
            flash(f'自动化任务执行成功: 过期状态更新{result["expired_count"]}条, '
                  f'生成通知{result["notification_count"]}条', 'success')
        else:
            flash(f'自动化任务执行失败: {result["error"]}', 'error')

        return redirect(url_for('expiry_handling.automation_status'))

    except Exception as e:
        logging.error(f"手动执行自动化任务失败: {str(e)}")
        flash('执行自动化任务失败', 'error')
        return redirect(url_for('expiry_handling.automation_status'))

@expiry_handling_bp.route('/loss-report')
@login_required
def loss_report():
    """损失报告页面"""
    try:
        # 获取查询参数
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')

        # 构建查询
        query = ExpiryHandling.query.filter(
            ExpiryHandling.handling_status == '已完成'
        )

        # 学校级数据隔离
        if hasattr(current_user, 'area_id') and current_user.area_id:
            query = query.filter(ExpiryHandling.area_id == current_user.area_id)

        # 日期筛选
        if start_date:
            query = query.filter(ExpiryHandling.handling_date >= start_date)
        if end_date:
            query = query.filter(ExpiryHandling.handling_date <= end_date)

        handlings = query.order_by(ExpiryHandling.handling_date.desc()).all()

        # 计算统计数据
        total_loss = sum(h.total_loss or 0 for h in handlings)
        total_quantity = sum(h.quantity for h in handlings)

        # 按类型分组统计
        type_stats = {}
        for handling in handlings:
            if handling.handling_type not in type_stats:
                type_stats[handling.handling_type] = {
                    'count': 0,
                    'quantity': 0,
                    'loss': 0
                }
            type_stats[handling.handling_type]['count'] += 1
            type_stats[handling.handling_type]['quantity'] += handling.quantity
            type_stats[handling.handling_type]['loss'] += handling.total_loss or 0

        return render_template('expiry_handling/loss_report.html',
                             handlings=handlings,
                             total_loss=total_loss,
                             total_quantity=total_quantity,
                             type_stats=type_stats,
                             start_date=start_date,
                             end_date=end_date)

    except Exception as e:
        logging.error(f"获取损失报告失败: {str(e)}")
        flash('获取损失报告失败', 'error')
        return redirect(url_for('expiry_handling.index'))
