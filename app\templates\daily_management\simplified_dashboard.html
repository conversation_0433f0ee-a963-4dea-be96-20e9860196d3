{% extends "base.html" %}

{% block title %}日常管理{% endblock %}

{% from 'daily_management/components/qrcode_card_widget.html' import qrcode_card_widget %}

{% block styles %}
{{ super() }}
<style nonce="{{ csp_nonce }}">
    .overview-card {
        border-radius: 10px;
        box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
        transition: transform 0.3s;
    }

    .overview-card:hover {
        transform: translateY(-5px);
    }

    .overview-card .title {
        font-size: 0.9rem;
        font-weight: 600;
        color: rgba(255, 255, 255, 0.8);
    }

    .overview-card .count {
        font-size: 1.8rem;
        font-weight: 700;
        margin-top: 5px;
    }

    .overview-card .icon {
        font-size: 2rem;
        opacity: 0.8;
    }

    .feature-card {
        transition: transform 0.3s;
        height: 100%;
    }

    .feature-card:hover {
        transform: translateY(-5px);
    }

    .icon-box {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        margin-bottom: 15px;
    }

    .status-badge {
        font-size: 0.7rem;
        padding: 0.25rem 0.5rem;
        border-radius: 10px;
        font-weight: 600;
    }

    .status-badge-success {
        background-color: rgba(40, 167, 69, 0.1);
        color: #28a745;
    }

    .status-badge-warning {
        background-color: rgba(255, 193, 7, 0.1);
        color: #ffc107;
    }

    .status-badge-info {
        background-color: rgba(23, 162, 184, 0.1);
        color: #17a2b8;
    }

    .status-badge-danger {
        background-color: rgba(220, 53, 69, 0.1);
        color: #dc3545;
    }

    .issue-list {
        max-height: 400px;
        overflow-y: auto;
    }

    .issue-item {
        padding: 10px;
        border-radius: 5px;
        margin-bottom: 10px;
        border-left: 4px solid #6c757d;
        background-color: #f8f9fa;
    }

    .issue-item.priority-high {
        border-left-color: #dc3545;
    }

    .issue-item.priority-medium {
        border-left-color: #ffc107;
    }

    .issue-item.priority-low {
        border-left-color: #28a745;
    }

    .issue-title {
        font-weight: 600;
        margin-bottom: 5px;
    }

    .issue-meta {
        font-size: 0.8rem;
        color: #6c757d;
    }

    .chart-container {
        position: relative;
        margin: auto;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">日常管理</h1>
        <div class="btn-group">
            <div class="dropdown">
                <button class="btn btn-success dropdown-toggle" type="button" data-bs-toggle="dropdown">
                    <i class="fas fa-download"></i> 导出报表
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="#" onclick="showDailyExportModal('inspection')">
                        <i class="fas fa-clipboard-check text-primary"></i> 检查记录
                    </a></li>
                    <li><a class="dropdown-item" href="#" onclick="showDailyExportModal('companion')">
                        <i class="fas fa-utensils text-success"></i> 陪餐记录
                    </a></li>
                    <li><a class="dropdown-item" href="#" onclick="showDailyExportModal('training')">
                        <i class="fas fa-graduation-cap text-info"></i> 培训记录
                    </a></li>
                    <li><a class="dropdown-item" href="#" onclick="showDailyExportModal('issues')">
                        <i class="fas fa-exclamation-circle text-warning"></i> 问题记录
                    </a></li>
                </ul>
            </div>
            <a href="{{ url_for('daily_management.print_daily_summary', date=today.strftime('%Y-%m-%d')) if today_log else '#' }}"
               class="btn btn-primary print-btn {{ 'disabled' if not today_log else '' }}"
               {{ 'disabled' if not today_log else '' }}>
                <i class="fas fa-print"></i> 打印今日汇总
            </a>
        </div>
    </div>

    <!-- 日常管理中心卡片 -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-calendar-alt mr-1"></i> 日常管理中心
            </h6>
        </div>
        <div class="card-body">
            <div class="text-center mb-4">
                <a href="{{ url_for('daily_management.edit_log', date_str=today.strftime('%Y-%m-%d')) }}" class="btn btn-primary btn-lg">
                    <i class="fas fa-calendar-day mr-1"></i> 进入今日日志
                </a>
                <p class="text-muted mt-2">管理日志、检查记录、陪餐记录等所有日常工作</p>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="card bg-light mb-3">
                        <div class="card-body">
                            <h5 class="card-title"><i class="fas fa-star text-warning mr-1"></i> 推荐使用</h5>
                            <p class="card-text">日常管理中心提供了更直观的日期导航体验，所有内容集中在一个页面，减少了页面跳转，让您可以看到整体情况。</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card bg-light mb-3">
                        <div class="card-body">
                            <h5 class="card-title"><i class="fas fa-magic text-info mr-1"></i> 自动创建日志</h5>
                            <p class="card-text">无需手动创建日志，系统会自动为您处理，让您专注于记录重要内容。</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 二维码快速访问区域 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-qrcode mr-1"></i> 二维码快速访问
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- 检查记录二维码 -->
                        <div class="col-lg-6 mb-3">
                            {% if inspection_qrcode_data and inspection_qrcode_data.upload_qrcode_base64 %}
                            {{ qrcode_card_widget(
                                title="检查记录二维码",
                                description="员工扫码上传检查照片，管理员扫码评分",
                                qrcode_base64=inspection_qrcode_data.upload_qrcode_base64,
                                detail_url=inspection_qrcode_data.detail_url,
                                icon="fas fa-clipboard-check",
                                color_theme="info",
                                quick_url=inspection_qrcode_data.upload_url
                            ) }}
                            {% else %}
                            <div class="card border-left-info shadow h-100">
                                <div class="card-header bg-info text-white py-2">
                                    <h6 class="m-0 font-weight-bold">
                                        <i class="fas fa-clipboard-check mr-2"></i>检查记录二维码
                                    </h6>
                                </div>
                                <div class="card-body p-3 text-center">
                                    <div class="text-muted">
                                        <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                                        <p>二维码生成失败</p>
                                        <a href="{{ url_for('daily_management.generate_fixed_inspection_qrcode') }}" class="btn btn-outline-info btn-sm">
                                            <i class="fas fa-external-link-alt mr-1"></i>查看详情
                                        </a>
                                    </div>
                                </div>
                            </div>
                            {% endif %}
                        </div>

                        <!-- 陪餐记录二维码 -->
                        <div class="col-lg-6 mb-3">
                            {% if companion_qrcode_data and companion_qrcode_data.entry_qrcode_base64 %}
                            {{ qrcode_card_widget(
                                title="陪餐记录二维码",
                                description="陪餐人员扫码填写陪餐记录和评价",
                                qrcode_base64=companion_qrcode_data.entry_qrcode_base64,
                                detail_url=companion_qrcode_data.detail_url,
                                icon="fas fa-users",
                                color_theme="success",
                                quick_url=companion_qrcode_data.entry_url
                            ) }}
                            {% else %}
                            <div class="card border-left-success shadow h-100">
                                <div class="card-header bg-success text-white py-2">
                                    <h6 class="m-0 font-weight-bold">
                                        <i class="fas fa-users mr-2"></i>陪餐记录二维码
                                    </h6>
                                </div>
                                <div class="card-body p-3 text-center">
                                    <div class="text-muted">
                                        <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                                        <p>二维码生成失败</p>
                                        <a href="{{ url_for('daily_management.school_qrcode') }}" class="btn btn-outline-success btn-sm">
                                            <i class="fas fa-external-link-alt mr-1"></i>查看详情
                                        </a>
                                    </div>
                                </div>
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- 使用说明 -->
                    <div class="alert alert-info mt-3">
                        <h6><i class="fas fa-info-circle mr-2"></i>使用说明</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <p class="mb-1"><strong>检查记录二维码：</strong></p>
                                <ul class="mb-0 small">
                                    <li>员工扫码上传检查照片</li>
                                    <li>管理员扫码对照片评分</li>
                                    <li>支持匿名访问，无需登录</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <p class="mb-1"><strong>陪餐记录二维码：</strong></p>
                                <ul class="mb-0 small">
                                    <li>陪餐人员扫码填写记录</li>
                                    <li>支持评分和意见反馈</li>
                                    <li>自动关联当日工作日志</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 日常管理导出模态框 -->
    <div class="modal fade" id="dailyExportModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="dailyExportTitle">导出设置</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="dailyExportForm">
                        <div class="row">
                            <div class="col-md-6">
                                <label class="form-label">开始日期</label>
                                <input type="date" class="form-control" id="startDate"
                                       value="{{ (today - timedelta(days=30)).strftime('%Y-%m-%d') }}">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">结束日期</label>
                                <input type="date" class="form-control" id="endDate"
                                       value="{{ today.strftime('%Y-%m-%d') }}">
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="confirmDailyExport()">确认导出</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 导出进度提示 -->
    <div class="modal fade" id="exportProgressModal" tabindex="-1" data-bs-backdrop="static">
        <div class="modal-dialog modal-sm">
            <div class="modal-content">
                <div class="modal-body text-center">
                    <div class="spinner-border text-primary mb-3" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mb-0">正在生成导出文件...</p>
                    <small class="text-muted">请稍候，不要关闭页面</small>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script nonce="{{ csp_nonce }}">
    // 全局导出变量
    let currentExportType = '';

    $(document).ready(function() {
        // 初始化提示工具
        $('[data-toggle="tooltip"]').tooltip();
    });

    // 日常管理导出功能
    function showDailyExportModal(exportType) {
        currentExportType = exportType;

        const titles = {
            'inspection': '导出检查记录',
            'companion': '导出陪餐记录',
            'training': '导出培训记录',
            'issues': '导出问题记录'
        };

        document.getElementById('dailyExportTitle').textContent = titles[exportType] || '导出设置';

        const modal = new bootstrap.Modal(document.getElementById('dailyExportModal'));
        modal.show();
    }

    function confirmDailyExport() {
        showExportProgress();

        const startDate = document.getElementById('startDate').value;
        const endDate = document.getElementById('endDate').value;

        const params = new URLSearchParams({
            start_date: startDate,
            end_date: endDate
        });

        const link = document.createElement('a');
        link.href = `/daily-management/export/${currentExportType}?${params.toString()}`;
        link.style.display = 'none';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // 隐藏模态框
        bootstrap.Modal.getInstance(document.getElementById('dailyExportModal')).hide();

        setTimeout(() => {
            hideExportProgress();
            toastr.success('导出完成');
        }, 2000);
    }

    // 通用进度提示功能
    function showExportProgress() {
        const modal = new bootstrap.Modal(document.getElementById('exportProgressModal'));
        modal.show();
    }

    function hideExportProgress() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('exportProgressModal'));
        if (modal) {
            modal.hide();
        }
    }
</script>
{% endblock %}
