"""
统一导出服务
整合专业模板系统，提供标准化的导出接口
"""

from flask import current_app, send_file, jsonify
from datetime import datetime
import os
import tempfile
import pandas as pd
from openpyxl import Workbook
from openpyxl.styles import Font, Alignment, Border, Side, PatternFill
from reportlab.lib.pagesizes import A4
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph
from reportlab.lib import colors
from app.utils.professional_template_manager import get_template_manager

class UnifiedExportService:
    """统一导出服务"""

    def __init__(self):
        self.export_dirs = {}
        self.setup_templates()

    def setup_export_directories(self):
        """设置导出目录"""
        if current_app:
            self.export_dirs = {
                'excel': os.path.join(current_app.root_path, 'static', 'excel'),
                'pdf': os.path.join(current_app.root_path, 'static', 'pdf'),
                'templates': os.path.join(current_app.root_path, 'static', 'export_templates')
            }

            for dir_path in self.export_dirs.values():
                if not os.path.exists(dir_path):
                    os.makedirs(dir_path)
        else:
            # 在没有应用上下文时使用默认路径
            self.export_dirs = {
                'excel': 'app/static/excel',
                'pdf': 'app/static/pdf',
                'templates': 'app/static/export_templates'
            }
    
    def setup_templates(self):
        """设置导出模板"""
        self.templates = {
            'supplier_list': {
                'headers': ['供应商名称', '联系人', '电话', '地址', '邮箱', '状态', '创建时间'],
                'columns': ['name', 'contact_person', 'phone', 'address', 'email', 'status', 'created_at'],
                'title': '供应商信息列表'
            },
            'inventory_list': {
                'headers': ['食材名称', '当前库存', '单位', '存储位置', '批次号', '生产日期', '过期日期', '供应商'],
                'columns': ['ingredient_name', 'current_stock', 'unit', 'storage_location', 'batch_number', 'production_date', 'expiry_date', 'supplier_name'],
                'title': '库存清单'
            },
            'daily_inspection': {
                'headers': ['检查日期', '检查类型', '检查项目', '检查结果', '问题描述', '整改措施', '检查人'],
                'columns': ['inspection_date', 'inspection_type', 'inspection_item', 'result', 'issue_description', 'corrective_action', 'inspector'],
                'title': '日常检查记录'
            },
            'companion_dining': {
                'headers': ['陪餐日期', '陪餐人', '餐次', '菜品评价', '问题反馈', '建议'],
                'columns': ['dining_date', 'companion_name', 'meal_type', 'food_rating', 'feedback', 'suggestions'],
                'title': '陪餐记录'
            },
            'training_records': {
                'headers': ['培训日期', '培训主题', '培训类型', '参与人员', '培训时长', '培训效果', '备注'],
                'columns': ['training_date', 'training_topic', 'training_type', 'participants', 'duration', 'effectiveness', 'notes'],
                'title': '培训记录'
            },
            'employee_list': {
                'headers': ['姓名', '工号', '职位', '部门', '电话', '邮箱', '入职日期', '状态', '健康证到期日期'],
                'columns': ['name', 'employee_id', 'position', 'department', 'phone', 'email', 'hire_date', 'status', 'health_certificate_expiry'],
                'title': '员工信息列表'
            }
        }
    
    def export_to_excel(self, data, template_name, filename=None, user_area=None):
        """导出数据到Excel"""
        try:
            # 确保导出目录已设置
            if not self.export_dirs:
                self.setup_export_directories()

            template = self.templates.get(template_name)
            if not template:
                raise ValueError(f"未找到模板: {template_name}")

            # 生成文件名
            if not filename:
                timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
                filename = f"{template['title']}_{timestamp}.xlsx"

            # 创建临时文件
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.xlsx')
            temp_file.close()
            
            # 创建工作簿
            wb = Workbook()
            ws = wb.active
            ws.title = template['title']
            
            # 设置标题
            if user_area:
                title = f"{user_area.name} - {template['title']}"
                ws.merge_cells('A1:' + chr(65 + len(template['headers']) - 1) + '1')
                ws['A1'] = title
                ws['A1'].font = Font(name='宋体', size=16, bold=True)
                ws['A1'].alignment = Alignment(horizontal='center', vertical='center')
                start_row = 3
            else:
                start_row = 1
            
            # 设置表头
            for col, header in enumerate(template['headers'], 1):
                cell = ws.cell(row=start_row, column=col, value=header)
                cell.font = Font(name='宋体', size=12, bold=True)
                cell.alignment = Alignment(horizontal='center', vertical='center')
                cell.fill = PatternFill(start_color='D9D9D9', end_color='D9D9D9', fill_type='solid')
                cell.border = Border(
                    left=Side(style='thin'),
                    right=Side(style='thin'),
                    top=Side(style='thin'),
                    bottom=Side(style='thin')
                )
            
            # 填充数据
            for row_idx, item in enumerate(data, start_row + 1):
                for col_idx, column in enumerate(template['columns'], 1):
                    value = self._get_nested_value(item, column)
                    cell = ws.cell(row=row_idx, column=col_idx, value=value)
                    cell.font = Font(name='宋体', size=10)
                    cell.alignment = Alignment(horizontal='left', vertical='center')
                    cell.border = Border(
                        left=Side(style='thin'),
                        right=Side(style='thin'),
                        top=Side(style='thin'),
                        bottom=Side(style='thin')
                    )
            
            # 自动调整列宽
            for column in ws.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)
                ws.column_dimensions[column_letter].width = adjusted_width
            
            # 保存文件
            wb.save(temp_file.name)
            
            return send_file(
                temp_file.name,
                as_attachment=True,
                download_name=filename,
                mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            )
            
        except Exception as e:
            current_app.logger.error(f"Excel导出失败: {str(e)}")
            return jsonify({'success': False, 'message': f'导出失败: {str(e)}'}), 500
    
    def _get_nested_value(self, obj, key):
        """获取嵌套对象的值"""
        try:
            if hasattr(obj, key):
                value = getattr(obj, key)
            elif isinstance(obj, dict):
                value = obj.get(key)
            else:
                return ""
            
            # 格式化特殊类型
            if hasattr(value, 'strftime'):  # 日期时间类型
                return value.strftime('%Y-%m-%d %H:%M:%S')
            elif value is None:
                return ""
            else:
                return str(value)
        except:
            return ""

# 供应商导出功能实现
def export_suppliers_excel(suppliers, user_area):
    """导出供应商Excel - 使用专业模板"""
    template_manager = get_template_manager()
    return template_manager.generate_supplier_list_excel(suppliers, user_area)

# 库存导出功能实现
def export_inventory_excel(inventories, user_area):
    """导出库存Excel - 使用专业模板"""
    template_manager = get_template_manager()
    return template_manager.generate_inventory_list_excel(inventories, user_area)

# 日常管理导出功能实现
def export_inspection_records_excel(records, user_area, start_date, end_date):
    """导出检查记录Excel - 使用专业模板"""
    template_manager = get_template_manager()
    return template_manager.generate_inspection_records_excel(records, user_area, start_date, end_date)

def export_companion_records_excel(records, user_area, start_date, end_date):
    """导出陪餐记录Excel - 使用专业模板"""
    template_manager = get_template_manager()
    return template_manager.generate_companion_dining_excel(records, user_area, start_date, end_date)

# 员工导出功能实现
def export_employees_excel(employees, user_area):
    """导出员工Excel - 使用专业模板"""
    template_manager = get_template_manager()
    return template_manager.generate_employee_list_excel(employees, user_area)

# 全局导出服务实例 - 延迟初始化
export_service = None

def get_export_service():
    """获取导出服务实例"""
    global export_service
    if export_service is None:
        export_service = UnifiedExportService()
    return export_service
