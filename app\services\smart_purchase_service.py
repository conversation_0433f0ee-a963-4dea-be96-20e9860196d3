"""
智能采购建议服务
基于历史消耗数据和过期率，为采购订单提供智能数量建议
"""
from datetime import datetime, date, timedelta
from app import db
from app.models import (
    Inventory, Ingredient, ConsumptionPlan, ConsumptionDetail, 
    StockIn, StockInItem, StockOut, StockOutItem, ExpiryHandling
)
from sqlalchemy import and_, or_, func, desc
import logging

logger = logging.getLogger(__name__)

class SmartPurchaseService:
    """智能采购建议服务"""
    
    def __init__(self):
        self.analysis_days = 90  # 分析最近90天的数据
        self.safety_stock_ratio = 0.2  # 安全库存比例
        self.max_purchase_days = 30  # 最大采购天数
    
    def get_purchase_suggestions(self, area_id=None, ingredient_ids=None):
        """获取采购建议"""
        try:
            suggestions = []
            
            # 获取需要分析的食材
            if ingredient_ids:
                ingredients = Ingredient.query.filter(Ingredient.id.in_(ingredient_ids)).all()
            else:
                # 获取有消耗记录的食材
                ingredients = self._get_active_ingredients(area_id)
            
            for ingredient in ingredients:
                suggestion = self._analyze_ingredient_purchase(ingredient, area_id)
                if suggestion:
                    suggestions.append(suggestion)
            
            # 按优先级排序
            suggestions.sort(key=lambda x: x['priority_score'], reverse=True)
            
            return suggestions
            
        except Exception as e:
            logger.error(f"获取采购建议失败: {str(e)}")
            return []
    
    def _get_active_ingredients(self, area_id=None):
        """获取有活跃消耗的食材"""
        try:
            # 获取最近90天有消耗记录的食材
            start_date = date.today() - timedelta(days=self.analysis_days)
            
            query = db.session.query(Ingredient).join(ConsumptionDetail).join(ConsumptionPlan).filter(
                and_(
                    ConsumptionPlan.plan_date >= start_date,
                    ConsumptionDetail.actual_quantity > 0
                )
            )
            
            if area_id:
                query = query.filter(ConsumptionPlan.area_id == area_id)
            
            return query.distinct().all()
            
        except Exception as e:
            logger.error(f"获取活跃食材失败: {str(e)}")
            return []
    
    def _analyze_ingredient_purchase(self, ingredient, area_id=None):
        """分析单个食材的采购建议"""
        try:
            # 获取当前库存
            current_stock = self._get_current_stock(ingredient.id, area_id)
            
            # 计算历史消耗数据
            consumption_data = self._calculate_consumption_stats(ingredient.id, area_id)
            
            # 计算过期率
            expiry_rate = self._calculate_expiry_rate(ingredient.id, area_id)
            
            # 计算采购建议
            suggestion = self._calculate_purchase_suggestion(
                ingredient, current_stock, consumption_data, expiry_rate
            )
            
            if suggestion:
                suggestion.update({
                    'ingredient_id': ingredient.id,
                    'ingredient_name': ingredient.name,
                    'category_name': ingredient.category.name if ingredient.category else '其他',
                    'current_stock': current_stock,
                    'consumption_data': consumption_data,
                    'expiry_rate': expiry_rate
                })
            
            return suggestion
            
        except Exception as e:
            logger.error(f"分析食材采购建议失败: {str(e)}")
            return None
    
    def _get_current_stock(self, ingredient_id, area_id=None):
        """获取当前库存信息"""
        try:
            query = Inventory.query.filter(
                and_(
                    Inventory.ingredient_id == ingredient_id,
                    Inventory.quantity > 0,
                    Inventory.status.in_(['正常', '优先使用'])
                )
            )
            
            if area_id:
                query = query.join(Inventory.warehouse_info).filter(
                    Inventory.warehouse_info.has(area_id=area_id)
                )
            
            inventories = query.all()
            
            total_quantity = sum(inv.quantity for inv in inventories)
            
            # 计算平均剩余天数
            today = date.today()
            valid_inventories = [inv for inv in inventories if inv.expiry_date >= today]
            
            if valid_inventories:
                avg_days_remaining = sum(
                    (inv.expiry_date - today).days for inv in valid_inventories
                ) / len(valid_inventories)
            else:
                avg_days_remaining = 0
            
            return {
                'total_quantity': total_quantity,
                'batch_count': len(inventories),
                'avg_days_remaining': avg_days_remaining,
                'expired_quantity': sum(
                    inv.quantity for inv in inventories 
                    if inv.expiry_date < today
                )
            }
            
        except Exception as e:
            logger.error(f"获取当前库存失败: {str(e)}")
            return {
                'total_quantity': 0,
                'batch_count': 0,
                'avg_days_remaining': 0,
                'expired_quantity': 0
            }
    
    def _calculate_consumption_stats(self, ingredient_id, area_id=None):
        """计算消耗统计数据"""
        try:
            start_date = date.today() - timedelta(days=self.analysis_days)
            
            query = db.session.query(
                func.sum(ConsumptionDetail.actual_quantity).label('total_consumption'),
                func.count(ConsumptionDetail.id).label('consumption_days'),
                func.avg(ConsumptionDetail.actual_quantity).label('avg_daily_consumption'),
                func.max(ConsumptionDetail.actual_quantity).label('max_daily_consumption'),
                func.min(ConsumptionDetail.actual_quantity).label('min_daily_consumption')
            ).join(ConsumptionPlan).filter(
                and_(
                    ConsumptionDetail.ingredient_id == ingredient_id,
                    ConsumptionPlan.plan_date >= start_date,
                    ConsumptionDetail.actual_quantity > 0
                )
            )
            
            if area_id:
                query = query.filter(ConsumptionPlan.area_id == area_id)
            
            result = query.first()
            
            if result and result.total_consumption:
                # 计算消耗趋势
                trend = self._calculate_consumption_trend(ingredient_id, area_id)
                
                return {
                    'total_consumption': float(result.total_consumption),
                    'consumption_days': result.consumption_days,
                    'avg_daily_consumption': float(result.avg_daily_consumption),
                    'max_daily_consumption': float(result.max_daily_consumption),
                    'min_daily_consumption': float(result.min_daily_consumption),
                    'trend': trend,
                    'predicted_daily_consumption': float(result.avg_daily_consumption) * (1 + trend)
                }
            
            return {
                'total_consumption': 0,
                'consumption_days': 0,
                'avg_daily_consumption': 0,
                'max_daily_consumption': 0,
                'min_daily_consumption': 0,
                'trend': 0,
                'predicted_daily_consumption': 0
            }
            
        except Exception as e:
            logger.error(f"计算消耗统计失败: {str(e)}")
            return {}
    
    def _calculate_consumption_trend(self, ingredient_id, area_id=None):
        """计算消耗趋势"""
        try:
            # 比较最近30天和之前30天的消耗量
            today = date.today()
            recent_start = today - timedelta(days=30)
            previous_start = today - timedelta(days=60)
            
            # 最近30天消耗
            recent_query = db.session.query(
                func.avg(ConsumptionDetail.actual_quantity)
            ).join(ConsumptionPlan).filter(
                and_(
                    ConsumptionDetail.ingredient_id == ingredient_id,
                    ConsumptionPlan.plan_date >= recent_start,
                    ConsumptionDetail.actual_quantity > 0
                )
            )
            
            # 之前30天消耗
            previous_query = db.session.query(
                func.avg(ConsumptionDetail.actual_quantity)
            ).join(ConsumptionPlan).filter(
                and_(
                    ConsumptionDetail.ingredient_id == ingredient_id,
                    ConsumptionPlan.plan_date >= previous_start,
                    ConsumptionPlan.plan_date < recent_start,
                    ConsumptionDetail.actual_quantity > 0
                )
            )
            
            if area_id:
                recent_query = recent_query.filter(ConsumptionPlan.area_id == area_id)
                previous_query = previous_query.filter(ConsumptionPlan.area_id == area_id)
            
            recent_avg = recent_query.scalar() or 0
            previous_avg = previous_query.scalar() or 0
            
            if previous_avg > 0:
                trend = (recent_avg - previous_avg) / previous_avg
            else:
                trend = 0
            
            return min(max(trend, -0.5), 0.5)  # 限制趋势在-50%到+50%之间
            
        except Exception as e:
            logger.error(f"计算消耗趋势失败: {str(e)}")
            return 0
    
    def _calculate_expiry_rate(self, ingredient_id, area_id=None):
        """计算过期率"""
        try:
            start_date = date.today() - timedelta(days=self.analysis_days)
            
            # 获取总入库量
            total_in_query = db.session.query(
                func.sum(StockInItem.quantity)
            ).join(StockIn).filter(
                and_(
                    StockInItem.ingredient_id == ingredient_id,
                    StockIn.stock_in_date >= start_date,
                    StockIn.status == '已入库'
                )
            )
            
            # 获取过期处理量
            expired_query = db.session.query(
                func.sum(ExpiryHandling.quantity)
            ).join(Inventory).filter(
                and_(
                    Inventory.ingredient_id == ingredient_id,
                    ExpiryHandling.handling_type.in_(['报废', '过期处理']),
                    ExpiryHandling.handling_status == '已完成',
                    ExpiryHandling.created_at >= start_date
                )
            )
            
            if area_id:
                total_in_query = total_in_query.join(StockIn.warehouse).filter(
                    StockIn.warehouse.has(area_id=area_id)
                )
                expired_query = expired_query.filter(ExpiryHandling.area_id == area_id)
            
            total_in = total_in_query.scalar() or 0
            total_expired = expired_query.scalar() or 0
            
            if total_in > 0:
                expiry_rate = total_expired / total_in
            else:
                expiry_rate = 0
            
            return min(expiry_rate, 1.0)  # 最大100%
            
        except Exception as e:
            logger.error(f"计算过期率失败: {str(e)}")
            return 0
    
    def _calculate_purchase_suggestion(self, ingredient, current_stock, consumption_data, expiry_rate):
        """计算采购建议"""
        try:
            if consumption_data['predicted_daily_consumption'] <= 0:
                return None
            
            # 计算库存可用天数
            if current_stock['total_quantity'] > 0:
                stock_days = current_stock['total_quantity'] / consumption_data['predicted_daily_consumption']
            else:
                stock_days = 0
            
            # 计算建议采购量
            target_days = min(self.max_purchase_days, max(7, current_stock['avg_days_remaining'] * 0.8))
            safety_stock = consumption_data['predicted_daily_consumption'] * target_days * self.safety_stock_ratio
            
            # 考虑过期率调整
            expiry_adjustment = 1 - min(expiry_rate * 2, 0.5)  # 过期率高则减少采购量
            
            suggested_quantity = (
                consumption_data['predicted_daily_consumption'] * target_days + safety_stock
            ) * expiry_adjustment
            
            # 减去当前可用库存
            net_suggested_quantity = max(0, suggested_quantity - current_stock['total_quantity'])
            
            # 计算优先级分数
            priority_score = self._calculate_priority_score(
                stock_days, consumption_data, expiry_rate, current_stock
            )
            
            # 生成建议信息
            suggestion = {
                'suggested_quantity': round(net_suggested_quantity, 2),
                'target_days': target_days,
                'stock_days': round(stock_days, 1),
                'safety_stock': round(safety_stock, 2),
                'expiry_adjustment': round(expiry_adjustment, 3),
                'priority_score': priority_score,
                'urgency_level': self._get_urgency_level(stock_days, priority_score),
                'recommendation_reason': self._generate_recommendation_reason(
                    stock_days, consumption_data, expiry_rate, current_stock
                )
            }
            
            return suggestion
            
        except Exception as e:
            logger.error(f"计算采购建议失败: {str(e)}")
            return None
    
    def _calculate_priority_score(self, stock_days, consumption_data, expiry_rate, current_stock):
        """计算优先级分数"""
        score = 0
        
        # 库存天数评分（0-40分）
        if stock_days <= 3:
            score += 40
        elif stock_days <= 7:
            score += 30
        elif stock_days <= 15:
            score += 20
        elif stock_days <= 30:
            score += 10
        
        # 消耗趋势评分（0-20分）
        if consumption_data['trend'] > 0.2:
            score += 20
        elif consumption_data['trend'] > 0.1:
            score += 15
        elif consumption_data['trend'] > 0:
            score += 10
        
        # 过期率评分（0-20分，过期率低得分高）
        if expiry_rate <= 0.05:
            score += 20
        elif expiry_rate <= 0.1:
            score += 15
        elif expiry_rate <= 0.2:
            score += 10
        elif expiry_rate <= 0.3:
            score += 5
        
        # 库存状态评分（0-20分）
        if current_stock['total_quantity'] == 0:
            score += 20
        elif current_stock['expired_quantity'] > 0:
            score += 10
        elif current_stock['avg_days_remaining'] <= 7:
            score += 15
        
        return min(score, 100)
    
    def _get_urgency_level(self, stock_days, priority_score):
        """获取紧急程度"""
        if stock_days <= 1 or priority_score >= 80:
            return '紧急'
        elif stock_days <= 3 or priority_score >= 60:
            return '高'
        elif stock_days <= 7 or priority_score >= 40:
            return '中'
        else:
            return '低'
    
    def _generate_recommendation_reason(self, stock_days, consumption_data, expiry_rate, current_stock):
        """生成推荐原因"""
        reasons = []
        
        if current_stock['total_quantity'] == 0:
            reasons.append("当前库存为零")
        elif stock_days <= 3:
            reasons.append(f"库存仅够{stock_days:.1f}天使用")
        elif stock_days <= 7:
            reasons.append(f"库存偏低，仅够{stock_days:.1f}天使用")
        
        if consumption_data['trend'] > 0.1:
            reasons.append(f"消耗量呈上升趋势({consumption_data['trend']:.1%})")
        
        if expiry_rate > 0.2:
            reasons.append(f"历史过期率较高({expiry_rate:.1%})")
        
        if current_stock['expired_quantity'] > 0:
            reasons.append("存在过期库存")
        
        if not reasons:
            reasons.append("正常补货建议")
        
        return "；".join(reasons)

# 创建服务实例
smart_purchase_service = SmartPurchaseService()
